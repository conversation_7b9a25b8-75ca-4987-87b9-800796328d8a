---
to: website/components/_sections/<%= h.inflection.camelize( name, false ) %>/<%= h.inflection.camelize( name, false ) %>.stories.ts
---
import type { Meta, StoryObj } from '@storybook/react'
import <%= h.inflection.camelize( name, false ) %> from './<%= h.inflection.camelize( name, false ) %>'

const meta = {
  title: 'Components/<%= h.inflection.camelize( name, false ) %>',
  component: <%= h.inflection.camelize( name, false ) %>,
  parameters: {
    layout: 'centered',
  }
} satisfies Meta<typeof <%= h.inflection.camelize( name, false ) %>>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    // Put props here
  },
}