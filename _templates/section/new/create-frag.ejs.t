---
to: website/data/queries/sections/<%= h.inflection.camelize( name, true ) %>.ts
---
import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
`

export const fragment = (name = '<%= h.inflection.camelize( name, true ) %>') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
