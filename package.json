{"name": "177milkstreet", "version": "1.0.0", "private": true, "workspaces": ["website", "cms"], "scripts": {"build": "concurrently \"npm run build:website\" \"npm run build:cms\"", "build:cms": "npm run build --workspace=cms", "build:website": "npm run build --workspace=website", "dev": "concurrently \"npm run dev:website\" \"npm run dev:cms\"", "dev:cms": "npm run dev --workspace=cms", "dev:website": "npm run dev --workspace=website", "start": "concurrently \"npm run start:website\" \"npm run start:cms\"", "start:cms": "npm run start --workspace=cms", "start:website": "npm run start --workspace=website", "lint": "concurrently \"npm run lint:website\" \"npm run lint:cms\"", "lint:cms": "npm run lint --workspace=cms", "lint:website": "npm run lint --workspace=website"}, "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"hygen": "^6.2.11"}}