export const structure = (S: any) =>
  S.list()
    .title('Content')
    .items([
      // MAIN CONTENT TYPES
      S.listItem()
        .title('Recipes')
        .icon(() => '🍳')
        .child(
          S.list()
            .title('Recipes')
            .items([
              S.listItem()
                .title('All Recipes')
                .child(S.documentTypeList('recipe').title('All Recipes')),
              S.listItem()
                .title('Recipes by Category')
                .child(
                  S.documentTypeList('category')
                    .title('Recipes by Category')
                    .child((categoryId: string) =>
                      S.documentList()
                        .title('Recipes')
                        .filter('_type == "recipe" && $categoryId in categories[]._ref')
                        .params({categoryId}),
                    ),
                ),
              S.listItem()
                .title('Recipes by Author')
                .child(
                  S.documentTypeList('person')
                    .title('Recipes by Author')
                    .child((personId: string) =>
                      S.documentList()
                        .title('Recipes')
                        .filter('_type == "recipe" && person._ref == $personId')
                        .params({personId}),
                    ),
                ),
            ]),
        ),

      S.listItem()
        .title('Pages')
        .icon(() => '📄')
        .child(S.documentTypeList('page').title('Pages')),

      // SHARED SECTIONS
      S.divider(),

      S.listItem()
        .title('Collections')
        .icon(() => '📚')
        .child(
          S.list()
            .title('Collections')
            .items([
              S.listItem()
                .title('All Collections')
                .child(S.documentTypeList('collection').title('All Collections')),
              S.listItem()
                .title('Collections by Type')
                .child(
                  S.list()
                    .title('Collections by Type')
                    .items([
                      S.listItem()
                        .title('Recipe Collections')
                        .child(
                          S.documentList()
                            .title('Recipe Collections')
                            .filter('_type == "collection" && count(items[references($type)]) > 0')
                            .params({type: 'recipe'}),
                        ),
                      S.listItem()
                        .title('Article Collections')
                        .child(
                          S.documentList()
                            .title('Article Collections')
                            .filter('_type == "collection" && count(items[references($type)]) > 0')
                            .params({type: 'article'}),
                        ),
                      S.listItem()
                        .title('Class Collections')
                        .child(
                          S.documentList()
                            .title('Class Collections')
                            .filter('_type == "collection" && count(items[references($type)]) > 0')
                            .params({type: 'class'}),
                        ),
                      S.listItem()
                        .title('Product Collections')
                        .child(
                          S.documentList()
                            .title('Product Collections')
                            .filter('_type == "collection" && count(items[references($type)]) > 0')
                            .params({type: 'product'}),
                        ),
                    ]),
                ),
            ]),
        ),

      S.listItem()
        .title('Shared Sections')
        .icon(() => '🔄')
        .child(
          S.list()
            .title('Shared Sections')
            .items([
              S.listItem()
                .title('All Sections')
                .child(S.documentTypeList('sharedSection').title('All Shared Sections')),
              S.listItem()
                .title('Sections by Type')
                .child(
                  S.list()
                    .title('Sections by Type')
                    .items([
                      S.listItem()
                        .title('Rich Text Sections')
                        .child(
                          S.documentList()
                            .title('Rich Text Sections')
                            .filter(
                              '_type == "sharedSection" && section.type == "richTextSection"',
                            ),
                        ),
                    ]),
                ),
            ]),
        ),

      // CATEGORIES & SUBCATEGORIES
      S.divider(),
      S.listItem()
        .title('Categories')
        .icon(() => '🏷️')
        .child(S.documentTypeList('category').title('Categories')),
      S.listItem()
        .title('Subcategories')
        .icon(() => '🏷️')
        .child(S.documentTypeList('subcategory').title('Subcategories')),

      // PAGE SECTIONS EXAMPLES
      S.divider(),
      S.listItem()
        .title('Page Sections Examples')
        .icon(() => '🫟')
        .child(S.documentTypeList('pageSectionExample').title('Page Sections Examples')),

      // MEDIA CONTENT
      S.divider(),
      S.listItem()
        .title('TV Episodes')
        .icon(() => '📺')
        .child(
          S.list()
            .title('TV Episodes')
            .items([
              S.listItem()
                .title('All TV Episodes')
                .child(S.documentTypeList('tvEpisode').title('All TV Episodes')),
              S.listItem()
                .title('TV Episodes by Season')
                .child(
                  S.documentList()
                    .title('TV Episodes by Season')
                    .filter('_type == "tvEpisode"')
                    .params({})
                    .menuItems([])
                    .initialValueTemplates([])
                    .child((seasonNumber: number) =>
                      S.documentList()
                        .title(`Season ${seasonNumber}`)
                        .filter('_type == "tvEpisode" && seasonNumber == $seasonNumber')
                        .params({seasonNumber}),
                    ),
                ),
            ]),
        ),

      S.listItem()
        .title('Radio Episodes')
        .icon(() => '🎙️')
        .child(S.documentTypeList('radioEpisode').title('Radio Episodes')),

      S.listItem()
        .title('Magazine Issues')
        .icon(() => '📝')
        .child(S.documentTypeList('magazineIssue').title('All Magazine Issues')),

      S.listItem()
        .title('Articles')
        .icon(() => '📝')
        .child(S.documentTypeList('article').title('All Articles')),

      // PRODUCTS & CLASSES
      S.divider(),
      S.listItem()
        .title('Shopify')
        .icon(() => '🏷️')
        .child(
          S.list()
            .title('Shopify')
            .items([
              S.listItem()
                .title('Products')
                .child(S.documentTypeList('shopifyProduct').title('All Products')),
              S.listItem()
                .title('Collections')
                .child(S.documentTypeList('shopifyCollection').title('All Collections')),
              S.listItem()
                .title('Product Variants')
                .child(S.documentTypeList('shopifyProductVariant').title('All Product Variants')),
            ]),
        ),
      // Cooking Classes
      S.listItem()
        .title('Cooking Classes')
        .icon(() => '👨‍🍳')
        .child(
          S.list()
            .title('Cooking Classes')
            .items([
              S.listItem()
                .title('All Classes')
                .child(S.documentTypeList('class').title('All Classes')),
              S.listItem()
                .title('Upcoming Classes')
                .child(
                  S.documentList()
                    .title('Upcoming Classes')
                    .filter('_type == "class" && date >= now()'),
                ),
              S.listItem()
                .title('Past Classes')
                .child(
                  S.documentList().title('Past Classes').filter('_type == "class" && date < now()'),
                ),
            ]),
        ),

      // People
      S.divider(),
      S.listItem()
        .title('People')
        .icon(() => '👤')
        .child(S.documentTypeList('person').title('People')),

      // SITE CONFIGURATION
      S.divider(),
      S.listItem()
        .title('Site Configuration')
        .icon(() => '⚙️')
        .child(S.document().schemaType('siteSettings').documentId('siteSettings')),
    ])
