import {defineConfig, PluginOptions} from 'sanity'
import {structureTool} from 'sanity/structure'
import {visionTool} from '@sanity/vision'
import {schemaTypes} from './schemaTypes'
import {structure} from './deskStructure'

import {colorInput} from '@sanity/color-input'
import {imageHotspotArrayPlugin} from 'sanity-plugin-hotspot-array'
import {
  media,
  //  mediaAssetSource
} from 'sanity-plugin-media'
import {customDocumentActions} from './schemaTypes/shopify/plugins/customDocumentActions'

export default defineConfig({
  name: process.env.SANITY_STUDIO_TITLE,
  title: process.env.SANITY_STUDIO_TITLE,
  projectId: process.env.SANITY_STUDIO_PROJECT_ID as string,
  dataset: process.env.SANITY_STUDIO_DATASET as string,
  plugins: [
    structureTool({
      structure,
    }),
    visionTool(),
    media(),

    colorInput(),
    imageHotspotArrayPlugin(),
    customDocumentActions(),
  ] as PluginOptions[],
  schema: {
    types: schemaTypes as any,
  },
})
