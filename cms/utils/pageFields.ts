export const getMetaFieldsForPage = () => {
  return [
    {
      name: 'metaTitle',
      title: 'Meta Title',
      type: 'string',
      description: 'Custom title for SEO (defaults to episode title if not set)',
      group: 'seo',
    },
    {
      name: 'metaDescription',
      title: 'Meta Description',
      type: 'text',
      description: 'Custom description for SEO',
      group: 'seo',
    },
    {
      name: 'openGraphImage',
      title: 'Social Share Image',
      type: 'imageAsset',
      description:
        'Image to display when shared on social media (defaults to main image if not set)',
      options: {
        hotspot: true,
      },
      group: 'seo',
    },
    {
      name: 'keywords',
      title: 'Keywords',
      type: 'array',
      description: 'Keywords for search engines',
      of: [{type: 'string'}],
      group: 'seo',
    },
    {
      name: 'noIndex',
      title: 'No Index',
      type: 'boolean',
      description: 'Hide this page from search engines',
      initialValue: false,
      group: 'seo',
    },
  ]
}
