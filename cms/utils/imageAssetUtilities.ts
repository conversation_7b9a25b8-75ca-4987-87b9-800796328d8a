import {Rule, ValidationContext} from 'sanity'

export const decodeAssetId = (
  id: string,
): {dimensions: {width: number; height: number}} | {dimensions: null} => {
  const pattern = /^image-([a-f\d]+)-(\d+x\d+)-(\w+)$/
  const imageContents = pattern.exec(id)
  if (!imageContents) return {dimensions: null}
  const [width, height] = imageContents[2].split('x').map((v: any) => parseInt(v, 10))

  return {
    dimensions: {width, height},
  }
}

export type RequiredImageDimensionsParamsType = {
  width?: number
  height?: number
  required?: boolean
  dimensionVariance?: number
  addedValidation?: (
    context: ValidationContext,
    dimensions?: {width: number; height: number},
  ) => string | boolean | null | undefined
}

export type RequiredImageDimensionsType = RequiredImageDimensionsParamsType & {
  Rule: Rule
}

export const requiredImageDimensions = ({
  width,
  height,
  Rule,
  required,
  addedValidation,
  dimensionVariance = 10, // if image uploaded is within this variance of width or height, it will be accepted
}: RequiredImageDimensionsType) => {
  let validation = Rule

  return validation.custom((image: {asset: {_ref: string}}, context) => {
    const {dimensions} = decodeAssetId(image?.asset?._ref)

    if (addedValidation) {
      const value = addedValidation(context, dimensions || {width: 0, height: 0})
      if (value) return value
    }

    if (!image && required) return 'Image is required.'
    if (!image && !required) return true

    if (!dimensions) return 'There has been an error with your image. Please try again.'

    let isCorrectDimensions = false
    let imageShouldBeText = ''

    if (width && !height) {
      isCorrectDimensions = Math.abs(dimensions.width - width) <= dimensionVariance
      imageShouldBeText = `${width}px in width.`
    } else if (height && !width) {
      isCorrectDimensions = Math.abs(dimensions.height - height) <= dimensionVariance
      imageShouldBeText = `${height}px in height.`
    } else if (width && height) {
      isCorrectDimensions =
        Math.abs(dimensions.width - width) <= dimensionVariance &&
        Math.abs(dimensions.height - height) <= dimensionVariance
      imageShouldBeText = `${width}x${height}.`
    } else if (!width && !height) {
      isCorrectDimensions = true
    }

    const error = `Incorrect dimensions. Current image is ${dimensions.width}x${dimensions.height}. Image should be ${imageShouldBeText}`

    return isCorrectDimensions || error
  })
}
