// Documents
import recipe from './documents/recipe'
import article from './documents/article'
import person from './documents/person'
import category from './documents/category'
import subcategory from './documents/subcategory'
import page from './documents/page'
import product from './documents/product'
import tvEpisode from './documents/tvEpisode'
import radioEpisode from './documents/radioEpisode'
import class_ from './documents/class'
import collection from './documents/collection'
import sharedSection from './documents/sharedSection'
import pageSectionExample from './documents/pageSectionExample'
import magazineIssue from './documents/magazineIssue'

// Shopify
import {shopifySchemaTypes} from './shopify/schemaTypes'

// Site Settings
import siteSettings from './documents/siteSettings'

// Components
import link from './objects/link'
import button from './objects/button'
import imageAsset from './objects/imageAsset'
import cmsSettings from './objects/cmsSettings'
import video from './objects/video'
import displayedCardFields from './objects/displayedCardFields'
import cardStyle from './objects/cardStyle'
import forumCard from './objects/forumCard'
import cardItem from './objects/cardItem'
import cardItems from './objects/cardItems'
import subnavTitleImage from './objects/subnavTitleImage'
import tip from './objects/tip'
import recipeComponents from './objects/recipeComponents'
import contentBlocks from './objects/contentBlocks'
import sidebar from './objects/sidebar'
import adObject from './objects/adObject'
import titleTextAndCta from './objects/titleTextAndCta'
import eyebrowWithLineItems from './objects/eyebrowWithLineItems'
import subscriptionPlanCard from './objects/subscriptionPlanCard'

// Sections
import {sectionTypes} from './sections'

export const schemaTypes = [
  // Components
  cmsSettings,
  link.link,
  link.linkNoLabel,
  link.linkNoDisabled,
  link.linkNoDisabledNoLabel,
  imageAsset.imageAsset,
  imageAsset.imageAssetNoAlt,
  video,
  displayedCardFields,
  cardStyle,
  forumCard,
  cardItem,
  cardItems,
  subnavTitleImage,
  tip,
  recipeComponents.recipeIngredient,
  recipeComponents.recipeIngredientSectionHeading,
  recipeComponents.recipeDirection,
  recipeComponents.recipeDirectionFastSlow,
  contentBlocks.contentBlockStepByStep,
  contentBlocks.contentBlockText,
  contentBlocks.contentBlockVideo,
  button,
  sidebar,
  adObject,
  titleTextAndCta,
  eyebrowWithLineItems,
  subscriptionPlanCard,
  // Section types for page content
  ...sectionTypes,

  // Doc types
  recipe,
  article,
  tvEpisode,
  radioEpisode,
  product,
  class_,
  page,
  collection,
  sharedSection,
  person,
  category,
  subcategory,
  pageSectionExample,
  magazineIssue,

  // Site configuration
  siteSettings,

  // Shopify
  ...shopifySchemaTypes,
]
