//       anchorTag -> blockId
//       label -> eyebrow
//       headline
//       headline2
//       images
//       text

import {Rule} from 'sanity'
import getRichTextFields from '../../utils/richText'

const contentBlockStepByStep = {
  name: 'contentBlockStepByStep',
  type: 'object',
  title: 'Step by Step',
  fields: [
    {
      name: 'blockId',
      type: 'string',
      title: 'Block ID',
      description: 'Used to identify the block in the code. Default is "steps"',
    },
    {
      name: 'eyebrow',
      type: 'string',
      title: 'Eyebrow',
      description: 'Shows up above the headline',
    },
    {
      name: 'headline',
      type: 'string',
      title: 'Headline',
    },
    {
      name: 'headline2',
      type: 'string',
      title: 'Headline 2',
    },
    {
      name: 'images',
      type: 'array',
      title: 'Images',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'image',
              type: 'imageAsset',
              title: 'Image',
              validation: (Rule: Rule) => Rule.required(),
            },
            {
              name: 'caption',
              type: 'string',
              title: 'Caption',
            },
          ],
        },
      ],
      validation: (Rule: Rule) => Rule.required().min(1),
    },
    {
      title: 'Text',
      name: 'text',
      type: 'array',
      validation: (Rule: any) => Rule.required(),
      of: [getRichTextFields({items: ['link', 'strong']})],
    },
  ],
}

const contentBlockText = {
  name: 'contentBlockText',
  type: 'object',
  title: 'Text',
  fields: [
    {
      name: 'blockId',
      type: 'string',
      title: 'Block ID',
      description: 'Used to identify the block in the code. Default is "video"',
    },
    {
      title: 'Text',
      name: 'text',
      type: 'array',
      validation: (Rule: any) => Rule.required(),
      of: [getRichTextFields({items: ['link', 'strong']})],
    },
  ],
}

const contentBlockVideo = {
  name: 'contentBlockVideo',
  type: 'object',
  title: 'Video',
  fields: [
    {
      name: 'blockId',
      type: 'string',
      title: 'Block ID',
      description: 'Used to identify the block in the code. Default is "video"',
    },
    {
      name: 'video',
      type: 'video',
      title: 'Video',
    },
    {
      title: 'Text',
      name: 'text',
      type: 'array',
      validation: (Rule: any) => Rule.required(),
      of: [getRichTextFields({items: ['link', 'strong']})],
    },
    // videoPosition not needed because it's a video block
  ],
}

const moduleExports = {
  contentBlockStepByStep,
  contentBlockText,
  contentBlockVideo,
}

export default moduleExports
