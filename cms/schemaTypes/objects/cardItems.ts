import {Rule} from 'sanity'
import {referenceableCardTypes} from '../../utils/referenceableCardTypes'

export default {
  name: 'cardItems',
  title: 'Card Items',
  type: 'object',
  preview: {
    select: {
      type: 'type',
      references: 'references',
      collection: 'collection',
      collectionTitle: 'collection.title',
    },
    prepare({
      type,
      references,
      collection,
      collectionTitle,
    }: {
      type: string
      references: any
      collection: any
      collectionTitle: string
    }) {
      let title = ''
      if (references?.length > 0 && type === 'multiReference') {
        title = `Multi-Reference (${references?.length} items)`
      } else if (collection && type === 'collection') {
        title = `Collection ("${collectionTitle}")`
      }

      return {
        title,
      }
    },
  },
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'type',
      title: 'Type',
      type: 'string',
      initialValue: 'multiReference',
      options: {
        list: [
          {title: 'Multi-Reference', value: 'multiReference'},
          {title: 'Collection', value: 'collection'},
        ],
        layout: 'radio',
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'references',
      title: 'References',
      type: 'array',
      validation: (Rule: Rule) =>
        Rule.custom((value: any, context: any) => {
          if (context.parent?.type === 'multiReference' && !value?.length) {
            return 'Minimum 1 item required'
          }
          return true
        }),
      hidden: ({parent}: any) => parent?.type !== 'multiReference',
      of: [
        {
          type: 'reference',
          to: referenceableCardTypes.map((type) => ({type})),
        },
      ],
    },
    {
      name: 'collection',
      title: 'Collection',
      type: 'reference',
      to: {type: 'collection'},
      validation: (Rule: Rule) =>
        Rule.custom((value: any, context: any) => {
          if (context.parent?.type === 'collection' && !value) {
            return 'This field is required'
          }
          return true
        }),
      hidden: ({parent}: any) => parent?.type !== 'collection',
    },
    {
      name: 'capItems',
      title: 'Cap Items',
      type: 'number',
      initialValue: 15,
      description: 'The maximum number of items to display. Max is 15.',
      validation: (Rule: Rule) => Rule.min(1).max(15),
      hidden: ({parent}: any) => parent?.type !== 'collection',
    },
    {
      name: 'displayedCardFields',
      title: 'Displayed Card Fields',
      type: 'displayedCardFields',
    },
    {
      name: 'cardStyle',
      title: 'Card Style',
      type: 'cardStyle',
    },
  ],
}
