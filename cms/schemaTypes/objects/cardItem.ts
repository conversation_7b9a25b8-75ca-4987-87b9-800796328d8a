import {Rule} from 'sanity'
import {referenceableCardTypes} from '../../utils/referenceableCardTypes'

export default {
  name: 'cardItem',
  title: 'Card Item',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'type',
      title: 'Card Type',
      type: 'string',
      initialValue: 'reference',
      options: {
        list: [
          {title: 'Reference', value: 'reference'},
          {title: 'Forum Card', value: 'forumCard'},
          {title: 'Custom Card', value: 'customCard'},
        ],
        layout: 'radio',
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    /*
    //
    //  Custom Card fields
    //
    */
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      hidden: ({parent, value}: any) => {
        const isInvalid = parent?.type === 'customCard' && !value
        return parent?.type !== 'customCard' && !isInvalid
      },
      validation: (Rule: Rule) =>
        Rule.custom((value, context: any) => {
          if (context.parent?.type === 'customCard' && !value) {
            return 'Title is required for custom cards'
          }
          return true
        }),
    },
    {
      name: 'image',
      title: 'Image',
      type: 'imageAsset',
      hidden: ({parent}: any) => parent?.type !== 'customCard',
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 3,
      hidden: ({parent}: any) => parent?.type !== 'customCard',
    },
    {
      name: 'link',
      title: 'Link',
      type: 'linkNoLabel',
      initialValue: {
        linkType: 'disabled',
      },
      hidden: ({parent}: any) => parent?.type !== 'customCard',
    },
    {
      name: 'tag',
      title: 'Tag',
      type: 'string',
      hidden: ({parent}: any) => parent?.type !== 'customCard',
    },
    {
      name: 'customCardStyle',
      title: 'Card Style',
      type: 'cardStyle',
      hidden: ({parent}: any) => parent?.type !== 'customCard',
    },
    /*
    //
    //  Custom Card fields
    //
    */
    {
      type: 'forumCard',
      title: 'Forum Card',
      name: 'forumCard',
      hidden: ({parent}: any) => parent?.type !== 'forumCard',
      options: {
        collapsible: true,
        collapsed: false,
      },
    },

    /*
    //
    //  Custom Card fields
    //
    */
    {
      name: 'reference',
      title: 'Content Reference',
      type: 'reference',
      to: referenceableCardTypes.map((type) => ({type})),
      hidden: ({parent}: any) => parent?.type !== 'reference',
      validation: (Rule: Rule) =>
        Rule.custom((value, context: any) => {
          if (context.parent?.type === 'reference' && !value) {
            return 'Reference is required for reference cards'
          }
          return true
        }),
    },
    {
      name: 'referenceDisplayFields',
      title: 'Display Fields',
      type: 'displayedCardFields',
      hidden: ({parent}: any) => parent?.type !== 'reference',
    },
    {
      name: 'referenceCardStyle',
      title: 'Card Style',
      type: 'cardStyle',
      hidden: ({parent}: any) => parent?.type !== 'reference',
    },
  ],
  preview: {
    select: {
      type: 'type',
      customTitle: 'title',
      reference: 'reference.title',
      forumTag: 'forumTag',
      image: 'image',
      referenceImage: 'reference.mainImage',
    },
    prepare(selection: any) {
      const {type, customTitle, reference, forumTag, image, referenceImage} = selection

      let title = 'Card Item'
      let subtitle = ''
      let media = image || referenceImage

      if (type === 'customCard') {
        title = customTitle || 'Custom Card'
        subtitle = 'Custom Card'
      } else if (type === 'forumCard') {
        title = forumTag || 'Forum Card'
        subtitle = 'Forum Card'
      } else if (type === 'reference') {
        title = reference || 'Referenced Content'
        subtitle = 'Reference'
      }

      return {
        title,
        subtitle,
        media,
      }
    },
  },
}
