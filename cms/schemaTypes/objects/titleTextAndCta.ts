import {Rule} from 'sanity'

export default {
  name: 'titleTextAndCta',
  title: 'Title, Text and CTA',
  type: 'object',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
    },
    {
      name: 'cta',
      title: 'CTA',
      type: 'link',
    },
  ],
  preview: {
    select: {
      title: 'title',
    },
    prepare({title}: {title?: string}) {
      return {
        title: title || 'Title, Text and CTA',
        subtitle: 'Title, Text and CTA',
      }
    },
  },
}
