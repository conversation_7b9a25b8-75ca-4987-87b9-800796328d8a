import {Rule} from 'sanity'

export default {
  name: 'forumCard',
  type: 'object',
  title: 'Forum Card',
  fields: [
    {
      name: 'tag',
      type: 'string',
      title: 'Tag',
    },
    {
      name: 'links',
      type: 'array',
      title: 'Links',
      of: [{type: 'link'}],
    },
    {
      name: 'cta',
      type: 'link',
      title: 'Call to Action',
      initialValue: {
        linkType: 'disabled',
      },
    },
  ],
}
