import {Rule} from 'sanity'

export default {
  name: 'adObject',
  title: 'Ad Object',
  type: 'object',
  options: {
    collapsible: true,
  },
  fields: [
    {
      name: 'type',
      title: 'Ad Type',
      type: 'string',
      options: {
        list: [
          {title: 'Skyscraper', value: 'skyscraper'},
          {title: 'Billboard', value: 'billboard'},
        ],
        layout: 'radio',
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'id',
      title: 'HTML ID',
      type: 'string',
      description: 'An HTML id for the element it will live in',
      validation: (Rule: Rule) => Rule.required(),
    },
  ],
  preview: {
    select: {
      type: 'type',
      id: 'id',
    },
    prepare({type, id}: {type: string; id: string}) {
      return {
        title: `Ad: ${type}`,
        subtitle: `ID: ${id}`,
      }
    },
  },
}
