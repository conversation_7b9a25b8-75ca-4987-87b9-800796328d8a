import {toPlainText} from '@portabletext/react'
import getRichTextFields, {portableTextToString} from '../../utils/richText'

const recipeIngredientSectionHeading = {
  name: 'recipeIngredientSectionHeading',
  type: 'object',
  title: 'Section Heading',
  fields: [
    {
      name: 'label',
      type: 'string',
      title: 'Label',
    },
  ],
  preview: {
    select: {
      title: 'label',
    },
  },
}

const recipeIngredient = {
  name: 'recipeIngredient',
  type: 'object',
  title: 'Ingredient',
  fields: [
    {
      name: 'amount',
      type: 'string',
      title: 'Amount',
    },
    {
      title: 'Description',
      name: 'description',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong', 'italic']})],
    },
  ],
  preview: {
    select: {
      title: 'amount',
      description: 'description',
    },
    prepare(selection: any) {
      return {
        title: `${selection?.title ? selection?.title : ''} ${toPlainText(selection?.description)}`,
      }
    },
  },
}

const recipeDirection = {
  name: 'recipeDirection',
  type: 'object',
  title: 'Direction',
  fields: [
    {
      name: 'text',
      title: 'Text',
      type: 'array',
      validation: (Rule: any) => Rule.required(),
      of: [getRichTextFields({items: ['link', 'strong', 'italic']})],
    },
  ],
  preview: {
    select: {
      title: 'text',
    },
    prepare(selection: any) {
      return {
        title: portableTextToString(selection?.title),
      }
    },
  },
}

const recipeDirectionFastSlow = {
  name: 'recipeDirectionFastSlow',
  type: 'object',
  title: 'Fast/Slow Directions',
  fields: [
    {
      name: 'fastDirectionText',
      title: 'Fast Direction Text',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong', 'italic']})],
    },
    {
      name: 'slowDirectionText',
      title: 'Slow Direction Text',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong', 'italic']})],
    },
  ],
  preview: {
    select: {
      title: 'fastDirectionText',
    },
    prepare(selection: any) {
      return {
        title: portableTextToString(selection?.title),
      }
    },
  },
}

const exports = {
  recipeIngredientSectionHeading,
  recipeIngredient,
  recipeDirection,
  recipeDirectionFastSlow,
}

export default exports
