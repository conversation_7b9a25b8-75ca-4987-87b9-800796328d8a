import {Rule} from 'sanity'

export default {
  name: 'subscriptionPlanCard',
  title: 'Subscription Plan Card',
  type: 'object',
  fields: [
    {
      name: 'image',
      title: 'Image',
      type: 'imageAsset',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'subtitle',
      title: 'Subtitle',
      type: 'string',
    },
    {
      name: 'cost',
      title: 'Cost',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'cta',
      title: 'Call to Action',
      type: 'link',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'items',
      title: 'Feature Items',
      type: 'array',
      of: [{type: 'string'}],
      validation: (Rule: Rule) => Rule.required(),
    },
  ],
  preview: {
    select: {
      title: 'title',
      subtitle: 'subtitle',
      media: 'image.asset',
    },
  },
}
