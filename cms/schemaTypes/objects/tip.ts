import {Rule} from 'sanity'
import getRichTextFields from '../../utils/richText'

export default {
  name: 'tip',
  title: 'Tip',
  type: 'object',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong']})],
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'alignment',
      title: 'Alignment',
      type: 'string',
      options: {
        list: [
          {title: 'Left', value: 'left'},
          {title: 'Center', value: 'center'},
        ],
        layout: 'radio',
      },
      initialValue: 'left',
    },
    {
      name: 'backgroundColor',
      title: 'Background Color',
      type: 'string',
      options: {
        list: [
          {title: 'White', value: 'white'},
          {title: 'Yellow', value: 'yellow'},
        ],
        layout: 'radio',
      },
      initialValue: 'white',
    },
    {
      name: 'textSize',
      title: 'Text Size',
      type: 'string',
      options: {
        list: [
          {title: 'Medium', value: 'md'},
          {title: 'Large', value: 'lg'},
        ],
        layout: 'radio',
      },
      initialValue: 'md',
    },
  ],
  preview: {
    select: {
      title: 'title',
    },
  },
}
