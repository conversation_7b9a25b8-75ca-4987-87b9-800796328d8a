import {Asset} from 'sanity'

const imageAsset = {
  title: 'Image Asset',
  name: 'imageAsset',
  type: 'image',
  options: {
    hotspot: true,
  },
  fields: [
    {
      title: 'Alt text',
      name: 'alt',
      type: 'string',
    },
  ],
  preview: {
    select: {
      asset: 'asset',
      alt: 'alt',
    },
    prepare({asset, alt}: {asset: Asset; alt?: string}) {
      return {
        title: alt || '(alt text missing)',
        media: asset,
      }
    },
  },
}

const imageAssetNoAlt = {
  title: 'Image Asset',
  name: 'imageAssetNoAlt',
  type: 'image',
  options: {
    hotspot: true,
  },
  preview: {
    select: {
      asset: 'asset',
      alt: 'alt',
    },
    prepare({asset, alt}: {asset: Asset; alt?: string}) {
      return {
        title: alt || '(alt text missing)',
        media: asset,
      }
    },
  },
}

const exports = {imageAsset, imageAssetNoAlt}

export default exports
