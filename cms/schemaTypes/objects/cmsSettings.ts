import {Rule} from 'sanity'

export default {
  name: 'cmsSettings',
  title: 'cms Settings',
  type: 'object',
  options: {
    collapsible: true,
    //collapsed: true,
  },
  fields: [
    {
      name: 'isHidden',
      title: 'Is Hidden',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'cmsTitle',
      title: 'CMS Title',
      description: 'For CMS lookups and referencing',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'id',
      title: 'ID',
      description:
        'The ID that shows up on the html element. Only use lowercase and dashes to separate words.',
      type: 'string',
    },
  ],
}
