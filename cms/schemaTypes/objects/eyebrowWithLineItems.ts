import {Rule} from 'sanity'

export default {
  name: 'eyebrowWithLineItems',
  title: 'Eyebrow With Line Items',
  type: 'object',
  fields: [
    {
      name: 'eyebrow',
      title: 'Eyebrow',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'items',
      title: 'Items',
      type: 'array',
      of: [{type: 'string'}],
      validation: (Rule: Rule) => Rule.required(),
    },
  ],
  preview: {
    select: {
      title: 'eyebrow',
      items: 'items',
    },
  },
}
