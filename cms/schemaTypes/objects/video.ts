import {ConditionalPropertyCallbackContext, Rule} from 'sanity'
import {requiredImageDimensions} from '../../utils/imageAssetUtilities'

export default {
  name: 'video',
  title: 'Video',
  type: 'object',
  options: {
    collapsible: true,
    //collapsed: true,
  },
  fields: [
    {
      name: 'type',
      title: 'Type',
      type: 'string',
      initialValue: 'youtube',
      options: {
        list: [
          {title: 'Youtube', value: 'youtube'},
          {title: 'Vimeo', value: 'vimeo'},
          {title: 'URL', value: 'url'},
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'youtubeId',
      title: 'Youtube ID',
      description: 'Video must be publicly',
      hidden: ({parent}: ConditionalPropertyCallbackContext) => parent?.type !== 'youtube',
      type: 'string',
      validation: (Rule: any) =>
        Rule.custom((value: string, context: any) => {
          if (context.parent?.type === 'youtube' && !value) {
            return 'This field is required'
          }
          return true
        }),
    },
    {
      name: 'vimeoId',
      title: 'Vimeo ID',
      description: 'Video must be publicly',
      hidden: ({parent}: ConditionalPropertyCallbackContext) => parent?.type !== 'vimeo',
      type: 'string',
      validation: (Rule: any) =>
        Rule.custom((value: string, context: any) => {
          if (context.parent?.type === 'vimeo' && !value) {
            return 'This field is required'
          }
          return true
        }),
    },
    {
      name: 'url',
      title: 'Video URL',
      description: 'Recommended file type: MP4',
      hidden: ({parent}: ConditionalPropertyCallbackContext) => parent?.type !== 'url',
      type: 'string',
      validation: (Rule: any) =>
        Rule.custom((value: string, context: any) => {
          if (context.parent?.type === 'url' && !value) {
            return 'This field is required'
          }
          return true
        }),
    },
    {
      name: 'aspectRatio',
      title: 'Aspect Ratio',
      description: 'Format: width:height. Example: 16:9. Defaults to 16:9.',
      type: 'string',
    },
    {
      type: 'imageAsset',
      name: 'previewImage',
      title: 'Preview Image',
      description: 'Required dimensions: 2500px in width.',
      validation: (Rule: Rule) => requiredImageDimensions({Rule, required: false, width: 2500}),
    },
  ],
}
