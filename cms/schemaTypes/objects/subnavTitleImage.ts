import {Rule} from 'sanity'

export default {
  name: 'subnavTitleImage',
  title: 'Subnav Title Image',
  type: 'object',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'image',
      title: 'Image',
      type: 'imageAsset',
      description: 'Image shows up to left of title',
    },
    {
      name: 'desktopWidth',
      title: 'Desktop Width',
      type: 'number',
      initialValue: 80,
      description: 'Width of the image on desktop',
    },
    {
      name: 'mobileWidth',
      title: 'Mobile Width',
      type: 'number',
      initialValue: 50,
      description: 'Width of the image on mobile',
    },
  ],
}
