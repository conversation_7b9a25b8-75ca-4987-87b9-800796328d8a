// id: '1254201',
// uid: '47c3889e-7303-4134-9a6d-e487db8d9fe2',
// dateCreated: '2022-12-14T17:51:45+00:00',
// dateUpdated: '2022-12-14T17:51:45+00:00',
// title: 'Shrimp',
// slug: 'shrimp',
// groupId: 9,
// groupHandle: 'ingredientscategory'

export default {
  name: 'subcategory',
  title: 'Subcategory',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'parentCategory',
      title: 'Parent Category',
      type: 'reference',
      to: [{type: 'category'}],
    },
    {
      name: 'craftData',
      title: 'Craft Data',
      type: 'object',
      options: {
        collapsible: true,
        collapsed: true,
      },
      fields: [
        {
          name: 'id',
          title: 'ID',
          type: 'text',
          rows: 1,
          readOnly: true,
        },
        {
          name: 'groupId',
          title: 'Group ID',
          type: 'number',
          readOnly: true,
        },
        {
          name: 'groupHandle',
          title: 'Group Handle',
          type: 'string',
          readOnly: true,
        },
        {
          name: 'importedFromCraft',
          title: 'Imported From Craft',
          type: 'boolean',
          initialValue: false,
          readOnly: true,
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
      subtitle: 'description',
    },
  },
}
