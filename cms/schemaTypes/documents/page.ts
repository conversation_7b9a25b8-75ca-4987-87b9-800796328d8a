import {getMetaFieldsForPage} from '../../utils/pageFields'
import pageSectionTypes from '../../utils/pageSectionTypes'

export default {
  name: 'page',
  title: 'Page',
  type: 'document',
  groups: [
    {
      name: 'basics',
      title: 'Basic Information',
      default: true,
    },
    {
      name: 'content',
      title: 'Page Content',
    },
    {
      name: 'seo',
      title: 'SEO & Social',
    },
  ],
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) =>
        Rule.required().custom(async (slug: any, context: any) => {
          if (!slug) return true // Skip validation if no slug provided
          const {current} = slug
          const {document, getClient} = context

          // Get a client with an explicit API version
          const client = getClient({apiVersion: '2024-03-13'})

          // Get both the draft and published ID
          const baseId = document._id.replace(/^drafts\./, '')
          const draftId = `drafts.${baseId}`

          const params = {
            slug: current,
            baseId,
            draftId,
          }

          // Check for any document (excluding both draft and published versions of current doc)
          const query = `*[
          _type == "page" && 
          slug.current == $slug && 
          !(_id in [$baseId, $draftId])
        ][0]`

          const existingPage = await client.fetch(query, params)
          return existingPage ? 'This slug is already in use' : true
        }),
      group: 'basics',
    },
    {
      name: 'authors',
      title: 'Authors',
      type: 'array',
      of: [{type: 'reference', to: {type: 'person'}}],
      group: 'basics',
    },
    {
      name: 'expiryDate',
      title: 'Expiry Date',
      description: 'Optional date when this content should no longer be displayed',
      type: 'datetime',
      group: 'basics',
    },
    {
      name: 'content',
      title: 'Page Content',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'sharedSection'}],
          title: 'Shared Section',
        },
        ...pageSectionTypes.map((type) => ({
          type,
        })),
      ],
      group: 'content',
    },
    // SEO Fields
    ...getMetaFieldsForPage(),
  ],
  preview: {
    select: {
      title: 'title',
      slug: 'slug.current',
      media: 'heroImage',
      createdAt: '_createdAt',
    },
    prepare({
      title,
      slug,
      media,
      createdAt,
    }: {
      title: string
      slug: string
      media: any
      createdAt: string
    }) {
      return {
        title,
        subtitle: `/${slug}${createdAt ? ` • ${new Date(createdAt).toLocaleDateString()}` : ''}`,
        media,
      }
    },
  },
}
