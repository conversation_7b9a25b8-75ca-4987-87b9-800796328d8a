import getRichTextFields from '../../utils/richText'

export default {
  name: 'class',
  title: 'Cooking Class',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'mainImage',
      title: 'Main Image',
      type: 'imageAsset',
      options: {
        hotspot: true,
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'date',
      title: 'Class Date',
      type: 'datetime',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'endDate',
      title: 'End Date/Time',
      type: 'datetime',
      description: 'When the class ends',
    },
    {
      name: 'location',
      title: 'Location',
      type: 'string',
      description: 'Where the class will be held (physical location or "online")',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'instructor',
      title: 'Instructor',
      type: 'reference',
      to: {type: 'person'},
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong']})],
    },
    {
      name: 'price',
      title: 'Price',
      type: 'number',
      validation: (Rule: any) => Rule.required().positive(),
    },
    {
      name: 'memberPrice',
      title: 'Member Price',
      type: 'number',
      description: 'Special price for members (if applicable)',
    },
    {
      name: 'capacity',
      title: 'Capacity',
      type: 'number',
      description: 'Maximum number of attendees',
    },
    {
      name: 'skillLevel',
      title: 'Skill Level',
      type: 'string',
      options: {
        list: [
          {title: 'Beginner', value: 'beginner'},
          {title: 'Intermediate', value: 'intermediate'},
          {title: 'Advanced', value: 'advanced'},
          {title: 'All Levels', value: 'all'},
        ],
      },
      initialValue: 'all',
    },
    {
      name: 'duration',
      title: 'Duration (minutes)',
      type: 'number',
      description: 'Length of class in minutes',
    },
    {
      name: 'whatToBring',
      title: 'What to Bring',
      type: 'array',
      of: [{type: 'string'}],
      description: 'Items participants should bring to class',
    },
    {
      name: 'whatIsProvided',
      title: 'What Is Provided',
      type: 'array',
      of: [{type: 'string'}],
      description: 'Items that will be provided to participants',
    },
    {
      name: 'recipes',
      title: 'Recipes Covered',
      type: 'array',
      of: [{type: 'reference', to: {type: 'recipe'}}],
    },
    {
      name: 'bookingUrl',
      title: 'Booking URL',
      type: 'url',
      description: 'Link to book this class',
    },
    {
      name: 'isSoldOut',
      title: 'Is Sold Out',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'metaTitle',
      title: 'Meta Title',
      type: 'string',
      description: 'Custom title for SEO (defaults to class title if not set)',
    },
    {
      name: 'metaDescription',
      title: 'Meta Description',
      type: 'text',
      description: 'Custom description for SEO',
    },
    {
      name: 'openGraphImage',
      title: 'Social Share Image',
      type: 'imageAsset',
      description:
        'Image to display when shared on social media (defaults to main image if not set)',
      options: {
        hotspot: true,
      },
    },
  ],
  preview: {
    select: {
      title: 'title',
      instructor: 'instructor.name',
      date: 'date',
      media: 'mainImage',
    },
    prepare(selection: any) {
      const {title, instructor, date, media} = selection
      const formattedDate = date ? new Date(date).toLocaleDateString() : ''
      return {
        title: title,
        subtitle: instructor ? `${formattedDate} - Instructor: ${instructor}` : formattedDate,
        media: media,
      }
    },
  },
}
