import {getMetaFieldsForPage} from '../../utils/pageFields'
import getRichTextFields from '../../utils/richText'

const ARTICLE_TYPE = 'article'
const BLOG_POST_TYPE = 'blogPost'

const articleTypes = [
  {
    title: 'Article',
    value: ARTICLE_TYPE,
  },
  {
    title: 'Blog Post',
    value: BLOG_POST_TYPE,
  },
]
export default {
  name: 'article',
  title: 'Article',
  type: 'document',
  groups: [
    {
      name: 'basics',
      title: 'Basic Information',
      default: true,
    },
    {
      name: 'content',
      title: 'Article Content',
    },
    {
      name: 'metadata',
      title: 'Metadata',
    },
    {
      name: 'relationships',
      title: 'Relationships',
    },
    {
      name: 'seo',
      title: 'SEO & Social',
    },
    {
      name: 'craftData',
      title: 'Craft Data',
    },
  ],
  fields: [
    // Basics
    {
      name: 'postType',
      title: 'Post Type',
      type: 'string',
      initialValue: articleTypes.find((type) => type.value === ARTICLE_TYPE)?.value,
      options: {
        list: articleTypes.map((type) => ({
          title: type.title,
          value: type.value,
        })),
        layout: 'radio',
      },
      group: 'basics',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'image',
      title: 'Image',
      type: 'imageAsset',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'imageLink',
      title: 'Image Link',
      type: 'link',
      initialValue: {
        linkType: 'disabled',
      },
      group: 'basics',
    },
    {
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'updatedAt',
      title: 'Updated At',
      type: 'datetime',
      group: 'basics',
    },
    {
      name: 'authors',
      title: 'Authors',
      type: 'array',
      of: [{type: 'reference', to: {type: 'person'}}],
      group: 'basics',
    },
    // Content
    {
      name: 'subheader',
      title: 'Subheader',
      type: 'string',
      group: 'content',
    },
    {
      name: 'lead',
      title: 'Lead',
      type: 'string',
      group: 'content',
      hidden: ({document}: {document: any}) => document?.postType !== ARTICLE_TYPE,
    },
    {
      name: 'body',
      title: 'Body',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong']})],
      group: 'content',
    },
    {
      name: 'sponsors',
      title: 'Sponsors',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'content',
    },
    {
      name: 'friendsOfMilkStreetSponsors',
      title: 'Friends of Milk Street Sponsors',
      type: 'array',
      of: [{type: 'reference', to: {type: 'person'}}],
      group: 'content',
    },
    {
      name: 'credits',
      title: 'Credits',
      type: 'string',
      hidden: ({document}: {document: any}) => document?.postType !== ARTICLE_TYPE,
      group: 'content',
    },
    // Relationships
    {
      name: 'relatedRecipes',
      title: 'Related Recipes',
      type: 'array',
      of: [{type: 'reference', to: {type: 'recipe'}}],
      group: 'relationships',
    },
    {
      name: 'magazineIssue',
      title: 'Magazine Issue',
      type: 'reference',
      to: [{type: 'magazineIssue'}],
      group: 'relationships',
    },
    {
      name: 'magazineColumn',
      title: 'Magazine Column',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'relationships',
    },
    {
      name: 'relatedEpisodes',
      title: 'Related Episodes',
      type: 'array',
      of: [{type: 'reference', to: [{type: 'tvEpisode'}, {type: 'radioEpisode'}]}],
      group: 'relationships',
      hidden: ({document}: {document: any}) => document?.postType !== BLOG_POST_TYPE,
    },
    // SEO Fields
    ...getMetaFieldsForPage(),
    // Craft Data
    {
      name: 'craftData',
      title: 'Craft Data',
      type: 'object',
      group: 'craftData',
      fields: [
        {
          name: 'id',
          title: 'ID',
          type: 'text',
          rows: 1,
          readOnly: true,
        },
        {
          name: 'importedFromCraft',
          title: 'Imported From Craft',
          type: 'boolean',
          initialValue: false,
          readOnly: true,
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
      subtitle: 'subheader',
      media: 'image',
    },
    prepare({title, subtitle, media}: {title: string; subtitle: string; media: any}) {
      return {
        title,
        subtitle,
        media,
      }
    },
  },
}
