export default {
  name: 'category',
  title: 'Category',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'filterType',
      title: 'Filter Type',
      type: 'string',
      initialValue: 'dropdown',
      options: {
        layout: 'radio',
        list: [
          {
            title: 'Dropdown',
            value: 'dropdown',
          },
          {
            title: 'Checkbox',
            value: 'checkbox',
          },
          {
            title: 'Radio',
            value: 'radio',
          },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'craftData',
      title: 'Craft Data',
      type: 'object',
      options: {
        collapsible: true,
        collapsed: true,
      },
      fields: [
        {
          name: 'id',
          title: 'ID',
          type: 'text',
          rows: 1,
          readOnly: true,
        },
        {
          name: 'importedFromCraft',
          title: 'Imported From Craft',
          type: 'boolean',
          initialValue: false,
          readOnly: true,
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
      subtitle: 'description',
    },
  },
}
