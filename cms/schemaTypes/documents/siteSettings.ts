import {Rule} from 'sanity'

export default {
  name: 'siteSettings',
  title: 'Site Settings',
  type: 'document',
  groups: [
    {
      name: 'general',
      title: 'General',
    },
    {
      name: 'navigation',
      title: 'Navigation',
    },
    {
      name: 'footer',
      title: 'Footer',
    },
    {
      name: 'announcements',
      title: 'Announcements',
    },
    {
      name: 'membership',
      title: 'Membership',
    },
    {
      name: 'seo',
      title: 'SEO & Social',
    },
    {
      name: 'cookie',
      title: '<PERSON><PERSON> Consent',
    },
  ],
  fields: [
    // General Settings
    {
      name: 'title',
      title: 'Site Title',
      type: 'string',
      description: 'The name of your site, displayed in the browser tab',
      group: 'general',
    },
    {
      name: 'description',
      title: 'Site Description',
      type: 'text',
      description: 'Used for SEO and social sharing',
      group: 'general',
    },
    {
      name: 'contactInfo',
      title: 'Contact Information',
      type: 'object',
      group: 'general',
      fields: [
        {
          name: 'email',
          title: 'Email',
          type: 'string',
        },
        {
          name: 'phone',
          title: 'Phone',
          type: 'string',
        },
        {
          name: 'address',
          title: 'Address',
          type: 'text',
        },
      ],
    },
    {
      name: 'copyrightText',
      title: 'Copyright Text',
      type: 'string',
      description:
        'Text that appears after the dynamic year (e.g., "Milk Street. All rights reserved.")',
      initialValue: 'Milk Street. All rights reserved.',
      group: 'footer',
    },

    // Navigation
    {
      name: 'headerNav',
      title: 'Header Navigation',
      type: 'array',
      group: 'navigation',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'isLink',
              title: 'Is Title a Link?',
              type: 'boolean',
              initialValue: true,
            },
            {
              name: 'title',
              title: 'Title',
              type: 'string',
              hidden: ({parent}: {parent: any}) => parent?.isLink,
              validation: (rule: Rule) =>
                rule.custom((value: any, context: any) => {
                  if (!context.parent.isLink && !value) {
                    return 'This field is required'
                  } else {
                    return true
                  }
                }),
            },
            {
              name: 'linkData',
              title: 'Link',
              type: 'link',
              options: {
                collapsible: false,
                collapsed: false,
              },
              hidden: ({parent}: {parent: any}) => {
                return !parent?.isLink && parent?.linkData?.linkType === 'disabled'
              },
              initialValue: {
                linkType: 'disabled',
              },
            },
            {
              name: 'subMenuGroups',
              title: 'Sub Menu Groups',
              type: 'array',
              description: 'Configure up to 4 columns for the dropdown submenu',
              validation: (Rule: Rule) => Rule.max(4),
              of: [
                {
                  type: 'object',
                  fields: [
                    {
                      name: 'items',
                      title: 'Items',
                      type: 'array',
                      description: 'Add headers and links to this column',
                      of: [
                        {
                          type: 'object',
                          name: 'header',
                          title: 'Header',
                          fields: [
                            {
                              name: 'title',
                              title: 'Header Title',
                              type: 'string',
                              validation: (Rule: any) => Rule.required(),
                            },
                            {
                              name: 'titleLink',
                              title: 'Header Link',
                              type: 'linkNoLabel',
                              description: 'Optional link for the header',
                              initialValue: {
                                linkType: 'disabled',
                              },
                              options: {
                                collapsible: true,
                                collapsed: true,
                              },
                            },
                          ],
                          preview: {
                            select: {
                              title: 'title',
                            },
                            prepare({title}: {title: string}) {
                              return {
                                title: `Header: ${title}`,
                              }
                            },
                          },
                        },
                        {
                          type: 'object',
                          name: 'link',
                          title: 'Link',
                          fields: [
                            {
                              name: 'linkData',
                              title: 'Link',
                              type: 'link',
                              validation: (Rule: any) => Rule.required(),
                              options: {
                                collapsible: true,
                                collapsed: false,
                              },
                            },
                          ],
                          preview: {
                            select: {
                              title: 'title',
                            },
                            prepare({title}: {title: string}) {
                              return {
                                title: `Link: ${title}`,
                              }
                            },
                          },
                        },
                      ],
                    },
                  ],
                  preview: {
                    select: {
                      items: 'items',
                    },
                    prepare({items}: {items: any[]}) {
                      return {
                        title: `Column with ${items?.length || 0} item${items?.length === 1 ? '' : 's'}`,
                      }
                    },
                  },
                },
              ],
            },
          ],
          preview: {
            select: {
              title: 'title',
              linkTitle: 'linkData.label',
              isLink: 'isLink',
              subMenuGroups: 'subMenuGroups',
            },
            prepare({title, linkTitle, isLink, subMenuGroups}: any) {
              const finalTitle = isLink ? linkTitle : title
              let subtitle = 'No submenu'

              if (subMenuGroups && subMenuGroups.length > 0) {
                const columnCount = subMenuGroups.length
                const totalLinks = subMenuGroups.reduce((total: number, group: any) => {
                  return total + (group.links?.length || 0)
                }, 0)

                subtitle = `${columnCount} column${columnCount === 1 ? '' : 's'}, ${totalLinks} link${totalLinks === 1 ? '' : 's'}`
              }

              return {
                title: finalTitle || 'Untitled Link',
                subtitle,
              }
            },
          },
        },
      ],
    },
    {
      name: 'signupLink',
      title: 'Signup Link',
      type: 'link',
      group: 'navigation',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'loginLink',
      title: 'Login Link',
      type: 'link',
      group: 'navigation',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'bowtieImage',
      title: 'Bow Tie Image',
      type: 'imageAsset',
      group: 'navigation',
      validation: (Rule: any) => Rule.required(),
    },

    // Footer Settings
    {
      name: 'footerNav',
      title: 'Footer Navigation',
      type: 'array',
      group: 'footer',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'isLink',
              title: 'Is Title a Link?',
              type: 'boolean',
              initialValue: false,
            },
            {
              name: 'link',
              title: 'Link',
              type: 'link',
              hidden: ({parent}: {parent: any}) => !parent?.isLink,
              validation: (Rule: any) =>
                Rule.custom((link: string, context: any) => {
                  if (context.parent?.isLink && !link) {
                    return 'Link is required when "Is Title a Link?" is checked'
                  }
                  return true
                }),
            },
            {
              name: 'links',
              title: 'Links',
              type: 'array',
              of: [
                {
                  type: 'object',
                  fields: [
                    {
                      name: 'linkData',
                      title: 'Link',
                      type: 'link',
                      validation: (Rule: any) => Rule.required(),
                    },
                  ],
                  preview: {
                    select: {
                      title: 'linkData.label',
                    },
                  },
                },
              ],
            },
          ],
          preview: {
            select: {
              title: 'title',
              isLink: 'isLink',
              titleLink: 'link.label',
            },
            prepare({
              title,
              isLink,
              titleLink,
            }: {
              title: string
              isLink: boolean
              titleLink: string
            }) {
              return {
                title,
                subtitle: isLink ? `Links to: ${titleLink}` : 'No link',
              }
            },
          },
        },
      ],
    },
    {
      name: 'footerNewsletter',
      title: 'Footer Newsletter',
      type: 'object',
      group: 'footer',
      fields: [
        {
          name: 'title',
          title: 'Title',
          type: 'string',
          initialValue: 'Get Recipes, Tips & News',
        },
        {
          name: 'description',
          title: 'Description',
          type: 'string',
          initialValue:
            'You will receive special offers from Milk Street. You can unsubscribe from our emails at any time.',
        },
        {
          name: 'placeholderText',
          title: 'Email Placeholder Text',
          type: 'string',
          initialValue: 'Email...',
        },
        {
          name: 'buttonText',
          title: 'Submit Button Text',
          type: 'string',
          initialValue: '→',
        },
      ],
    },
    {
      name: 'socialLinks',
      title: 'Social Links',
      type: 'array',
      group: 'footer',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'platform',
              title: 'Platform',
              type: 'string',
              options: {
                list: [
                  {title: 'Facebook', value: 'facebook'},
                  {title: 'X (Twitter)', value: 'twitter'},
                  {title: 'YouTube', value: 'youtube'},
                  {title: 'Pinterest', value: 'pinterest'},
                  {title: 'Instagram', value: 'instagram'},
                ],
              },
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'url',
              title: 'URL',
              type: 'url',
              validation: (Rule: any) => Rule.required(),
            },
          ],
          preview: {
            select: {
              platform: 'platform',
              url: 'url',
            },
            prepare({platform, url}: {platform: string; url: string}) {
              const platformTitles: {[key: string]: string} = {
                facebook: 'Facebook',
                twitter: 'X (Twitter)',
                youtube: 'YouTube',
                pinterest: 'Pinterest',
                instagram: 'Instagram',
              }
              return {
                title: platformTitles[platform] || platform,
                subtitle: url,
              }
            },
          },
        },
      ],
    },
    {
      name: 'footerLegalLinks',
      title: 'Footer Legal Links',
      type: 'array',
      group: 'footer',
      description: 'Legal links and text that appear at the bottom of the footer',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'text',
              title: 'Text',
              type: 'string',
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'isLink',
              title: 'Is this a link?',
              type: 'boolean',
              initialValue: true,
            },
            {
              name: 'linkData',
              title: 'Link',
              type: 'link',
              hidden: ({parent}: {parent: any}) => !parent?.isLink,
              validation: (Rule: any) =>
                Rule.custom((link: string, context: any) => {
                  if (context.parent?.isLink && !link) {
                    return 'Link is required when "Is this a link?" is checked'
                  }
                  return true
                }),
            },
          ],
          preview: {
            select: {
              text: 'text',
              isLink: 'isLink',
              link: 'link',
            },
            prepare({text, isLink, link}: {text: string; isLink: boolean; link: string}) {
              return {
                title: text,
                subtitle: isLink ? `Links to: ${link}` : 'Text only',
              }
            },
          },
        },
      ],
      initialValue: [
        {
          text: 'Terms & Conditions',
          isLink: true,
          link: '/terms',
          openInNewTab: false,
        },
        {
          text: 'Privacy',
          isLink: true,
          link: '/privacy',
          openInNewTab: false,
        },
        {
          text: 'Cookie Policy',
          isLink: true,
          link: '/cookie-policy',
          openInNewTab: false,
        },
        {
          text: 'Do not sell my personal information',
          isLink: true,
          link: '/privacy/do-not-sell',
          openInNewTab: false,
        },
      ],
    },

    // Global Announcement
    {
      name: 'globalAnnouncement',
      title: 'Global Announcement Bar',
      type: 'object',
      group: 'announcements',
      fields: [
        {
          name: 'enabled',
          title: 'Enable Announcement Bar',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'text',
          title: 'Announcement Text',
          type: 'string',
          description: 'Text to display in the announcement bar',
          validation: (Rule: any) =>
            Rule.custom((text: string, context: any) => {
              if (context.parent?.enabled && !text) {
                return 'Announcement text is required when enabled'
              }
              return true
            }),
        },
        {
          name: 'link',
          title: 'Announcement Link',
          type: 'string',
          description: 'Optional URL to link from the announcement bar',
        },
        {
          name: 'backgroundColor',
          title: 'Background Color',
          type: 'string',
          description: 'Color code for the announcement bar (e.g., #FF0000 for red)',
          initialValue: '#EE4B2B', // Milk Street red
        },
        {
          name: 'textColor',
          title: 'Text Color',
          type: 'string',
          description: 'Color code for the announcement text (e.g., #FFFFFF for white)',
          initialValue: '#FFFFFF',
        },
        {
          name: 'displayUntil',
          title: 'Display Until',
          type: 'datetime',
          description: 'Optional: date when the announcement should automatically stop showing',
        },
      ],
    },

    // Newsletter Popup
    {
      name: 'newsletterPopup',
      title: 'Newsletter Signup Popup',
      type: 'object',
      group: 'announcements',
      fields: [
        {
          name: 'enabled',
          title: 'Enable Newsletter Popup',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'title',
          title: 'Popup Title',
          type: 'string',
          initialValue: 'Join the Milk Street Community',
        },
        {
          name: 'description',
          title: 'Popup Description',
          type: 'text',
          initialValue:
            'Subscribe to our newsletter for recipes, cooking tips, and exclusive offers.',
        },
        {
          name: 'image',
          title: 'Popup Image',
          type: 'imageAsset',
        },
        {
          name: 'buttonText',
          title: 'Button Text',
          type: 'string',
          initialValue: 'Subscribe Now',
        },
        {
          name: 'delay',
          title: 'Display Delay (seconds)',
          type: 'number',
          description: 'How long to wait before showing the popup',
          initialValue: 5,
        },
        {
          name: 'frequency',
          title: 'Display Frequency (days)',
          type: 'number',
          description: 'How many days to wait before showing the popup again to the same user',
          initialValue: 7,
        },
      ],
    },

    // Membership Promotion
    {
      name: 'membershipPromo',
      title: 'Membership Promotion',
      type: 'object',
      group: 'membership',
      fields: [
        {
          name: 'enabled',
          title: 'Enable Membership Promotion',
          type: 'boolean',
          initialValue: false,
        },
        {
          name: 'title',
          title: 'Promotion Title',
          type: 'string',
          initialValue: 'Become a Milk Street Member',
        },
        {
          name: 'description',
          title: 'Promotion Description',
          type: 'text',
          initialValue: 'Get unlimited access to all recipes, cooking classes, and more.',
        },
        {
          name: 'image',
          title: 'Promotion Image',
          type: 'imageAsset',
        },
        {
          name: 'buttonText',
          title: 'Button Text',
          type: 'string',
          initialValue: 'Join Now',
        },
        {
          name: 'buttonLink',
          title: 'Button Link',
          type: 'string',
          initialValue: '/membership',
        },
        {
          name: 'discount',
          title: 'Discount Information',
          type: 'string',
          description: 'E.g., "Save 20% today"',
        },
        {
          name: 'expiration',
          title: 'Promotion Expiration',
          type: 'datetime',
          description: 'When this promotion expires',
        },
      ],
    },

    // SEO & Social Sharing
    {
      name: 'socialSharing',
      title: 'Social Sharing Settings',
      type: 'object',
      group: 'seo',
      fields: [
        {
          name: 'keywords',
          title: 'Keywords',
          type: 'array',
          of: [{type: 'string'}],
          description: 'The meta keywords of the site',
          validation: (Rule: any) => Rule.required().min(1),
        },
        {
          name: 'favicon',
          title: 'Favicon',
          type: 'imageAsset',
          description: 'Recommended size: at least 1200x1200. Keep to square dimensions.',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'defaultOpenGraphImage',
          title: 'Default Social Share Image',
          type: 'imageAsset',
          description:
            'Default image used when content is shared on social media (if not specified on individual content)',
          validation: (Rule: any) => Rule.required(),
        },
      ],
    },

    // Cookie Consent
    {
      name: 'cookieConsent',
      title: 'Cookie Consent Banner',
      type: 'object',
      group: 'cookie',
      fields: [
        {
          name: 'enabled',
          title: 'Enable Cookie Consent Banner',
          type: 'boolean',
          initialValue: true,
        },
        {
          name: 'text',
          title: 'Consent Text',
          type: 'text',
          initialValue:
            'We use cookies to enhance your experience on our website. By continuing to browse, you agree to our use of cookies.',
        },
        {
          name: 'acceptButtonText',
          title: 'Accept Button Text',
          type: 'string',
          initialValue: 'Accept All',
        },
        {
          name: 'settingsButtonText',
          title: 'Settings Button Text',
          type: 'string',
          initialValue: 'Cookie Settings',
        },
        {
          name: 'privacyPolicyLink',
          title: 'Privacy Policy Link',
          type: 'string',
          initialValue: '/privacy-policy',
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
    },
    prepare({title}: {title: string}) {
      return {
        title: title || 'Site Settings',
      }
    },
  },
}
