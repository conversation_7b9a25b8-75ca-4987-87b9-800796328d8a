import {getMetaFieldsForPage} from '../../utils/pageFields'
import getRichTextFields from '../../utils/richText'

const recipeSchema = {
  name: 'recipe',
  title: 'Recipe',
  type: 'document',
  fieldsets: [
    {
      name: 'cookingTimeFieldset',
      title: 'Cooking Time',
      options: {columns: 3},
    },
    {
      name: 'servingsFieldset',
      title: 'Servings',
      options: {columns: 2},
    },
  ],
  groups: [
    {
      name: 'basics',
      default: true,
      title: 'Basic Information',
    },
    {
      name: 'content',
      title: 'Recipe Content',
    },
    {
      name: 'relationships',
      title: 'Relationships',
    },
    {
      name: 'metadata',
      title: 'Metadata',
    },
    {
      name: 'seo',
      title: 'SEO & Social',
    },
    {
      name: 'craftData',
      title: 'Craft Data',
    },
  ],
  fields: [
    /* =============================================== */
    // Basics
    /* =============================================== */
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'summary',
      title: 'Summary',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong', 'italic']})],
      group: 'basics',
    },
    {
      name: 'images',
      title: 'Images',
      type: 'array',
      of: [{type: 'imageAsset'}],
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    /* =============================================== */
    // Content
    /* =============================================== */
    {
      name: 'cookTime',
      title: 'Cook Time',
      type: 'string',
      group: 'content',
      fieldset: 'cookingTimeFieldset',
    },
    {
      name: 'cookTimeNotes',
      title: 'Cook Time Notes',
      type: 'string',
      group: 'content',
      fieldset: 'cookingTimeFieldset',
    },
    {
      name: 'slowCookTime',
      title: 'Slow Cook Time',
      type: 'string',
      group: 'content',
      fieldset: 'cookingTimeFieldset',
    },
    {
      name: 'servings',
      title: 'Servings',
      type: 'string',
      group: 'content',
      fieldset: 'servingsFieldset',
    },
    {
      name: 'servingsLabel',
      title: 'Servings Label',
      type: 'string',
      ie: '"Fajitas" would render "X Fajitas" instead of "X Servings"',
      group: 'content',
      fieldset: 'servingsFieldset',
    },
    {
      title: 'Body',
      name: 'body',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong', 'italic']})],
      group: 'content',
    },
    {
      title: 'Tip',
      name: 'tip',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong', 'italic']})],
      group: 'content',
    },
    {
      title: 'Recipe Byline Label',
      name: 'recipeBylineLabel',
      type: 'string',
      description:
        'To override the "By [Author]" label text. ie. "Adapted By" would show up as "Adapted by [Author]"',
      group: 'content',
    },
    {
      title: 'Series Tag',
      name: 'seriesTag',
      type: 'string',
      group: 'content',
    },
    {
      title: 'Ingredients',
      name: 'ingredients',
      type: 'array',
      of: [
        {
          type: 'recipeIngredient',
        },
        {
          type: 'recipeIngredientSectionHeading',
        },
      ],
      group: 'content',
    },
    {
      title: 'Cooking Directions',
      name: 'cookingDirections',
      type: 'array',
      of: [{type: 'recipeDirection'}, {type: 'recipeDirectionFastSlow'}],
      group: 'content',
    },
    {
      title: 'Content Blocks',
      name: 'contentBlocks',
      type: 'array',
      of: [
        {type: 'contentBlockStepByStep'},
        {type: 'contentBlockText'},
        {type: 'contentBlockVideo'},
      ],
      group: 'content',
    },

    /* =============================================== */
    // Relationships
    /* =============================================== */
    {
      name: 'relatedRecipes',
      title: 'Related Recipes',
      type: 'array',
      of: [{type: 'reference', to: {type: 'recipe'}}],
      group: 'relationships',
    },
    {
      name: 'recipeCategories',
      title: 'Recipe Categories',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'relationships',
    },
    {
      name: 'method',
      title: 'Method',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'relationships',
    },
    {
      name: 'dish',
      title: 'Dish',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'relationships',
    },
    {
      name: 'region',
      title: 'Region',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'relationships',
    },
    {
      name: 'dietCategories',
      title: 'Diet Categories',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'relationships',
    },
    {
      name: 'sourceCategories',
      title: 'Source Categories',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'relationships',
    },
    {
      name: 'timeCategories',
      title: 'Time Categories',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'relationships',
    },
    {
      name: 'ingredientsCategories',
      title: 'Ingredients Categories',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'relationships',
    },
    {
      name: 'magazineIssue',
      title: 'Magazine Issue',
      type: 'reference',
      to: [{type: 'magazineIssue'}],
      group: 'relationships',
    },
    {
      name: 'magazineColumnCategories',
      title: 'Magazine Column Categories',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
      group: 'relationships',
    },

    /* =============================================== */
    // Metadata
    /* =============================================== */
    {
      name: 'authors',
      title: 'Authors',
      type: 'array',
      of: [{type: 'reference', to: {type: 'person'}}],
      group: 'metadata',
    },
    {
      name: 'publishedAt',
      title: 'Published at',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },

    /* =============================================== */
    // SEO
    /* =============================================== */
    {
      name: 'titleTag',
      title: 'Title Tag',
      type: 'string',
      description: 'Custom title for the page (defaults to recipe title if not set)',
      group: 'seo',
    },
    ...getMetaFieldsForPage(),
    /* =============================================== */
    // Craft Data
    /* =============================================== */
    {
      name: 'craftData',
      title: 'Craft Data',
      type: 'object',
      group: 'craftData',
      fields: [
        {
          name: 'id',
          title: 'ID',
          type: 'text',
          rows: 1,
          readOnly: true,
        },
        {
          name: 'importedFromCraft',
          title: 'Imported From Craft',
          type: 'boolean',
          initialValue: false,
          readOnly: true,
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
      author: 'authors.0.name',
      media: 'images.0.asset',
    },
    prepare(selection: any) {
      const {author} = selection
      return {...selection, subtitle: author && `by ${author}`}
    },
  },
}

export default recipeSchema
