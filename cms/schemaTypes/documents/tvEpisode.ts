/* eslint-disable */

import {getMetaFieldsForPage} from '../../utils/pageFields'
import getRichTextFields from '../../utils/richText'

export default {
  name: 'tvEpisode',
  title: 'TV Episode',
  type: 'document',
  groups: [
    {
      name: 'basics',
      default: true,
      title: 'Basic Information',
    },
    {
      name: 'content',
      title: 'Content',
    },
    {
      name: 'relationships',
      title: 'Relationships',
    },
    {
      name: 'seo',
      title: 'SEO & Social',
    },
    {
      name: 'craftData',
      title: 'Craft Data',
    },
  ],
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'summary',
      title: 'Summary',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong']})],
      group: 'basics',
    },
    {
      type: 'imageAsset',
      title: 'Image',
      name: 'image',
      group: 'basics',
    },
    {
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'number',
      title: 'Number',
      type: 'string',
      group: 'content',
    },
    {
      name: 'season',
      title: 'Season',
      type: 'reference',
      to: [{type: 'subcategory'}],
      group: 'content',
    },
    {
      name: 'video',
      title: 'Video',
      type: 'video',
      options: {
        collapsible: true,
        collapsed: false,
      },
      group: 'content',
    },
    {
      name: 'body',
      title: 'Body',
      type: 'array',
      of: [getRichTextFields({items: ['bullet', 'link', 'strong', 'italic', 'blockquote']})],
      group: 'content',
    },
    {
      name: 'relatedRecipes',
      title: 'Related Recipes',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'recipe'}],
        },
      ],
      group: 'relationships',
    },
    {
      name: 'relatedArticles',
      title: 'Related Articles',
      type: 'reference',
      to: [{type: 'article'}],
      group: 'relationships',
    },
    // SEO & Social
    ...getMetaFieldsForPage(),
    // Craft Data
    {
      name: 'craftData',
      title: 'Craft Data',
      type: 'object',
      group: 'craftData',
      fields: [
        {
          name: 'id',
          title: 'ID',
          type: 'text',
          rows: 1,
          readOnly: true,
        },
        {
          name: 'importedFromCraft',
          title: 'Imported From Craft',
          type: 'boolean',
          initialValue: false,
          readOnly: true,
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
      season: 'season.title',
      episode: 'number',
      media: 'image',
    },
    prepare(selection: any) {
      return {
        title: selection.title,
        subtitle: `${selection.season}, Episode ${selection.episode}`,
        media: selection.media,
      }
    },
  },
}
