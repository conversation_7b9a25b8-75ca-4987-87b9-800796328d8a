import pageSectionTypes from '../../utils/pageSectionTypes'

export default {
  name: 'pageSectionExample',
  title: 'Page Section Example',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
    },
    {
      name: 'figmaLink',
      title: 'Figma Link',
      type: 'string',
      description: 'Enter the Figma link for the page section example',
    },
    {
      name: 'imageExample',
      title: 'Image Example',
      type: 'imageAssetNoAlt',
    },
    {
      name: 'content',
      title: 'Page Content',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'sharedSection'}],
          title: 'Shared Section',
        },
        ...pageSectionTypes.map((type) => ({
          type,
        })),
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
      slug: 'slug.current',
      media: 'imageExample',
    },
    prepare({
      title,
      slug,
      media,
      createdAt,
    }: {
      title: string
      slug: string
      media: any
      createdAt: string
    }) {
      return {
        title,
        subtitle: slug ? `/page-sections/${slug}` : '',
        media,
      }
    },
  },
}
