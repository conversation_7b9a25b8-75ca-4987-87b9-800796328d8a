import {Rule} from 'sanity'
import getRichTextFields from '../../utils/richText'
import {requiredImageDimensions} from '../../utils/imageAssetUtilities'

export default {
  name: 'person',
  title: 'Person',
  type: 'document',
  fields: [
    {
      name: 'name',
      title: 'Name',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      validation: (Rule: Rule) => Rule.required(),
      options: {
        source: 'name',
        maxLength: 96,
      },
    },
    {
      name: 'image',
      title: 'Image',
      type: 'imageAsset',
      description: 'Required dimensions: 2500px in width.',
      validation: (Rule: Rule) => requiredImageDimensions({Rule, required: false, width: 2500}),
    },
    {
      name: 'bio',
      title: 'Bio',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong']})],
    },
    {
      name: 'designation',
      title: 'Designation',
      type: 'string',
      description: 'Professional title (e.g., "Chef", "Food Editor")',
    },
    {
      name: 'craftData',
      title: 'Craft Data',
      type: 'object',
      options: {
        collapsible: true,
        collapsed: true,
      },
      fields: [
        {
          name: 'id',
          title: 'ID',
          type: 'text',
          rows: 1,
          readOnly: true,
        },
        {
          name: 'importedFromCraft',
          title: 'Imported From Craft',
          type: 'boolean',
          initialValue: false,
          readOnly: true,
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'name',
      media: 'image',
      subtitle: 'title',
    },
  },
}
