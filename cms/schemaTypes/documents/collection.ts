export default {
  name: 'collection',
  title: 'Collection',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      validation: (Rule: any) => Rule.required(),
      options: {
        source: 'title',
      },
    },
    {
      name: 'items',
      title: 'Collection Items',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [
            {type: 'recipe'},
            {type: 'article'},
            {type: 'class'},
            {type: 'radioEpisode'},
            {type: 'tvEpisode'},
            {type: 'magazineIssue'},
            // {type: 'page'},
            // {type: 'product'},
          ],
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
      firstItemImage: 'items.0.image',
      firstItemImages: 'items.0.images.0.asset',
      firstItemType: 'items.0._type',
    },
    prepare({title, firstItemImage, firstItemImages}: any) {
      let media = firstItemImage || firstItemImages || null
      return {
        title: title || 'Untitled Collection',
        media: media,
      }
    },
  },
}
