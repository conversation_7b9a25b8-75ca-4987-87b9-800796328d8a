import {Rule} from 'sanity'
import pageSectionTypes from '../../utils/pageSectionTypes'

export default {
  name: 'sharedSection',
  type: 'document',
  title: 'Shared Section',
  fields: [
    {
      name: 'section',
      description: 'Select ONE section only.',
      title: 'Section',
      type: 'array',
      of: pageSectionTypes.map((type) => ({type})),
      validation: (Rule: Rule) => Rule.required().min(1).max(1),
    },
  ],
  preview: {
    select: {
      section: 'section',
    },
    prepare({section}: {section: any[]}) {
      const sectionData = section[0]
      const title = `${sectionData?.cmsSettings?.cmsTitle || sectionData?._type}`
      const isHidden = sectionData?.cmsSettings?.isHidden
      const type = sectionData?._type
      const subtitle = type || ''

      return {
        title,
        subtitle: `${isHidden ? '⚪' : '🟢'} ${subtitle}`,
      }
    },
  },
}
