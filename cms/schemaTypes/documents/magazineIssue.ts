import {getMetaFieldsForPage} from '../../utils/pageFields'
import getRichTextFields from '../../utils/richText'

export default {
  name: 'magazineIssue',
  title: 'Magazine Issue',
  type: 'document',
  groups: [
    {
      name: 'basics',
      default: true,
      title: 'Basic Information',
    },
    {
      name: 'featuredContent',
      title: 'Featured Content',
    },
    {
      name: 'editorsNote',
      title: 'Editors Note',
    },
    {
      name: 'relationships',
      title: 'Relationships',
    },
    {
      name: 'seo',
      title: 'SEO & Social',
    },
    {
      name: 'craftData',
      title: 'Craft Data',
    },
  ],
  fields: [
    // Basics
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'image',
      title: 'Image',
      type: 'imageAsset',
      group: 'basics',
    },
    // Featured Content
    {
      name: 'featuredArticle',
      title: 'Featured Article',
      type: 'reference',
      to: [{type: 'article'}],
      group: 'featuredContent',
    },
    {
      name: 'featuredTitle',
      title: 'Featured Title',
      type: 'string',
      group: 'featuredContent',
    },
    {
      name: 'featuredSubtitle',
      title: 'Featured Subtitle',
      type: 'string',
      group: 'featuredContent',
    },
    {
      name: 'featuredImage',
      title: 'Featured Image',
      type: 'imageAsset',
      group: 'featuredContent',
    },
    // Editors Note
    {
      name: 'useLegacyEditorsNoteMeta',
      title: 'Use Legacy Editors Note Meta',
      type: 'boolean',
      initialValue: false,
      group: 'editorsNote',
    },
    {
      name: 'editorsNoteTitle',
      title: 'Editors Note Title',
      type: 'string',
      group: 'editorsNote',
      hidden: ({document}: {document: any}) => {
        return !document?.useLegacyEditorsNoteMeta
      },
    },
    {
      name: 'editorsNoteByline',
      title: 'Editors Note By Line',
      type: 'string',
      group: 'editorsNote',
      hidden: ({document}: {document: any}) => {
        return !document?.useLegacyEditorsNoteMeta
      },
    },
    {
      name: 'editorsNoteImage',
      title: 'Editors Note Image',
      type: 'imageAsset',
      group: 'editorsNote',
      hidden: ({document}: {document: any}) => {
        return !document?.useLegacyEditorsNoteMeta
      },
    },
    {
      title: 'Authors',
      name: 'authors',
      type: 'array',
      of: [{type: 'reference', to: {type: 'person'}}],
      group: 'editorsNote',
      hidden: ({document}: {document: any}) => {
        return document?.useLegacyEditorsNoteMeta
      },
    },
    {
      title: 'Editors Note',
      name: 'editorsNote',
      type: 'array',
      validation: (Rule: any) => Rule.required(),
      of: [getRichTextFields({items: ['bullet', 'link', 'strong']})],
      group: 'editorsNote',
    },
    // SEO & Social
    ...getMetaFieldsForPage(),
    // Craft Data
    {
      name: 'craftData',
      title: 'Craft Data',
      type: 'object',
      group: 'craftData',
      fields: [
        {
          name: 'id',
          title: 'ID',
          type: 'text',
          rows: 1,
          readOnly: true,
        },
        {
          name: 'importedFromCraft',
          title: 'Imported From Craft',
          type: 'boolean',
          initialValue: false,
          readOnly: true,
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
      episode: 'episodeNumber',
      media: 'image',
      legacyBody: 'legacyBody',
    },
    prepare(selection: any) {
      console.log({title: selection.title, legacyBody: selection.legacyBody})
      const {title, episode, media} = selection
      return {
        title: title,
        subtitle: `Episode ${episode}`,
        media: media,
      }
    },
  },
}
