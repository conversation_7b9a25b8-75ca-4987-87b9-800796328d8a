import {getMetaFieldsForPage} from '../../utils/pageFields'
import getRichTextFields from '../../utils/richText'

export default {
  name: 'radioEpisode',
  title: 'Radio Episode',
  type: 'document',
  groups: [
    {
      name: 'basics',
      default: true,
      title: 'Basic Information',
    },
    {
      name: 'content',
      title: 'Content',
    },
    {
      name: 'relationships',
      title: 'Relationships',
    },
    {
      name: 'seo',
      title: 'SEO & Social',
    },
    {
      name: 'craftData',
      title: 'Craft Data',
    },
  ],
  fields: [
    // Basics
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime',
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'summary',
      title: 'Summary',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong']})],
      validation: (Rule: any) => Rule.required(),
      group: 'basics',
    },
    {
      name: 'image',
      title: 'Image',
      type: 'imageAsset',
      group: 'basics',
    },
    // Content
    {
      name: 'episodeNumber',
      title: 'Episode Number',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
      group: 'content',
    },
    {
      name: 'audioUrl',
      title: 'Audio URL',
      type: 'string',
      description: 'URL to the episode audio file',
      validation: (Rule: any) => Rule.required(),
      group: 'content',
    },
    {
      name: 'originalEpisode',
      title: 'Original Episode',
      type: 'reference',
      to: [{type: 'radioEpisode'}],
      group: 'content',
    },
    {
      name: 'useLegacyBody',
      title: 'Use Legacy Body',
      type: 'boolean',
      initialValue: false,
      group: 'content',
    },
    {
      name: 'legacyBody',
      title: 'Legacy Body',
      type: 'array',
      of: [getRichTextFields({items: ['bullet', 'link', 'strong', 'italic', 'blockquote']})],
      hidden: ({document}: {document: any}) => !document?.useLegacyBody,
      group: 'content',
    },
    {
      name: 'body',
      title: 'Body',
      type: 'array',
      of: [getRichTextFields({items: ['bullet', 'link', 'strong', 'italic', 'blockquote']})],
      hidden: ({document}: {document: any}) => document?.useLegacyBody,
      group: 'content',
    },
    {
      name: 'iTunesUrl',
      title: 'iTunes URL',
      type: 'string',
      group: 'content',
    },
    {
      name: 'tuneInUrl',
      title: 'TuneIn URL',
      type: 'string',
      group: 'content',
    },
    {
      name: 'spotifyUrl',
      title: 'Spotify URL',
      type: 'string',
      group: 'content',
    },
    {
      name: 'pandoraUrl',
      title: 'Pandora URL',
      type: 'string',
      group: 'content',
    },
    {
      name: 'amazonMusicUrl',
      title: 'Amazon Music URL',
      type: 'string',
      group: 'content',
    },
    {
      name: 'transcript',
      title: 'Transcript',
      type: 'array',
      of: [getRichTextFields({items: ['bullet', 'link', 'strong', 'italic', 'blockquote']})],
      group: 'content',
    },
    // Relationships
    {
      name: 'relatedRecipes',
      title: 'Related Recipes',
      type: 'array',
      of: [{type: 'reference', to: {type: 'recipe'}}],
      group: 'relationships',
    },
    // SEO & Social
    ...getMetaFieldsForPage(),
    // Craft Data
    {
      name: 'craftData',
      title: 'Craft Data',
      type: 'object',
      group: 'craftData',
      fields: [
        {
          name: 'id',
          title: 'ID',
          type: 'text',
          rows: 1,
          readOnly: true,
        },
        {
          name: 'importedFromCraft',
          title: 'Imported From Craft',
          type: 'boolean',
          initialValue: false,
          readOnly: true,
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'title',
      episode: 'episodeNumber',
      media: 'image',
      legacyBody: 'legacyBody',
    },
    prepare(selection: any) {
      console.log({title: selection.title, legacyBody: selection.legacyBody})
      const {title, episode, media} = selection
      return {
        title: title,
        subtitle: `Episode ${episode}`,
        media: media,
      }
    },
  },
}
