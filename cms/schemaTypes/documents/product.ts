export default {
  name: 'product',
  title: 'Product',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule) => Rule.required(),
    },
    {
      name: 'productImages',
      title: 'Product Images',
      type: 'array',
      validation: (Rule) => Rule.required().min(1),
      of: [
        {
          type: 'imageAssetNoAlt',
        },
      ],
    },
    {
      name: 'price',
      title: 'Price (USD)',
      type: 'number',
      validation: (Rule) => Rule.required(),
    },
    {
      name: 'salePrice',
      title: 'Sale Price (USD)',
      type: 'number',
    },
    {
      name: 'onSale',
      title: 'On Sale',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'sku',
      title: 'SKU',
      type: 'string',
    },
    {
      name: 'categories',
      title: 'Categories',
      type: 'array',
      of: [{type: 'reference', to: {type: 'subcategory'}}],
    },
    {
      name: 'shortDescription',
      title: 'Short Description',
      type: 'text',
      rows: 4,
    },
    {
      name: 'description',
      title: 'Full Description',
      type: 'array',
      of: [{type: 'block'}],
    },
    {
      name: 'specifications',
      title: 'Specifications',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'name',
              title: 'Name',
              type: 'string',
            },
            {
              name: 'value',
              title: 'Value',
              type: 'string',
            },
          ],
        },
      ],
    },
    {
      name: 'relatedProducts',
      title: 'Related Products',
      type: 'array',
      of: [{type: 'reference', to: {type: 'product'}}],
    },
    {
      name: 'relatedRecipes',
      title: 'Related Recipes',
      type: 'array',
      of: [{type: 'reference', to: {type: 'recipe'}}],
    },
    {
      name: 'inStock',
      title: 'In Stock',
      type: 'boolean',
      initialValue: true,
    },
    {
      name: 'featured',
      title: 'Featured',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'publishedAt',
      title: 'Published at',
      type: 'datetime',
    },
  ],
  preview: {
    select: {
      title: 'title',
      price: 'price',
      media: 'productImages.0',
    },
    prepare(selection) {
      const {title, price, media} = selection
      return {
        title,
        subtitle: price ? `$${price}` : 'No price set',
        media,
      }
    },
  },
}
