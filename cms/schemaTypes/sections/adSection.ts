import {Rule} from 'sanity'

export default {
  name: 'adSection',
  title: 'Ad Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      description: 'Optional title for the ad section',
    },
    {
      name: 'ad',
      title: 'Ad',
      type: 'adObject',
      validation: (Rule: Rule) => Rule.required(),
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
      title: 'title',
      adType: 'ad.type',
    },
    prepare({title, adType}: {title?: string; adType?: string}) {
      return {
        title: `Ad Section: ${title}`,
        subtitle: `Ad Type: ${adType}`,
      }
    },
  },
}
