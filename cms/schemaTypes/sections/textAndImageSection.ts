import {referenceableCardTypes} from '../../utils/referenceableCardTypes'

export default {
  name: 'textAndImageSection',
  title: 'Text and Image Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'useContentReference',
      title: 'Use Content Reference',
      description:
        'If a reference is used, the content will be pulled from the reference. Any fields filled in after the reference will override the reference content.',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'referenceForContent',
      title: 'Reference for Content',
      type: 'reference',
      to: referenceableCardTypes.map((type) => ({type})),
      hidden: ({parent}: {parent: any}) => parent?.useContentReference === false,
      validation: (Rule: any) =>
        Rule.custom((field: any, context: any) => {
          if (context.parent?.useContentReference === true && !field) {
            return 'This is required when using a content reference'
          }
          return true
        }),
    },
    {
      name: 'eyebrow',
      title: 'Eyebrow Text',
      type: 'string',
    },
    {
      name: 'linkLabel',
      title: 'Link Label',
      type: 'string',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) =>
        Rule.custom((field: any, context: any) => {
          if (!context.parent?.useContentReference && !field) {
            return 'This field is required'
          }
          return true
        }),
    },
    {
      name: 'description',
      title: 'Description',
      description: 'Note: This will be truncated to 200 characters.',
      type: 'text',
      validation: (Rule: any) =>
        Rule.custom((field: any, context: any) => {
          if (!context.parent?.useContentReference && !field) {
            return 'This field is required'
          }
          return true
        }),
    },
    {
      name: 'image',
      title: 'Image',
      type: 'imageAsset',
      description: 'Note: This will be cropped to 4:3.',
      validation: (Rule: any) =>
        Rule.custom((field: any, context: any) => {
          if (!context.parent?.useContentReference && !field) {
            return 'This field is required'
          }
          return true
        }),
    },
    {
      name: 'price',
      title: 'Price',
      type: 'number',
      hidden: ({parent}: {parent: any}) => parent?.useContentReference,
    },
    {
      name: 'discountPrice',
      title: 'DiscountPrice',
      type: 'number',
      hidden: ({parent}: {parent: any}) => parent?.useContentReference,
    },
    {
      name: 'link',
      title: 'Link',
      type: 'link',
      initialValue: {
        linkType: 'disabled',
      },
      hidden: ({parent}: {parent: any}) => parent?.useContentReference,
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Text and Image Section'

      return {
        title,
      }
    },
  },
}
