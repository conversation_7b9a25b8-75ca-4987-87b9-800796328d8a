import getRichTextFields from '../../utils/richText'

export default {
  name: 'richTextSection',
  title: 'Rich Text Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      title: 'Content',
      name: 'content',
      type: 'array',
      validation: (Rule: any) => Rule.required(),
      of: [getRichTextFields({items: ['bullet', 'heading2', 'link', 'strong']})],
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Rich Text Section'

      return {
        title,
      }
    },
  },
}
