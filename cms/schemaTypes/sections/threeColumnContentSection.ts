import {Rule} from 'sanity'

export default {
  name: 'threeColumnContentSection',
  title: 'Three Column Content Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'items',
      title: 'Column Items',
      type: 'array',
      of: [{type: 'imageAsset'}, {type: 'titleTextAndCta'}, {type: 'eyebrowWithLineItems'}],
      validation: (Rule: Rule) => Rule.required().min(3).max(3),
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
      title: 'title',
      image: 'image',
    },
    prepare(selection: any) {
      const {cmsSettings, title, image} = selection
      const sectionTitle = cmsSettings?.cmsTitle || title

      return {
        title: sectionTitle,
        subtitle: title,
        media: image,
      }
    },
  },
}
