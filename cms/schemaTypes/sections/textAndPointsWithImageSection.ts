import getRichTextFields from '../../utils/richText'

export default {
  name: 'textAndPointsWithImageSection',
  title: 'Text and Points with Image Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      title: 'Description',
      name: 'description',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong']})],
    },
    {
      name: 'points',
      title: 'Points',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'title',
              title: 'Title',
              type: 'string',
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'text',
              title: 'Text',
              type: 'string',
              validation: (Rule: any) => Rule.required(),
            },
          ],
        },
      ],
    },
    {
      name: 'image',
      title: 'Image',
      type: 'imageAsset',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'imageSide',
      title: 'Image Side',
      type: 'string',
      options: {
        list: [
          {title: 'Left', value: 'left'},
          {title: 'Right', value: 'right'},
        ],
      },
      initialValue: 'right',
      validation: (Rule: any) => Rule.required(),
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
      title: 'title',
    },
    prepare(selection: any) {
      const {cmsSettings, title} = selection
      const displayTitle = cmsSettings?.cmsTitle || title || 'Text and Points with Image Section'

      return {
        title: displayTitle,
      }
    },
  },
}
