import {Rule} from 'sanity'
import getRichTextFields from '../../utils/richText'

export default {
  name: 'imageAndRichTextWithButton',
  title: 'Image and Rich Text with Button',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'media',
      title: 'Media',
      type: 'array',
      of: [{type: 'imageAsset'}, {type: 'video'}],
      validation: (Rule: Rule) => Rule.required().max(1),
    },
    {
      title: 'Description',
      name: 'description',
      type: 'array',
      of: [getRichTextFields({items: ['link', 'strong', 'italic']}), {type: 'button'}],
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Image and Rich Text with Button'

      return {
        title,
      }
    },
  },
}
