import {Rule} from 'sanity'

export default {
  name: 'contentList',
  title: 'Content List',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
    },
    {
      name: 'titleSize',
      title: 'Title Size',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
      options: {
        list: [
          {title: 'Small', value: 'sm'},
          {title: 'Medium', value: 'md'},
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      initialValue: 'md',
    },
    {
      name: 'listType',
      title: 'List Type',
      type: 'string',
      initialValue: 'stacked',
      options: {
        list: [
          {title: 'Carousel', value: 'carousel'},
          {title: 'Stacked', value: 'stacked'},
          {title: 'Vertical', value: 'vertical'},
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'containerSize',
      title: 'Container Size',
      type: 'string',
      initialValue: 'medium',
      hidden: ({parent}: any) => parent?.listType !== 'vertical',
      options: {
        list: [
          {title: 'Small', value: 'small'},
          {title: 'Medium', value: 'medium'},
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'perRow',
      title: 'Per Row',
      type: 'string',
      initialValue: '5',
      hidden: ({parent}: any) => parent?.listType !== 'stacked',
      options: {
        list: [
          {title: '3', value: '3'},
          {title: '4', value: '4'},
          {title: '5', value: '5'},
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'items',
      title: 'Items',
      type: 'cardItems',
      options: {
        collapsible: true,
        collapsed: false,
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'cta',
      title: 'Call to Action',
      type: 'link',
      options: {
        collapsible: true,
        collapsed: false,
      },
      initialValue: {
        linkType: 'disabled',
      },
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Content List'

      return {
        title,
      }
    },
  },
}
