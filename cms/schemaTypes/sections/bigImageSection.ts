import {ConditionalPropertyCallbackContext, Rule} from 'sanity'

export default {
  name: 'bigImageSection',
  title: 'Big Image Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'mediaType',
      title: 'Media Type',
      type: 'string',
      options: {
        list: [
          {title: 'Image', value: 'image'},
          {title: 'Video', value: 'video'},
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      initialValue: 'image',
      validation: (Rule: Rule) =>
        Rule.required().custom((mediaType, context) => {
          const media = (context?.parent as {media?: any})?.media
          if (!media || !Array.isArray(media) || !media[0]) return true
          const selectedType = media[0]._type === 'video' ? 'video' : 'image'
          if (mediaType !== selectedType) {
            return `Media type "${mediaType}" does not match selected media (_type: "${media[0]._type}"). Please update Media Type.`
          }
          return true
        }),
    },
    {
      name: 'aspectRatio',
      title: 'Aspect Ratio',
      type: 'string',
      options: {
        list: [
          {title: '16:9', value: '16:9'},
          {title: 'Original', value: 'imageAspect'},
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      initialValue: '16:9',
      hidden: ({parent}: ConditionalPropertyCallbackContext) => parent?.mediaType === 'video',
    },
    {
      name: 'media',
      title: 'Media',
      description: 'Select the image or video to display',
      type: 'array',
      of: [
        {
          type: 'imageAsset',
          title: 'Image',
          hidden: ({parent}: ConditionalPropertyCallbackContext) => parent?.mediaType === 'video',
        },
        {
          type: 'video',
          title: 'Video',
          hidden: ({parent}: ConditionalPropertyCallbackContext) => parent?.mediaType === 'image',
        },
      ],
      validation: (Rule: Rule) =>
        Rule.custom((media: any, context: any) => {
          const _type = media?.[0]?._type

          if (!_type) return 'Media is required'
          if (media?.length > 1) return 'Only one media item is allowed'

          // Rule.required().min(1).max(1)
          if (context?.parent?.mediaType === 'video' && _type !== 'video') {
            return 'Video media is required when Media Type is set to Video.'
          }
          if (context?.parent?.mediaType === 'image' && _type !== 'imageAsset') {
            return 'Image media is required when Media Type is set to Image.'
          }
          return true
        }),
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
      media: 'media',
      mediaType: 'mediaType',
    },
    prepare(selection: any) {
      const {cmsSettings, media, mediaType} = selection
      const title = cmsSettings?.cmsTitle || 'Big Image Section'
      const mediaItem = media && media.length > 0 ? media[0] : null

      return {
        title,
        subtitle: mediaType === 'video' ? 'Video' : 'Image',
        media: mediaType === 'image' && mediaItem ? mediaItem.asset : null,
      }
    },
  },
}
