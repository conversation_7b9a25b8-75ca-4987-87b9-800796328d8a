import {Rule} from 'sanity'
import getRichTextFields from '../../utils/richText'

export default {
  name: 'logoSliderSection',
  title: 'Logo Slider Section',
  type: 'object',
  options: {
    collapsible: true,
    collapsed: true,
  },
  fields: [
    {
      name: 'cmsSettings',
      title: 'CMS Settings',
      type: 'cmsSettings',
    },
    {
      name: 'content',
      title: 'Content',
      type: 'array',
      of: [
        getRichTextFields({
          items: ['italic', 'link', 'heading2'],
        }),
      ],
    },
    {
      name: 'logoItems',
      title: 'Logo Items',
      type: 'array',
      validation: (Rule: Rule) => Rule.required().min(1),
      of: [
        {
          type: 'object',
          preview: {
            select: {
              media: 'image',
            },
          },
          fields: [
            {
              name: 'image',
              title: 'Image',
              type: 'imageAsset',
              validation: (Rule: Rule) => Rule.required(),
            },
            {
              name: 'desktopWidth',
              title: 'Desktop Width',
              description: 'Width of the logo on desktop, defaults to 100px',
              type: 'number',
            },
            {
              name: 'mobileWidth',
              title: 'Mobile Width',
              description: 'Width of the logo on desktop, defaults to 100px',
              type: 'number',
            },
          ],
        },
      ],
    },
  ],
  preview: {
    select: {
      cmsSettings: 'cmsSettings',
    },
    prepare(selection: any) {
      const {cmsSettings} = selection
      const title = cmsSettings?.cmsTitle || 'Logo Slider Section'

      return {
        title,
      }
    },
  },
}
