const isValidUrl = (url: string) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

const ShopifyImageComponent = (props: any) => {
  if (!props?.value || !isValidUrl(props?.value)) {
    return <div>Invalid URL</div>
  }

  if (props?.value) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          height: '100%',
          backgroundColor: '#e5e5e5',
        }}
      >
        <img
          src={props.value}
          style={{
            width: '100%',
            height: '100%',
            maxWidth: '100%',
            maxHeight: '300px',
            objectFit: 'contain',
          }}
        />
      </div>
    )
  }
  return <div>No value entered</div>
}

export default ShopifyImageComponent
