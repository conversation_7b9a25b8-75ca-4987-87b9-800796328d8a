import {PackageIcon} from '@sanity/icons'
import {defineField} from 'sanity'

export const collectionGroupType = defineField({
  name: 'shopifyCollectionGroup',
  title: 'Collection group',
  type: 'object',
  icon: PackageIcon,
  fields: [
    defineField({
      name: 'title',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'collectionLinks',
      type: 'shopifyCollectionLinks',
    }),
    defineField({
      name: 'collectionProducts',
      type: 'reference',
      description: 'Products from this collection will be listed',
      weak: true,
      to: [{type: 'shopifyCollection'}],
    }),
  ],
})
