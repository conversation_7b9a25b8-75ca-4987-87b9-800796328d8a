import {defineField} from 'sanity'

export const imageCallToActionType = defineField({
  name: 'shopifyImageCallToAction',
  title: 'Image Call to action',
  type: 'object',
  fields: [
    defineField({
      name: 'title',
      type: 'string',
    }),
    defineField({
      name: 'link',
      type: 'array',
      of: [{type: 'shopifyLinkInternal'}, {type: 'shopifyLinkExternal'}],
      validation: (Rule) => Rule.max(1),
    }),
  ],
})
