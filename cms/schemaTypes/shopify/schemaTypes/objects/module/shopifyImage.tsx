import {defineField} from 'sanity'
import ShopifyImageComponent from '../../../components/inputs/ShopifyImage'

const isValidUrl = (url: string) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export const shopifyImage = defineField({
  name: 'shopifyImage',
  title: 'Shopify Image',
  type: 'object',
  icon: false,
  fields: [
    defineField({
      name: 'width',
      type: 'number',
    }),
    defineField({
      name: 'height',
      type: 'number',
    }),
    defineField({
      name: 'id',
      type: 'string',
    }),
    defineField({
      name: 'altText',
      type: 'string',
    }),
    defineField({
      name: 'src',
      type: 'string',
      components: {
        input: ShopifyImageComponent,
      },
    }),
  ],
  preview: {
    select: {
      src: 'src',
      title: 'id',
      altText: 'altText',
    },
    prepare(selection) {
      return {
        title: selection?.altText || selection?.title || undefined,
        media: isValidUrl(selection?.src) ? (
          <img
            src={`${selection?.src}&width=100`}
            style={{
              height: '100%',
              left: 0,
              objectFit: 'contain',
              position: 'absolute',
              top: 0,
              width: '100%',
            }}
          />
        ) : undefined,
      }
    },
  },
})
