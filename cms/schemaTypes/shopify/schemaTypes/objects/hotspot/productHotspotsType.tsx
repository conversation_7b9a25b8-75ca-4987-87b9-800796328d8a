import {defineArrayMember, defineField} from 'sanity'

// import ProductTooltip from '../../../components/hotspots/ProductTooltip'

export const productHotspotsType = defineField({
  name: 'shopifyProductHotspots',
  title: 'Hotspots',
  type: 'array',
  of: [defineArrayMember({type: 'shopifySpot'})],
  // options: {
  //   imageHotspot: {
  //     imagePath: 'image',
  //     tooltip: ProductTooltip,
  //     pathRoot: 'parent',
  //   },
  // },
})
