import {defineField} from 'sanity'

export const inventoryType = defineField({
  name: 'shopifyInventory',
  title: 'Inventory',
  type: 'object',
  options: {
    columns: 3,
  },
  fields: [
    defineField({
      name: 'isAvailable',
      title: 'Available',
      type: 'boolean',
    }),
    defineField({
      name: 'management',
      type: 'string',
    }),
    defineField({
      name: 'policy',
      type: 'string',
    }),
    defineField({
      name: 'quantity',
      type: 'number',
    }),
  ],
})
