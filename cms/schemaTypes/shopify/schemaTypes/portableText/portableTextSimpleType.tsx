import {defineArrayMember, defineField} from 'sanity'

export const portableTextSimpleType = defineField({
  name: 'shopifyPortableTextSimple',
  type: 'array',
  of: [
    defineArrayMember({
      lists: [
        {title: 'Bullet', value: 'bullet'},
        {title: 'Numbered', value: 'number'},
      ],
      marks: {
        decorators: [
          {
            title: 'Italic',
            value: 'em',
          },
          {
            title: 'Strong',
            value: 'strong',
          },
        ],
        annotations: [
          {
            name: 'linkProduct',
            type: 'shopifyLinkProduct',
          },
          {
            name: 'linkEmail',
            type: 'shopifyLinkEmail',
          },
          {
            name: 'linkInternal',
            type: 'shopifyLinkInternal',
          },
          {
            name: 'linkExternal',
            type: 'shopifyLinkExternal',
          },
        ],
      },
      type: 'block',
    }),
  ],
})
