import {defineArrayMember, defineField} from 'sanity'

export const portableTextType = defineField({
  name: 'shopifyPortableText',
  type: 'array',
  of: [
    defineArrayMember({
      lists: [
        {title: 'Bullet', value: 'bullet'},
        {title: 'Numbered', value: 'number'},
      ],
      marks: {
        decorators: [
          {
            title: 'Italic',
            value: 'em',
          },
          {
            title: 'Strong',
            value: 'strong',
          },
        ],
        annotations: [
          {
            name: 'linkProduct',
            type: 'shopifyLinkProduct',
          },
          {
            name: 'linkEmail',
            type: 'shopifyLinkEmail',
          },
          {
            name: 'linkInternal',
            type: 'shopifyLinkInternal',
          },
          {
            name: 'linkExternal',
            type: 'shopifyLinkExternal',
          },
        ],
      },
      type: 'block',
    }),
    defineArrayMember({type: 'shopifyAccordion'}),
    defineArrayMember({type: 'shopifyCallout'}),
    defineArrayMember({type: 'shopifyGrid'}),
    defineArrayMember({type: 'shopifyImages'}),
    defineArrayMember({type: 'shopifyImageWithProductHotspots', title: 'Image with Hotspots'}),
    defineArrayMember({type: 'shopifyInstagram'}),
    defineArrayMember({type: 'shopifyProducts'}),
  ],
})
