import {HomeIcon} from '@sanity/icons'
import {defineArrayMember, defineField} from 'sanity'
import {GROUPS} from '../../constants'

const TITLE = 'Home'

export const homeType = defineField({
  name: 'shopifyHome',
  title: TITLE,
  type: 'document',
  icon: HomeIcon,
  groups: GROUPS,
  fields: [
    defineField({
      name: 'hero',
      type: 'shopifyHero',
      group: 'editorial',
    }),
    defineField({
      name: 'modules',
      type: 'array',
      of: [
        defineArrayMember({type: 'shopifyAccordion'}),
        defineArrayMember({type: 'shopifyCallout'}),
        defineArrayMember({type: 'shopifyGrid'}),
        defineArrayMember({type: 'shopifyImages'}),
        defineArrayMember({type: 'shopifyImageWithProductHotspots', title: 'Image with Hotspots'}),
        defineArrayMember({type: 'shopifyInstagram'}),
        defineArrayMember({type: 'shopifyProducts'}),
      ],
      group: 'editorial',
    }),
    defineField({
      name: 'seo',
      title: 'SEO',
      type: 'shopifySeo',
      group: 'seo',
    }),
  ],
  preview: {
    prepare() {
      return {
        media: HomeIcon,
        subtitle: 'Index',
        title: TITLE,
      }
    },
  },
})
