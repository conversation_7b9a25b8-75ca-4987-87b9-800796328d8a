This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Authentication System

The staging environment is protected by a password authentication system. This ensures that the site content remains private during development and before launch.

### Configuration

The authentication system is controlled by the following environment variables:

- `AUTH_ENABLED`: Set to `true` to enable password protection
- `AUTH_PASSWORD`: The password required to access the site
- `AUTH_EXPIRY_MINUTES`: Duration (in minutes) for which the authentication remains valid

### How it Works

1. When `AUTH_ENABLED` is set to `true`, all routes except `/api/*`, `/_next/*`, and `/auth/*` require authentication
2. Unauthenticated users are redirected to `/auth/index.html`
3. After entering the correct password, a secure HTTP-only cookie is set
4. The authentication persists for the duration specified in `AUTH_EXPIRY_MINUTES`
5. Sanity draft mode is automatically enabled after successful authentication

### Security Features

- HTTP-only cookies to prevent XSS attacks
- Strict same-site policy for cookies
- Secure cookie flag in production
- No password storage (only used for comparison)
- Protected API routes

### Testing

To test the authentication system:

1. Set `AUTH_ENABLED=true` in your `.env.local`
2. Set a password in `AUTH_PASSWORD`
3. Visit any page on the site
4. You should be redirected to the authentication page
5. Enter the password to gain access
6. Verify that you can access protected routes
7. Verify that the authentication persists after page refresh
8. Verify that the authentication expires after the specified duration

### Generating Page Sections

1. Ensure you have [Hygen](https://github.com/jondot/hygen) installed
2. In the root of the project, run `hygen section new name_of_section`. Ensure that the `name_of_section` is written in `snake_case`

### Generating Components

1. Ensure you have [Hygen](https://github.com/jondot/hygen) installed
2. Run the following in the root of the project (Ensure that the `name_of_component` is written in `snake_case`):

- `hygen section new:create-component name_of_component`

1. Drag the generated files from `/_sections` one directory down into `/components`
2. Change the typescript type from `Sanity[name_of_component]` to `[name_of_component]Props` and move the file (`Sanity[name_of_component].d.ts` -> `[name_of_component]Props.d.ts`) into the `/types/components` directory
