import axios from 'axios'
import initialize from './initialize'

initialize()

export const getCraftHeaders = () => {
  return {
    Authorization: `Bearer ${process.env.CRAFT_BEARER_TOKEN}`,
    'Content-Type': 'application/graphql',
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const sendCraftRequest = async (data: any) => {
  if (!process.env.CRAFT_GRAPHQL_ENDPOINT) {
    throw new Error('CRAFT_GRAPHQL_ENDPOINT is not set')
  }

  if (!data) {
    throw new Error('Data is required')
  }

  if (!process.env.CRAFT_BEARER_TOKEN) {
    throw new Error('CRAFT_BEARER_TOKEN is not set')
  }

  return axios.post(process.env.CRAFT_GRAPHQL_ENDPOINT, data, {
    headers: getCraftHeaders(),
  })
}
