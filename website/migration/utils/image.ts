import { sendCraftRequest } from './craftCms'
import axios from 'axios'
import { getSanityClient, getSanityIdFromCraftId } from './sanity'
import { Readable } from 'stream'
import { groq } from 'next-sanity'
import fs from 'fs'
import sharp from 'sharp'
export const MILK_STREET_URL = 'https://www.177milkstreet.com'
const client = getSanityClient()

//console.log('client ', client.config())

export const formatImageUrl = (url: string) => {
  if (!url) {
    return null
  }

  if (url.startsWith(MILK_STREET_URL)) {
    return url
  }

  if (url.startsWith('/')) {
    return `${MILK_STREET_URL}${url}`
  }

  return `${MILK_STREET_URL}/${url}`
}

export const getImageDataFromCraftId = async (id: string | string[]) => {
  if (!id) {
    throw new Error('Id is required in getImageDataFromCraftId')
  }
  const imageIds = Array.isArray(id) ? id : [id]
  const result = await sendCraftRequest(
    `
      {
        assets(id: [${imageIds.map(id => `"${id}"`).join(',')}]) {
          id
          title
          url
        }
      }
  `,
  )

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formatted: any = {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  result?.data?.data?.assets.forEach((asset: any) => {
    if (!formatted[asset?.id] && asset?.id) {
      const value = {
        id: asset?.id,
        title: asset?.title,
        url: formatImageUrl(asset?.url),
      }

      formatted[asset?.id] = value
    }
  })

  return Object.values(formatted)
}

function getBase64(url: string) {
  if (!url) {
    throw new Error('Url is required in getBase64')
  }

  return axios
    .get(url, {
      responseType: 'arraybuffer',
    })
    .then(response => {
      return {
        buffer: Buffer.from(response.data, 'binary').toString('base64'),
        contentType: response.headers['content-type'],
      }
    })
}

function bufferToStream(buffer: Buffer) {
  const stream = new Readable()
  stream.push(buffer)
  stream.push(null)
  return stream
}

export const uploadImageFromUrl = async (imageUrl: string, imageId: string) => {
  if (!imageUrl || !imageId) {
    throw new Error('Image URL and imageId are required in uploadImageFromUrl')
  }

  const { contentType, buffer } = await getBase64(imageUrl)

  const base64Buffer = Buffer.from(buffer, 'base64')

  // Convert base64 buffer to PNG format using sharp
  // For some reason some image formats are uploaded but not returned
  // in client.fetch queries
  // This seems to fix it lol
  const pngBuffer = await sharp(base64Buffer).png().toBuffer()

  const bufferStream = bufferToStream(pngBuffer)
  // const bufferStream = bufferToStream(base64Buffer)

  const id = getSanityIdFromCraftId(imageId, 'image')

  const asset = await client.assets
    .upload('image', bufferStream, {
      filename: id,
      contentType: contentType,
      source: {
        id: id,
        name: id,
      },
    })
    .catch(error => {
      console.error(error)
      return null
    })

  return asset
}

export const checkIfImageExistsInSanity = async (imageId: string | string[]) => {
  const ids = Array.isArray(imageId) ? imageId : [imageId]

  let query = ids.map(id => `source.id == "${getSanityIdFromCraftId(id, 'image')}"`).join(' || ')
  query = `*[${query}]`

  // console.log(query)
  // const exists = await client.fetch(groq`
  //   *[_type == "sanity.imageAsset" && ${query}]{
  //     "sourceId": source.id
  //   }
  // `)

  const exists = await client.fetch(groq`
    ${query}{
      _type,
      "sourceId": source.id
    }
  `)

  return exists
}

export const getUploadedImageAssetsByCraftId = async (sourceId: string | string[]) => {
  const sourceIds = Array.isArray(sourceId) ? sourceId : [sourceId]
  const sourceIdQuery = sourceIds.map(id => `source.id == "${getSanityIdFromCraftId(id, 'image')}"`).join('||')
  const query = groq`
    *[_type == "sanity.imageAsset" && (${sourceIdQuery})] {
      _id,
      source {
        id
      }
    }
  `
  const assets = await client.fetch(query)
  return assets
}

type ImageObject = {
  id: string
  url: string
}

export const getIsImageUrl = async (url: string): Promise<boolean> => {
  try {
    const response = await axios.head(url)
    const contentType = response.headers['content-type']

    return contentType?.startsWith('image/')
  } catch {
    // console.error('Error checking image URL:', err)
    return false
  }
}

// Usage

export const uploadImagesToSanity = async (imageObject: ImageObject | ImageObject[]) => {
  let imageObjects = Array.isArray(imageObject) ? imageObject : [imageObject]

  imageObjects = [
    ...new Map(
      imageObjects.map(obj => [
        obj.id,
        {
          id: obj.id,
          url: formatImageUrl(obj.url) as string,
        },
      ]),
    ).values(),
  ]

  imageObjects.forEach(obj => {
    if (!obj.url || !obj.id) {
      throw new Error('Image object must have a url and id in uploadImagesToSanity')
    }
  })

  // if (imageObjects.length > 0) {
  //   fs.appendFileSync(
  //     'migration/debug/images_that_were_uploaded.txt',
  //     `\n\nIDs to check: ${imageObjects.map(obj => obj.id).join(', ')}\n`,
  //   )
  // }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let sanityImagesThatExist: any = await checkIfImageExistsInSanity(imageObjects.map(obj => obj.id))

  // if (sanityImagesThatExist.length > 0) {
  //   fs.appendFileSync(
  //     'migration/debug/images_that_were_uploaded.txt',
  //     // eslint-disable-next-line @typescript-eslint/no-explicit-any
  //     `IDs that exist: ${sanityImagesThatExist.map((obj: any) => `${obj._type}: ${obj.sourceId}`).join(', ')}\n`,
  //   )
  // }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  sanityImagesThatExist = sanityImagesThatExist.map((obj: any) => obj.sourceId)

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let itemsToUpload: any = []
  imageObjects.forEach(obj => {
    const sanityIdExists = sanityImagesThatExist.includes(getSanityIdFromCraftId(obj.id, 'image'))
    if (!sanityIdExists) {
      itemsToUpload.push(obj)
    }
  })

  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const { default: PQueue } = require('p-queue')
  const queue = new PQueue({
    concurrency: 1,
    timeout: 40000,
    // interval: 1000,
  })

  if (itemsToUpload.length > 0) {
    // eslint-disable-next-line no-console
    console.log(`Checking if ${itemsToUpload.length} images are valid images to upload...`)
  }

  const validImageIndexes: number[] = []
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  itemsToUpload.forEach(async (item: any, i: number) => {
    const operation = () =>
      new Promise(async resolve => {
        const isImage = await getIsImageUrl(item.url) // check to make sure it goes to image content type
        // console.log('isImage')
        if (isImage) {
          validImageIndexes.push(i)
        } else {
          // eslint-disable-next-line no-console
          console.log(`Image ${item.id} with url ${item.url} is not a valid image to upload`)
        }
        resolve(isImage)
      })

    queue.add(operation)
  })

  await queue.onIdle()

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  itemsToUpload = itemsToUpload.filter((_: any, i: number) => validImageIndexes.includes(i))

  if (itemsToUpload?.length) {
    // eslint-disable-next-line no-console
    console.log(`Uploading ${itemsToUpload.length} images to Sanity...`)
  }

  let inc = 0
  for (const imageObj of itemsToUpload) {
    await fs.appendFileSync('migration/debug/images_that_were_uploaded.txt', `${imageObj.id}, ${imageObj.url}\n`)

    // Delete existing image if it exists
    // For some reason without this, it doesn't upload properly???????
    // or it overrides with an invalid format that does not return
    // when originally fetched earlier in this function
    const existingImages = await checkIfImageExistsInSanity(imageObj.id)
    if (existingImages?.length > 0) {
      for (const image of existingImages) {
        await client.delete(image._id)
      }
    }

    await uploadImageFromUrl(imageObj.url, imageObj.id)
    inc++
    process.stdout.write(`\rUploaded ${inc} of ${itemsToUpload?.length} images`)
  }

  if (itemsToUpload?.length) {
    process.stdout.write('\n')
  }

  // needs a second until can query sanity for assets
  if (itemsToUpload?.length) {
    await new Promise(resolve => setTimeout(resolve, 2500))
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const uploadedHashmap: any = {}
  const sourceIds = imageObjects.map(item => item.id)
  const sanityAssets = await getUploadedImageAssetsByCraftId(sourceIds)

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  sanityAssets.forEach((asset: any) => {
    if (asset.source.id && !uploadedHashmap[asset.source.id]) {
      uploadedHashmap[asset.source.id] = asset
    }
  })

  return uploadedHashmap
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getSanityImageArrayFromCraftImageHashmap = (imageObjects: ImageObject | ImageObject[], hashmap: any) => {
  const objects = Array.isArray(imageObjects) ? imageObjects : [imageObjects]

  return objects
    .map((obj, index) => {
      const imageFilename = getSanityIdFromCraftId(obj?.id, 'image')
      const hashmapValue = hashmap[imageFilename]
      if (!hashmapValue) {
        return null
      }

      return {
        _type: 'imageAsset',
        _key: `${imageFilename}-${index}`,
        asset: {
          _type: 'reference',
          _ref: hashmapValue._id,
        },
      }
    })
    .filter(Boolean)
}
