import { config } from 'dotenv'
import { resolve } from 'path'

const main = () => {
  const envPath = resolve(__dirname, '../../.env.local')
  const result = config({ path: envPath })
  if (result.error) {
    console.error('Error loading .env.local:', result.error)
    process.exit(1)
  }

  // Debug: Print environment variables
  console.warn('Environment variables loaded:', {
    CRAFT_GRAPHQL_ENDPOINT: process.env.CRAFT_GRAPHQL_ENDPOINT,
    // Add other relevant env vars here
  })
}

export default main
