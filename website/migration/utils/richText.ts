/* eslint-disable */

import { MILK_STREET_URL } from './image'
import { getSanityClient, getSanityIdFromCraftId } from './sanity'
import { DOC_TYPES, HOME_SLUG } from '../../data'
import { groq } from 'next-sanity'
import { htmlToBlocks } from '@portabletext/block-tools'
const { JSDOM } = require('jsdom')
const fs = require('fs')

const client = getSanityClient()

/*

Carrying over: 
  [ 'about' ]
[ '2025', '05', 'best-pasta-salad-inspired-by-mexican-street-corn' ]
[ 'magazine', 'january-february-2025' ]
[ 'radio', 'white-house-noshes-what-presidents-really-eat-1' ]
[ 'tv', 'chicken-paprikash' ]
[ 'recipes', 'thai-inspired-chicken-cabbage-salad' ]
[ 'tours', 'malaysia' ]

Maybe?
[ 'rss-feed', 'flipboard' ]
[ 'series', 'demo-sponsorship' ]
[ 'school', 'sponsors-test' ]

Not migrating:
[ 'cooking-school-teachers-staff', 'maria-capde<PERSON>lle' ]
[ 'promotions', 'digital-14-day-trial-promo-1' ]
[ 'live-qa', 'christopher-kimball-lynn-clark-feb-2020' ]
[ 'friends-of-milk-street', 'jet-tila-1' ]
[
  'milk-street-faces',
  'ercole-maggio-of-molino-maggio-in-salento-italy'
]
[ 'dish', 'cookies-and-bars' ]

*/

const SAMPLE_INTERNAL_LINK = {
  linkType: 'internal',
  internalLink: {
    _ref: 'dc5992d7-d259-413d-b371-ceecd240a788',
    _type: 'reference',
  },
}

const getPathsFromUrl = (url: string): string[] => {
  // If URL is not a valid URL string, return empty array
  try {
    const urlObj = new URL(url)
    // Get pathname and remove leading/trailing slashes
    const pathname = urlObj.pathname.replace(/^\/+|\/+$/g, '')
    // Split path into segments and filter out empty strings
    return pathname.split('/').filter(Boolean)
  } catch {
    return []
  }
}

const getLinkDocTypeAndSlugFromUrl = (url: string) => {
  if (!url.includes(MILK_STREET_URL)) {
    return {
      docType: 'external',
      slug: 'none',
      componentFields: {
        linkType: 'external',
        externalLink: url,
      },
    }
  }

  const pathParts = getPathsFromUrl(url)
  const slug = pathParts[pathParts.length - 1]

  // Home
  if (pathParts.length === 0) {
    return {
      docType: DOC_TYPES.PAGE,
      slug: HOME_SLUG,
    }
  }

  // Internal Page
  if (pathParts?.length === 1) {
    return {
      docType: DOC_TYPES.PAGE,
      slug,
    }
  }

  // Has one level of subdirectory
  if (pathParts?.length === 2) {
    switch (pathParts[0]) {
      // Is magazine
      case 'magazine':
        return {
          docType: DOC_TYPES.MAGAZINE,
          slug,
        }
      // Is radio
      case 'radio':
        return {
          docType: DOC_TYPES.RADIO_EPISODE,
          slug,
        }
      // Is tv
      case 'tv':
        return {
          docType: DOC_TYPES.TV_EPISODE,
          slug,
        }
      // Is recipes
      case 'recipes':
        return {
          docType: DOC_TYPES.RECIPE,
          slug,
        }
      // Is tours
      case 'tours':
        return {
          docType: DOC_TYPES.TOUR,
          slug,
        }
      default:
        return null
    }
  }

  // Is blog post/article
  if (pathParts.length === 3) {
    return {
      docType: DOC_TYPES.ARTICLE,
      slug,
    }
  }

  return null
}

export const getAllLinksFromLinkMarks = (blocks: any, foundOnPageString?: string) => {
  const linkUrls: any[] = []
  if (!blocks?.length) {
    return linkUrls
  }
  blocks.forEach((block: any) => {
    if (block?.markDefs?.length) {
      block.markDefs = block.markDefs.map((markDef: any) => {
        let newMarkDef = markDef
        if (newMarkDef?._type === 'link' && newMarkDef?.href) {
          let value: any = getLinkDocTypeAndSlugFromUrl(newMarkDef.href)
          if (foundOnPageString && value) {
            value.foundOn = foundOnPageString
          }
          linkUrls.push(value)
        }
        return newMarkDef
      })
    }
  })

  return linkUrls.filter(Boolean)
}

export const formatLinksInBlocks = (blocks: any, linkSlugHashmap: any) => {
  if (!blocks?.length) {
    console.warn('No blocks supplied to formatLinksInBlocks')
    return []
  }

  return blocks.map((block: any) => {
    if (block?.markDefs?.length) {
      block.markDefs = block.markDefs.map((markDef: any) => {
        let newMarkDef = markDef
        if (newMarkDef?._type === 'link') {
          let linkData = getLinkDocTypeAndSlugFromUrl(markDef.href)

          // Is external link
          if (linkData?.docType === 'external' && linkData?.componentFields) {
            return {
              ...linkData.componentFields,
              _key: markDef._key,
            }
          }

          const linkDataFromHashmap = linkSlugHashmap[`${linkData?.docType}_${linkData?.slug}`]

          // No link reference exists for URL
          if (!linkDataFromHashmap) {
            newMarkDef = {
              _key: markDef._key,
              _type: 'span',
            }
            // link reference exists
          } else {
            newMarkDef = {
              ...linkDataFromHashmap.componentFields,
              _key: markDef._key,
              _type: 'link',
            }
          }
        }
        return newMarkDef
      })
    }
    return block
  })
}

export const fetchDataByLinkObject = async (linkObject: any | any[]) => {
  const linkObjects = Array.isArray(linkObject) ? linkObject : [linkObject]

  const query = linkObjects
    .map((linkObject: any) => {
      return groq`(slug.current == "${linkObject.slug}" && _type == "${linkObject.docType}")`
    })
    .join(' || ')
  const exists = await client.fetch(groq`*[${query}]{
    _id,
    _type,
    'slug': slug.current,
    "componentFields": {
      "linkType": "internal",
      "internalLink": {
        "_ref": _id,
        "_type": "reference"
      }
    }
  }`)

  const slugHashmap: any = {}
  exists.forEach((item: any) => {
    if (item.slug) {
      slugHashmap[`${item._type}_${item.slug}`] = item
    }
  })

  const linksThatHaveNoReference = linkObjects
    .filter(linkObject => linkObject?.docType !== 'external')
    .filter((linkObject: any) => {
      return !slugHashmap[`${linkObject.docType}_${linkObject.slug}`]
    })

  return { slugHashmap, linksThatHaveNoReference }
}

export const getBlocksFromHtml = (html: string, fieldType: any) => {
  if (!html || !fieldType) {
    if (!html) {
      console.warn('No html supplied to getBlocksFromHtml.')
    }
    if (!fieldType) {
      console.warn('No fieldType supplied to getBlocksFromHtml.')
    }
    return []
  }

  const blocks = htmlToBlocks(html, fieldType, {
    parseHtml: (html: any) => new JSDOM(html).window.document,
  })

  return blocks
}

export const writeLinksNotFoundToFile = async (linksThatHaveNoReference: any[]) => {
  // Group references by where they were found
  const linksByFoundOn = linksThatHaveNoReference.reduce((acc: any, link: any) => {
    if (!acc[link.foundOn]) {
      acc[link.foundOn] = []
    }
    acc[link.foundOn].push(link)
    return acc
  }, {})

  // Format each group for debug file
  const formattedForDebugFile = Object.keys(linksByFoundOn)
    .map((key: string) => {
      let text = `Found on: ${key}`
      linksByFoundOn[key].forEach((link: any) => {
        text = `${text}\n${link?.docType} - ${link?.slug}`
      })
      return text
    })
    .join('\n\n')

  await fs.appendFileSync('migration/debug/references_that_dont_exist.txt', formattedForDebugFile)
}

export const wipeLinksNotFoundFile = async () => {
  // Clear the debug file first before appending new data
  await fs.writeFileSync('migration/debug/references_that_dont_exist.txt', '')
}
