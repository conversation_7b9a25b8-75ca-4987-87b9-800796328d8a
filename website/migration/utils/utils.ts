import { convert } from 'html-to-text'
import { getSanityClient } from './sanity'
import { groq } from 'next-sanity'
import fs from 'fs'
export const stringToSentenceCase = (string: string) => {
  if (!string) return ''

  // Handle different case formats
  let normalized = string
    // Convert camelCase and PascalCase to space-separated
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    // Convert snake_case to space-separated
    .replace(/_/g, ' ')
    // Convert kebab-case to space-separated (already handled in return, but adding here for completeness)
    .replace(/-/g, ' ')
    // Remove any extra spaces
    .trim()
    // Convert to lowercase for consistent transformation
    .toLowerCase()

  // Capitalize the first letter of each word
  normalized = normalized
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')

  return normalized
}

export const stringToSlug = (string: string): string => {
  if (!string) return ''

  return (
    string
      // Convert to lowercase
      .toLowerCase()
      // Replace spaces, underscores, and other non-alphanumeric characters with hyphens
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      // Replace multiple hyphens with a single hyphen
      .replace(/\-\-+/g, '-')
      // Remove leading and trailing hyphens
      .replace(/^-+/, '')
      .replace(/-+$/, '')
  )
}

export const wait = (ms = 0) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export const htmlToPlainText = (html: string) => {
  return convert(html, {
    // preserveNewlines: false,
    wordwrap: false,
  })
}

export const htmlToPortableTextParagraph = (html: string) => {
  const text = htmlToPlainText(html)
  return [
    {
      _key: 'body1',
      _type: 'block',
      style: 'normal',
      markDefs: [],
      children: [
        {
          _type: 'span',
          text: text,
          marks: [],
        },
      ],
    },
  ]
}

export const getVideoTypeAndId = (url: string) => {
  if (!url) return null

  // YouTube URL patterns
  const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
  const youtubeMatch = url.match(youtubeRegex)

  // Vimeo URL patterns
  const vimeoRegex =
    /(?:vimeo\.com\/(?:channels\/(?:\w+\/)?|groups\/(?:[^\/]*)\/videos\/|album\/(?:\d+)\/video\/|video\/|))(\d+)(?:[a-zA-Z0-9_\-]+)?/
  const vimeoMatch = url.match(vimeoRegex)

  if (youtubeMatch) {
    return {
      type: 'youtube',
      id: youtubeMatch[1],
    }
  }

  if (vimeoMatch) {
    return {
      type: 'vimeo',
      id: vimeoMatch[1],
    }
  }

  // External URL
  if (url.startsWith('http')) {
    return {
      type: 'url',
      url: url,
    }
  }

  return null
}

export const checkSanityForIds = async (ids: string | string[]) => {
  ids = Array.isArray(ids) ? ids : [ids]
  if (!ids.length) {
    console.warn('No ids provided to checkSanityForIds')
    return []
  }
  const sanity = getSanityClient()
  const query = ids.map((id: string) => `(_id == "${id}")`).join(' || ')
  const sanityIds = await sanity.fetch(groq`*[${query}]{_id, _type}`)

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const idHashmap: any = {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  sanityIds.forEach((item: any) => {
    if (!idHashmap[item._id]) {
      idHashmap[item._id] = item
    }
  })

  return idHashmap
}

// Normalize special characters in slugs to their ASCII equivalents
export const normalizeSlug = (slug: string, type: string) => {
  const newSlug = slug
    // Replace common special characters and diacritics
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/[àáâãäåāăąǻȁȃạảấầẩẫậắằẳẵặ]/g, 'a')
    .replace(/[æǽ]/g, 'ae')
    .replace(/[çćĉċč]/g, 'c')
    .replace(/[ďđ]/g, 'd')
    .replace(/[èéêëēĕėęěȅȇẹẻẽếềểễệ]/g, 'e')
    .replace(/[ĝğġģ]/g, 'g')
    .replace(/[ĥħ]/g, 'h')
    .replace(/[ìíîïĩīĭįǐȉȋḭỉịớờởỡợ]/g, 'i')
    .replace(/[ĵ]/g, 'j')
    .replace(/[ķ]/g, 'k')
    .replace(/[ĺļľŀł]/g, 'l')
    .replace(/[ñńņňṅṇṉṋ]/g, 'n')
    .replace(/[òóôõöōŏőơǒȍȏọỏốồổỗộớờởỡợ]/g, 'o')
    .replace(/[œ]/g, 'oe')
    .replace(/[ŕŗř]/g, 'r')
    .replace(/[śŝşšș]/g, 's')
    .replace(/[ţťŧț]/g, 't')
    .replace(/[ùúûüũūŭůűųưǔȕȗụủứừửữự]/g, 'u')
    .replace(/[ŵẃẁẅ]/g, 'w')
    .replace(/[ýÿŷỳỵỷỹ]/g, 'y')
    .replace(/[źżž]/g, 'z')
    // Handle uppercase variants
    .replace(/[ÀÁÂÃÄÅĀĂĄǺȀȂẠẢẤẦẨẪẬẮẰẲẴẶ]/g, 'A')
    .replace(/[ÆǼ]/g, 'AE')
    .replace(/[ÇĆĈĊČ]/g, 'C')
    .replace(/[ĎĐ]/g, 'D')
    .replace(/[ÈÉÊËĒĔĖĘĚȄȆẸẺẼẾỀỂỄỆ]/g, 'E')
    .replace(/[ĜĞĠĢ]/g, 'G')
    .replace(/[ĤĦ]/g, 'H')
    .replace(/[ÌÍÎÏĨĪĬĮǏȈȊḬỈỊ]/g, 'I')
    .replace(/[Ĵ]/g, 'J')
    .replace(/[Ķ]/g, 'K')
    .replace(/[ĹĻĽĿŁ]/g, 'L')
    .replace(/[ÑŃŅŇṄṆṈṊ]/g, 'N')
    .replace(/[ÒÓÔÕÖŌŎŐƠǑȌȎỌỎỐỒỔỖỘỚỜỞỠỢ]/g, 'O')
    .replace(/[Œ]/g, 'OE')
    .replace(/[ŔŖŘ]/g, 'R')
    .replace(/[ŚŜŞŠȘ]/g, 'S')
    .replace(/[ŢŤŦȚ]/g, 'T')
    .replace(/[ÙÚÛÜŨŪŬŮŰŲƯǓȔȖỤỦỨỪỬỮỰ]/g, 'U')
    .replace(/[ŴẂẀẄ]/g, 'W')
    .replace(/[ÝŸŶỲỴỶỸ]/g, 'Y')
    .replace(/[ŹŻŽ]/g, 'Z')
    // Replace other special characters
    .replace(/[^a-zA-Z0-9-]/g, '-') // Replace any other non-alphanumeric chars with hyphen
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens

  if (newSlug !== slug) {
    fs.appendFileSync('migration/debug/weird_slugs.txt', `${type}/${slug},${type}/${newSlug}\n`)
  }

  return newSlug
}
