/* eslint-disable */

export const getAllRecipesFromAirtable = async () => {
  const items = []
  let offset = null

  const mapFieldNames = (fieldName: string): string => {
    const categoryMap: { [key: string]: string } = {
      'Category ID (from Time)': 'timeCategories',
      'Category ID (from Source)': 'sourceCategories',
      'Category ID (from Ingredients)': 'ingredientsCategories',
      'Category ID (from Diet)': 'dietCategories',
      'Category ID (from Region)': 'region',
      'Category ID (from Dish)': 'dish',
      'Category ID (from Method)': 'method',
      'Home Caption': 'summary',
    }
    return categoryMap[fieldName] || fieldName
  }

  const mapFields = (fields: any) => {
    const mappedFields: { [key: string]: any } = {}

    Object.keys(fields).forEach(fieldName => {
      const mappedName = mapFieldNames(fieldName)
      if (mappedName !== fieldName) {
        mappedFields[mappedName] = fields[fieldName]
      }
    })

    return mappedFields
  }

  do {
    const url = new URL(`https://api.airtable.com/v0/${process.env.AIRTABLE_APP_ID}/Recipes`)
    if (offset) url.searchParams.set('offset', offset)

    const res: any = await fetch(url, {
      headers: {
        Authorization: `Bearer ${process.env.AIRTABLE_ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
      },
    })
    const data = await res.json()

    items.push(...data?.records)
    offset = data?.offset
  } while (offset)

  let itemsBySlug: any = {}
  items.forEach(item => {
    if (item.fields['Slug']) {
      if (!itemsBySlug[item.fields['Slug']]) {
        itemsBySlug[item.fields['Slug']] = mapFields(item.fields)
      }
    }
  })

  return itemsBySlug
}
