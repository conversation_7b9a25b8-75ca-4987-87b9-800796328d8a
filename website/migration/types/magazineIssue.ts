/* eslint-disable */

import { sendCraftRequest } from '../utils/craftCms'
import { getSanityImageArrayFromCraftImageHashmap, uploadImagesToSanity } from '../utils/image'
import {
  fetchDataByLinkObject,
  formatLinksInBlocks,
  getAllLinksFromLinkMarks,
  getBlocksFromHtml,
  wipeLinksNotFoundFile,
  writeLinksNotFoundToFile,
} from '../utils/richText'
import {
  createAllDocumentsFromCraft,
  getSanityClient,
  getSanityIdFromCraftId,
  patchAllDocsById,
  unsetAndDeleteAllItems,
} from '../utils/sanity'
import { checkSanityForIds, htmlToPortableTextParagraph, normalizeSlug } from '../utils/utils'
import { htmlToBlocks } from '@portabletext/block-tools'
import { Schema } from '@sanity/schema'
import path from 'path'
import tvEpisode from './tvEpisode'
const { JSDOM } = require('jsdom')

// Import the schema using the correct relative path from website directory
const magazineIssueSchema = require(path.resolve(__dirname, '../../../cms/schemaTypes/documents/magazineIssue')).default
const linkObjectFile = require(path.resolve(__dirname, '../../../cms/schemaTypes/objects/link')).default

// Create a schema with the radio episode type
const compiledSchemas = new Schema({
  name: 'default',
  types: [magazineIssueSchema, linkObjectFile.linkNoLabel, linkObjectFile.link],
})

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const editorsNoteType = compiledSchemas
  .get('magazineIssue')
  .fields.find((field: any) => field.name === 'editorsNote').type

const PER_PAGE = 200

const getTotalLength = async () => {
  const items = await sendCraftRequest(
    `
      {
        entries(section: "magazines", status: "live") {
          id
        }
      }
    `,
  )
  return items?.data?.data?.entries?.length
}

export const getAllItems = async (offset: number, perPage?: number) => {
  const _perPage = perPage || PER_PAGE
  const items = await sendCraftRequest(
    `
      {
        entries(section: "magazines", status: "live", limit: ${_perPage}, offset: ${offset}) {
          postDate
          id
          title
          slug
          coverImage {
            id
            url
          }
          featuredArticle {
            id
          }
          featuredTitle
          featuredSubHeader
          featuredImage {
            id
            url
          }
          editorsNoteTitle
          editorsNoteByline
          editorsNote
          editorsNoteImage{
            id
            url
          }
          pageTitle
          seoDescription
          seoImage {
            id
            url
          }
        }
      }
  `,
  )

  const value = items?.data?.data?.entries

  return value
}

export const formatItems = async (items: any) => {
  const toUpload: any[] = []
  const slugHashmap: any = {}
  let linkSlugHashmap: any = {}
  let imageAssetsByCraftId: any = {}
  let imageIdsToUpload: any[] = []
  let slugsToSearchFor: any[] = []
  let referenceIdsToCheckIfExists: any = []
  let sanityIdHashmap: any = {}

  // coverImage {
  //   id
  //   url
  // }
  // featuredArticle {
  //   id
  // }
  // featuredTitle
  // featuredSubheader
  // featuredImage {
  //   id
  //   url
  // }
  // editorsNoteTitle
  // editorsNoteByline
  // editorsNote
  // editorsNoteImage{
  //   id
  //   url
  // }
  // pageTitle
  // seoDescription
  // seoImage {
  //   id
  //   url
  // }

  items.forEach(async (item: any) => {
    // Images
    if (item?.coverImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...item.coverImage]
    }

    if (item?.featuredImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...item.featuredImage]
    }

    if (item?.seoImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...item.seoImage]
    }

    if (item?.editorsNoteImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...item.editorsNoteImage]
    }

    // Links

    const editorsNoteBlocks = getBlocksFromHtml(item?.editorsNote, editorsNoteType)
    const editorsNoteLinks = getAllLinksFromLinkMarks(editorsNoteBlocks, `Magazine Issue ${item?.slug}`)
    slugsToSearchFor = [...slugsToSearchFor, ...editorsNoteLinks]

    // References
    // Todo: featured article
    // if (item?.featuredArticle?.length) {
    //   const itemsToAdd = item.originalEpisode.map((item: any) => getSanityIdFromCraftId(item.id, 'item'))
    //   referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd]
    // }
  })

  if (referenceIdsToCheckIfExists.length) {
    sanityIdHashmap = await checkSanityForIds(referenceIdsToCheckIfExists)
  }

  if (slugsToSearchFor.length) {
    const { slugHashmap: _linkSlugHashmap, linksThatHaveNoReference } = await fetchDataByLinkObject(slugsToSearchFor)
    linkSlugHashmap = _linkSlugHashmap
    await writeLinksNotFoundToFile(linksThatHaveNoReference)
  }

  if (imageIdsToUpload.length) {
    imageAssetsByCraftId = await uploadImagesToSanity(imageIdsToUpload)
  }

  // Record
  items.forEach(async (item: any) => {
    if (item?.id && item?.title) {
      slugHashmap[item?.slug] = true

      const image = getSanityImageArrayFromCraftImageHashmap(item?.coverImage, imageAssetsByCraftId)[0]
      const openGraphImage = getSanityImageArrayFromCraftImageHashmap(item?.seoImage, imageAssetsByCraftId)[0]
      const featuredImage = getSanityImageArrayFromCraftImageHashmap(item?.featuredImage, imageAssetsByCraftId)[0]
      const editorsNoteImage = getSanityImageArrayFromCraftImageHashmap(item?.editorsNoteImage, imageAssetsByCraftId)[0]
      let editorsNoteBlocks = getBlocksFromHtml(item?.editorsNote, editorsNoteType)
      editorsNoteBlocks = formatLinksInBlocks(editorsNoteBlocks, linkSlugHashmap)

      // name: 'featuredArticle',

      const itemRecord: any = {
        _id: getSanityIdFromCraftId(item?.id, 'magazineIssue'),
        _type: 'magazineIssue',
        title: item?.title,
        slug: {
          // _type: 'slug',
          current: normalizeSlug(item?.slug, 'magazineIssue'),
        },
        publishedAt: item?.postDate,
        metaTitle: item?.pageTitle ? item?.pageTitle : null,
        metaDescription: item?.seoDescription ? item?.seoDescription : null,
        featuredTitle: item?.featuredTitle ? item?.featuredTitle : null,
        featuredSubtitle: item?.featuredSubHeader ? item?.featuredSubHeader : null,
        useLegacyEditorsNoteMeta: true,
        editorsNoteTitle: item?.editorsNoteTitle ? item?.editorsNoteTitle : null,
        editorsNoteByline: item?.editorsNoteByline ? item?.editorsNoteByline : null,
        editorsNote: editorsNoteBlocks,
        editorsNoteImage: item?.editorsNoteImage ? item?.editorsNoteImage : null,
        craftData: {
          id: item?.id,
          importedFromCraft: true,
        },
      }

      if (featuredImage) {
        itemRecord.featuredImage = featuredImage
      }

      if (openGraphImage) {
        itemRecord.openGraphImage = openGraphImage
      }

      if (image) {
        itemRecord.image = image
      }

      if (editorsNoteImage) {
        itemRecord.editorsNoteImage = editorsNoteImage
      }

      toUpload.push(itemRecord)
    }
  })

  return toUpload
}

const updateItemsInSanity = async (itemsFormatted: any) => {
  await patchAllDocsById(itemsFormatted)
}

const perPageFunction = async (page: number, totalPages: number, perPage?: number) => {
  const _perPage = perPage || PER_PAGE
  try {
    console.log(`Fetching Craft Magazine Issues Episodes`)
    console.log(`(Items ${page * _perPage + 1} - ${page * _perPage + _perPage} of ${totalPages * _perPage})...`)
    const items = await getAllItems(page * _perPage, _perPage)

    if (!items?.length) {
      console.log('No magazine issues found.')
      return
    } else {
      console.log(`Found ${items?.length} magazine issues.`)
    }
    console.log('Formatting magazineIssues to schema...')
    const itemsFormatted = await formatItems(items)
    console.log('Creating Magazine Issues in Sanity...')
    await updateItemsInSanity(itemsFormatted)
    console.log(`Done page ${page + 1} of ${totalPages}!`)
    console.log(`===============================================`)
  } catch (error) {
    console.error('Error fetching radio episodes:', error)
    process.exit(1)
  }
}

async function main({ perPage }: { perPage?: number }) {
  const _perPage = perPage || PER_PAGE
  const totalRadioEpisodes = await getTotalLength()
  const totalPages = Math.ceil(totalRadioEpisodes / _perPage)

  for (let i = 0; i <= totalPages; i++) {
    // for (let i = 1; i <= 1; i++) {
    await perPageFunction(i, totalPages, _perPage)
  }
}

async function createDocumentsInSanity() {
  await createAllDocumentsFromCraft(
    `
      {
        entries(section: "magazines", status: "live") {
          id
        }
      }
    `,
    'data.entries',
    'magazineIssue',
  )
}

const deleteAll = async () => {
  const query = `*[_type == "magazineIssue" && craftData.importedFromCraft == true]`
  await unsetAndDeleteAllItems('magazineIssue', query, ['featuredArticle'])
}

// createDocumentsInSanity()
// main({ perPage: 5 })

const moduleExports = {
  createDocumentsInSanity,
  main,
  deleteAll,
}

export default moduleExports
