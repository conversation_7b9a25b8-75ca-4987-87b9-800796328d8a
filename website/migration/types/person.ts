/* eslint-disable */

import { sendCraftRequest } from '../utils/craftCms'
import { getImageDataFromCraftId } from '../utils/image'
import {
  checkIfIdExistsInSanity,
  getSanityClient,
  getSanityIdFromCraftId,
  patchAllDocsById,
  unsetAndDeleteAllItems,
} from '../utils/sanity'
import { normalizeSlug, stringToSentenceCase, stringToSlug, wait } from '../utils/utils'

const { default: PQueue } = require('p-queue')
const queue = new PQueue({
  concurrency: 1,
  interval: 100,
})

export const getAllUsers = async () => {
  const users = await sendCraftRequest(
    `
      {
        users {
          id
          uid
          dateCreated	
          dateUpdated
          photo{
            id
          }
          firstName
          lastName
          email
          friendlyName
          fullName
          name
          username
          email
        }
      }
  `,
  )

  const value = users?.data?.data?.users

  return value
}

export const formatUsers = (users: any) => {
  const toUpload: any[] = []

  const slugHashmap: any = {}

  // "id": "73051",
  // "uid": "7162491c-d2a1-480f-89ce-b51cfa5ff7b4",
  // "dateCreated": "2019-03-10T18:44:26+00:00",
  // "dateUpdated": "2019-08-09T00:54:38+00:00",
  // "photo": null,
  // "firstName": "Merriell",
  // "lastName": "Wahwassuck Jr",
  // "email": "<EMAIL>",
  // "friendlyName": "Merriell",
  // "fullName": "Merriell Wahwassuck Jr",
  // "name": "Merriell Wahwassuck Jr",
  // "username": "<EMAIL>"

  users.forEach(async (user: any) => {
    let name = user?.fullName
    if (!name && user?.firstName && user?.lastName) {
      name = `${user?.firstName} ${user?.lastName}`
    } else if (!name && user?.name) {
      name = user?.name
    } else if (!name && user?.username) {
      name = user?.username
    }

    const slug = stringToSlug(name)

    // if (user?.photo) {
    //   console.log(`${user?.id} has photo: ${user?.photo?.id}`)
    //   const imageData = await getImageDataFromCraftId(`${user?.photo?.id}`)

    //   console.log(imageData)
    // }

    if (user?.id && name) {
      slugHashmap[slug] = true

      const value = {
        _id: getSanityIdFromCraftId(user?.id, 'person'),
        _type: 'person',
        name,
        slug: {
          current: normalizeSlug(stringToSlug(name), 'person'),
        },
        craftData: {
          id: `${user?.id}`,
          importedFromCraft: true,
        },
        // name: 'image',
        // name: 'bio',
        // name: 'designation',
      }

      toUpload.push(value)
    }
  })

  return toUpload
}

const createPeopleInSanity = async (peopleFormatted: any) => {
  await patchAllDocsById(peopleFormatted)
}

const deleteAll = async () => {
  await unsetAndDeleteAllItems('person', `*[_type == "person" && craftData.importedFromCraft == true]`)
}

async function main() {
  try {
    console.log('Fetching users from Craft...')
    const users = await getAllUsers()
    console.log('Formatting users to schema...')
    const usersFormatted = formatUsers(users)
    console.log('Creating people in Sanity...')
    await createPeopleInSanity(usersFormatted)
    console.log('Done!')
  } catch (error) {
    console.error('Error fetching users:', error)
    process.exit(1)
  }
}

// main()

const moduleExports = {
  deleteAll,
  main,
}

export default moduleExports
