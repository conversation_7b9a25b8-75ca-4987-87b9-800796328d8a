/* eslint-disable */

import { getAllRecipesFromAirtable } from '../utils/airtable'
import { sendCraftRequest } from '../utils/craftCms'
import { getSanityImageArrayFromCraftImageHashmap, uploadImagesToSanity } from '../utils/image'
import {
  createAllDocumentsFromCraft,
  getAllSubcategoriesByIdInSanity,
  getSanityClient,
  getSanityIdFromCraftId,
  patchAllDocsById,
  unsetAndDeleteAllItems,
} from '../utils/sanity'
import { checkSanityForIds, htmlToPortableTextParagraph, normalizeSlug } from '../utils/utils'
import fs from 'fs'

const PER_PAGE = 300

const getRecipesLength = async () => {
  const recipes = await sendCraftRequest(
    `
      {
        entries(section: "recipes", status: "live") {
          id
        }
      }
    `,
  )
  return recipes?.data?.data?.entries?.length
}

export const getAllRecipes = async (offset: number, perPage?: number) => {
  const _perPage = perPage || PER_PAGE
  const recipes = await sendCraftRequest(
    `
      {
        entries(section: "recipes", status: "live", limit: ${_perPage}, offset: ${offset}) {
          dateCreated
          dateUpdated
          id
          uid
          title
          slug
          postDate
          status
          titleTag
          seoDescription
          seoImage {
              id
              url
          }
          images {
              id
              url
          }
          cookTime
          cookTimeNotes
          slowCookTime
          servings
          servingsLabel
          body
          tip
          recipeByline {
            id
            name
          }
          recipeBylineLabel
          seriesTag
          ingredients {
            typeHandle
            amount
            description
            label
          }
          cookingDirections {
            typeHandle
            text
            fastDirectionText
            slowDirectionText
          }
          relatedRecipes {
              id
          }
          recipeCategories {	
              title
              id
          }
          magazineColumn {
            id
          }
          method {
              title
              name
              id
          }
          dish {
              id
              name
              title
          }
          region {
              id
              name
              title
          }
          isFree
          insiderFlag
        }
      }
  `,
  )

  const value = recipes?.data?.data?.entries

  return value
}

export const formatRecipes = async (recipes: any) => {
  const toUpload: any[] = []
  const slugHashmap: any = {}
  let imageAssetsByCraftId: any = {}
  let imageIdsToUpload: any[] = []
  let referenceIdsToCheckIfExists: any = []
  let sanityIdHashmap: any = {}

  recipes.forEach(async (recipe: any) => {
    // Images
    if (recipe?.images?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...recipe.images]
    }
    if (recipe?.seoImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...recipe?.seoImage]
    }

    // References
    if (recipe?.relatedRecipes?.length) {
      const itemsToAdd = recipe.relatedRecipes.map((item: any) => getSanityIdFromCraftId(item.id, 'recipe'))
      referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd]
    }
  })

  if (referenceIdsToCheckIfExists.length) {
    sanityIdHashmap = await checkSanityForIds(referenceIdsToCheckIfExists)
  }

  if (imageIdsToUpload.length) {
    imageAssetsByCraftId = await uploadImagesToSanity(imageIdsToUpload)
  }

  recipes.forEach(async (recipe: any) => {
    if (recipe?.id && !slugHashmap[recipe?.slug] && recipe?.title) {
      slugHashmap[recipe?.slug] = true

      const images = getSanityImageArrayFromCraftImageHashmap(recipe?.images, imageAssetsByCraftId)
      const openGraphImage = getSanityImageArrayFromCraftImageHashmap(recipe?.seoImage, imageAssetsByCraftId)[0]

      const authors = recipe?.recipeByline?.length
        ? recipe?.recipeByline.map((craftAuthorObject: any, index: number) => ({
            _type: 'reference',
            _ref: getSanityIdFromCraftId(craftAuthorObject?.id, 'person'),
            _key: `${craftAuthorObject?.id}-${index}`,
          }))
        : []

      const recipeCategories = recipe?.recipeCategories?.length
        ? recipe?.recipeCategories.map((craftCategoryObject: any, index: number) => ({
            _type: 'reference',
            _ref: getSanityIdFromCraftId(craftCategoryObject?.id, 'subcategory'),
            _key: `${craftCategoryObject?.id}-${index}`,
          }))
        : []

      const method = recipe?.method?.length
        ? recipe?.method.map((craftMethodObject: any, index: number) => ({
            _type: 'reference',
            _ref: getSanityIdFromCraftId(craftMethodObject?.id, 'subcategory'),
            _key: `${craftMethodObject?.id}-${index}`,
          }))
        : []

      const dish = recipe?.dish?.length
        ? recipe?.dish.map((craftDishObject: any, index: number) => ({
            _type: 'reference',
            _ref: getSanityIdFromCraftId(craftDishObject?.id, 'subcategory'),
            _key: `${craftDishObject?.id}-${index}`,
          }))
        : []

      const region = recipe?.region?.length
        ? recipe?.region.map((craftRegionObject: any, index: number) => ({
            _type: 'reference',
            _ref: getSanityIdFromCraftId(craftRegionObject?.id, 'subcategory'),
            _key: `${craftRegionObject?.id}-${index}`,
          }))
        : []

      const magazineColumnCategories = recipe?.magazineColumn?.length
        ? recipe?.magazineColumn.map((item: any, index: number) => ({
            _type: 'reference',
            _ref: getSanityIdFromCraftId(item?.id, 'subcategory'),
            _key: `${item?.id}-${index}`,
          }))
        : []

      /* =============================================== */
      // Ingredients
      /* =============================================== */
      const ingredients = recipe?.ingredients?.length
        ? recipe?.ingredients
            .map((ingredient: any, index: number) => {
              let type = ''
              switch (ingredient?.typeHandle) {
                case 'ingredient':
                  type = 'recipeIngredient'
                  break
                case 'sectionHeading':
                  type = 'recipeIngredientSectionHeading'
                  break
              }

              if (type === 'recipeIngredient') {
                return {
                  _type: 'recipeIngredient',
                  amount: ingredient?.amount ? ingredient?.amount : null,
                  description: ingredient?.description ? htmlToPortableTextParagraph(ingredient?.description) : [],
                  _key: `recipeIngredient-${index}`,
                }
              }

              if (type === 'recipeIngredientSectionHeading') {
                return {
                  _type: 'recipeIngredientSectionHeading',
                  _key: `recipeIngredientSectionHeading-${index}`,
                  label: ingredient?.label ? ingredient?.label : null,
                }
              }

              return null
            })
            .filter(Boolean)
        : []

      /* =============================================== */
      // Cooking Directions
      /* =============================================== */
      const cookingDirections = recipe?.cookingDirections?.length
        ? recipe?.cookingDirections
            .map((obj: any, index: number) => {
              let type = ''
              switch (obj?.typeHandle) {
                case 'direction':
                  type = 'recipeDirection'
                  break
                case 'fastSlowDirections':
                  type = 'recipeDirectionFastSlow'
                  break
              }

              if (type === 'recipeDirection') {
                return {
                  _type: 'recipeDirection',
                  text: obj?.text ? htmlToPortableTextParagraph(obj?.text) : [],
                  _key: `recipeDirection-${index}`,
                }
              }

              if (type === 'recipeIngredientSectionHeading') {
                return {
                  _type: 'recipeIngredientSectionHeading',
                  fastDirectionText: obj?.fastDirectionText ? htmlToPortableTextParagraph(obj?.fastDirectionText) : [],
                  slowDirectionText: obj?.slowDirectionText ? htmlToPortableTextParagraph(obj?.slowDirectionText) : [],
                  _key: `recipeIngredientSectionHeading-${index}`,
                }
              }

              return null
            })
            .filter(Boolean)
        : []

      const recipeRecord: any = {
        _id: getSanityIdFromCraftId(recipe?.id, 'recipe'),
        _type: 'recipe',

        // Basic data
        title: recipe?.title,
        slug: {
          current: normalizeSlug(recipe?.slug, 'recipe'),
        },
        images,
        publishedAt: recipe?.postDate,

        // Cooking time
        cookTime: recipe?.cookTime ? recipe?.cookTime : null,
        cookTimeNotes: recipe?.cookTimeNotes ? recipe?.cookTimeNotes : null,
        slowCookTime: recipe?.slowCookTime ? recipe?.slowCookTime : null,
        servings: recipe?.servings ? recipe?.servings : null,
        servingsLabel: recipe?.servingsLabel ? recipe?.servingsLabel : null,
        body: recipe?.body ? htmlToPortableTextParagraph(recipe?.body) : [],
        tip: recipe?.tip ? htmlToPortableTextParagraph(recipe?.tip) : [],
        seriesTag: recipe?.seriesTag ? recipe?.seriesTag : null,
        ingredients,
        cookingDirections,

        // Metadata
        authors,
        recipeCategories,
        method,
        dish,
        region,
        magazineColumnCategories,
        // relatedRecipes

        // SEO
        titleTag: recipe?.titleTag ? recipe?.titleTag : null,
        metaDescription: recipe?.seoDescription ? recipe?.seoDescription : null,

        // Craft Data
        craftData: {
          id: `${recipe?.id}`,
          importedFromCraft: true,
        },
      }

      if (openGraphImage) {
        recipeRecord.openGraphImage = openGraphImage
      }

      if (recipe?.relatedRecipes?.length) {
        recipeRecord.relatedRecipes = recipe.relatedRecipes
          .map((recipe: any, index: number) => {
            const itemInHashmap = sanityIdHashmap[getSanityIdFromCraftId(recipe.id, 'recipe')]

            if (itemInHashmap) {
              return {
                _type: 'reference',
                _ref: itemInHashmap._id,
                _key: `${itemInHashmap._id}-${index}`,
              }
            }

            return null
          })
          .filter((item: any) => item !== null)
      }

      toUpload.push(recipeRecord)
    }
  })

  return toUpload
}

const createRecipesInSanity = async (recipesFormatted: any) => {
  await patchAllDocsById(recipesFormatted)
}

const deleteAll = async () => {
  await unsetAndDeleteAllItems('recipe', `*[_type == "recipe" && craftData.importedFromCraft == true]`, [
    'relatedRecipes',
    'authors',
    'recipeCategories',
    'method',
    'dish',
    'region',
  ])
}

const perPageFunction = async (page: number, totalPages: number, perPage?: number) => {
  const _perPage = perPage || PER_PAGE
  try {
    console.log(
      `Fetching recipes from Craft (Items ${page * _perPage + 1} - ${page * _perPage + _perPage} of ${totalPages * _perPage})...`,
    )
    const recipes = await getAllRecipes(page * _perPage, _perPage)

    if (!recipes?.length) {
      console.log('No recipes found.')
      return
    } else {
      console.log(`Found ${recipes?.length} recipes.`)
    }
    console.log('Formatting recipes to schema...')
    const recipesFormatted = await formatRecipes(recipes)
    console.log('Creating recipes in Sanity...')
    await createRecipesInSanity(recipesFormatted)
    console.log(`Done page ${page + 1} of ${totalPages}!`)
    console.log(`===============================================`)
  } catch (error) {
    console.error('Error fetching recipes:', error)
    process.exit(1)
  }
}

async function main({ perPage }: { perPage?: number }) {
  const _perPage = perPage || PER_PAGE
  try {
    const recipesLength = await getRecipesLength()
    console.log(`Found ${recipesLength} recipes.`)
    const totalPages = Math.ceil(recipesLength / _perPage)

    for (let i = 0; i < totalPages; i++) {
      await perPageFunction(i, totalPages, _perPage)
    }

    console.log('Done!')
  } catch (error) {
    console.error('Error fetching recipes:', error)
    process.exit(1)
  }
}

async function createDocumentsInSanity() {
  await createAllDocumentsFromCraft(
    `
      {
        entries(section: "recipes", status: "live") {
          id
        }
      }
    `,
    'data.entries',
    'recipe',
  )
}

const syncWithAirtable = async () => {
  await fs.writeFileSync('migration/debug/recipes-airtable-debug.txt', '')

  console.log('Updating recipe content with airtable fields...')
  const recipesBySlugFromAirtable = await getAllRecipesFromAirtable()

  console.log('Getting all subcategories from Sanity...')
  const subcategoriesByIdInSanity = await getAllSubcategoriesByIdInSanity()

  const client = getSanityClient()

  console.log('Getting all recipes from Sanity...')
  const allRecipesInSanity = await client.fetch(`
    *[_type == "recipe" && craftData.importedFromCraft == true] {
      _id,
      slug
    }
  `)

  const transactions = []

  let inc = 1
  for (const recipe of allRecipesInSanity) {
    const airtableRecipe = recipesBySlugFromAirtable[recipe?.slug?.current]
    if (!airtableRecipe) {
      await fs.appendFileSync(
        'migration/debug/recipes-airtable-debug.txt',
        `Recipe ${recipe?.slug?.current} not found in Airtable. Skipping...\n`,
      )
      continue
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let fields: any = {}

    Object.keys(airtableRecipe).forEach((key: any) => {
      const value = airtableRecipe[key]
      if (value) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const isArrayOfStrings = (value: any): boolean => {
          return Array.isArray(value) && value.every(item => typeof item === 'string')
        }

        if (isArrayOfStrings(value)) {
          fields[key as keyof typeof fields] = value
            .map((craftId: any, index: number) => {
              const sanityId = getSanityIdFromCraftId(craftId, 'subcategory')
              if (!subcategoriesByIdInSanity[sanityId]) {
                // eslint-disable-next-line no-console
                console.log(`Subcategory ${sanityId} not found in Sanity. Skipping...`)
                return null
              }
              return {
                _type: 'reference',
                _ref: getSanityIdFromCraftId(craftId, 'subcategory'),
                _key: `${getSanityIdFromCraftId(craftId, 'subcategory')}-${index}`,
              }
            })
            .filter((item: any) => item !== null)
        } else if (key === 'summary') {
          fields.summary = htmlToPortableTextParagraph(value)
        }
      }
    })

    // if (inc < 10) {
    //   console.log(fields)
    // }

    if (Object.keys(fields).length) {
      transactions.push(client.patch(recipe._id).set(fields))
    }

    inc++
  }

  // Process transactions in batches of 30
  const batchSize = 80
  const batches = []

  for (let i = 0; i < transactions.length; i += batchSize) {
    const batch = transactions.slice(i, i + batchSize)
    batches.push(batch)
  }

  let i = 0
  for (const batch of batches) {
    await Promise.all(batch.map(transaction => transaction.commit()))
    process.stdout.write(`\rProcessed batch ${i + 1} of ${batches.length}`)
    i++
  }

  process.stdout.write('\n')

  console.log('Synced all recipes with airtable.')

  // console.log(subcategoriesByIdInSanity)
}

// syncWithAirtable()
// createDocumentsInSanity()
// main({ perPage: 300 })
// connectReferences()
// deleteAll()

// const test = async () => {
//   const stepByStepFields = `
//     ... on contentBlocks_stepByStep_BlockType {
//       anchorTag
//       label
//       headline
//       headline2
//       images {
//         id
//         url
//       }
//       text
//     }
//   `

//   const textFields = `
//     ... on contentBlocks_text_BlockType {
//       text
//       columns
//     }
//   `

//   const videoFields = `
//     ... on contentBlocks_video_BlockType {
//       anchorTag
//       videoUrl
//       text
//       videoPosition
//     }
//   `

//   let recipes = await sendCraftRequest(
//     `
//       {
//         entries(section: "recipes") {
//           title
//           slug
//           contentBlocks {
//             typeHandle
//             ${stepByStepFields}
//             ${textFields}
//             ${videoFields}
//           }
//         }
//       }
//     `,
//   )

//   recipes = recipes?.data?.data?.entries
//     .map((recipe: any) => {
//       if (recipe?.contentBlocks?.length) {
//         console.log(recipe?.slug)
//         console.log(recipe?.contentBlocks)
//         return {
//           title: recipe?.title,
//           slug: recipe?.slug,
//           contentBlocks: recipe?.contentBlocks,
//         }
//       } else {
//         return null
//       }
//     })
//     .filter((recipe: any) => recipe !== null)
// }

// test()

const moduleExports = {
  deleteAll,
  main,
  syncWithAirtable,
  // connectReferences,
  createDocumentsInSanity,
}

export default moduleExports
