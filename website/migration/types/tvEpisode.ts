/* eslint-disable */

import { sendCraftRequest } from '../utils/craftCms'
import { getSanityImageArrayFromCraftImageHashmap, uploadImagesToSanity } from '../utils/image'
import {
  fetchDataByLinkObject,
  formatLinksInBlocks,
  getAllLinksFromLinkMarks,
  getBlocksFromHtml,
  wipeLinksNotFoundFile,
  writeLinksNotFoundToFile,
} from '../utils/richText'
import {
  createAllDocumentsFromCraft,
  getSanityClient,
  getSanityIdFromCraftId,
  patchAllDocsById,
  unsetAndDeleteAllItems,
} from '../utils/sanity'
import { Schema } from '@sanity/schema'
import path from 'path'
import { checkSanityForIds, getVideoTypeAndId, normalizeSlug } from '../utils/utils'

const SEASON_HASHMAP = {
  '1032983': {
    _id: 'craft_subcategory_1032983',
    slug: 'season-6',
  },
  '1432': {
    _id: 'craft_subcategory_1432',
    slug: 'season-1',
  },
  '1607568': {
    _id: 'craft_subcategory_1607568',
    slug: 'season-7',
  },
  '188435': {
    _id: 'craft_subcategory_188435',
    slug: 'season-4',
  },
  '2210052': {
    _id: 'craft_subcategory_2210052',
    slug: 'season-8',
  },
  '43983': {
    _id: 'craft_subcategory_43983',
    slug: 'season-2',
  },
  '555691': {
    _id: 'craft_subcategory_555691',
    slug: 'season-5',
  },
  '90267': {
    _id: 'craft_subcategory_90267',
    slug: 'season-3',
  },
}

// Import the schema using the correct relative path from website directory
const tvEpisodeSchema = require(path.resolve(__dirname, '../../../cms/schemaTypes/documents/tvEpisode')).default
const linkObjectFile = require(path.resolve(__dirname, '../../../cms/schemaTypes/objects/link')).default

// Create a schema with the radio episode type
const compiledSchemas = new Schema({
  name: 'default',
  types: [tvEpisodeSchema, linkObjectFile.linkNoLabel, linkObjectFile.link],
})

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const bodyType = compiledSchemas.get('tvEpisode').fields.find((field: any) => field.name === 'body').type

const PER_PAGE = 200

const getTvEpisodesLength = async () => {
  const tvEpisodes = await sendCraftRequest(
    `
      {
        entries(section: "tv", status: "live") {
          id
        }
      }
    `,
  )
  return tvEpisodes?.data?.data?.entries?.length
}

export const getAllTvEpisodes = async (offset: number, perPage?: number) => {
  const _perPage = perPage || PER_PAGE
  const tvEpisodes = await sendCraftRequest(
    `
      {
        entries(section: "tv", status: "live", limit: ${_perPage}, offset: ${offset}) {
          id
          uid
          title
          slug
          postDate
          number
          videoUrl
          body
          heroImage {
            id
            url
          }
          tvSeason {
            id
          }
          relatedArticles {
            id
          }
          relatedRecipes {
            id
          }
          pageTitle
          seoDescription
          seoImage {
            id
            url
          }
        }
      }
  `,
  )

  const value = tvEpisodes?.data?.data?.entries

  return value
}

export const formatTvEpisodes = async (tvEpisodes: any) => {
  const toUpload: any[] = []
  const slugHashmap: any = {}
  let linkSlugHashmap: any = {}
  let imageAssetsByCraftId: any = {}
  let imageIdsToUpload: any[] = []
  let slugsToSearchFor: any[] = []
  let referenceIdsToCheckIfExists: any = []
  let sanityIdHashmap: any = {}

  tvEpisodes.forEach((tvEpisode: any) => {
    // Images
    if (tvEpisode?.heroImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...tvEpisode.heroImage]
    }
    if (tvEpisode?.seoImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...tvEpisode?.seoImage]
    }

    // Links
    const bodyBlocks = getBlocksFromHtml(tvEpisode?.body, bodyType)
    const bodyLinks = getAllLinksFromLinkMarks(bodyBlocks, `TV Episode ${tvEpisode?.slug}`)
    slugsToSearchFor = [...slugsToSearchFor, ...bodyLinks]

    // References
    if (tvEpisode?.relatedRecipes?.length) {
      const itemsToAdd = tvEpisode.relatedRecipes.map((item: any) => getSanityIdFromCraftId(item.id, 'recipe'))
      referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd]
    }
  })

  if (referenceIdsToCheckIfExists.length) {
    sanityIdHashmap = await checkSanityForIds(referenceIdsToCheckIfExists)
  }

  if (slugsToSearchFor.length) {
    const { slugHashmap: _linkSlugHashmap, linksThatHaveNoReference } = await fetchDataByLinkObject(slugsToSearchFor)
    linkSlugHashmap = _linkSlugHashmap
    await writeLinksNotFoundToFile(linksThatHaveNoReference)
  }

  if (imageIdsToUpload.length) {
    imageAssetsByCraftId = await uploadImagesToSanity(imageIdsToUpload)
  }

  tvEpisodes.forEach((tvEpisode: any) => {
    if (tvEpisode?.id && !slugHashmap[tvEpisode?.slug] && tvEpisode?.title) {
      slugHashmap[tvEpisode?.slug] = true

      const image = getSanityImageArrayFromCraftImageHashmap(tvEpisode?.heroImage, imageAssetsByCraftId)[0]
      const openGraphImage = getSanityImageArrayFromCraftImageHashmap(tvEpisode?.seoImage, imageAssetsByCraftId)[0]

      let bodyBlocks = getBlocksFromHtml(tvEpisode?.body, bodyType)
      bodyBlocks = formatLinksInBlocks(bodyBlocks, linkSlugHashmap)

      const tvEpisodeRecord: any = {
        _id: getSanityIdFromCraftId(tvEpisode?.id, 'tvEpisode'),
        _type: 'tvEpisode',
        title: tvEpisode?.title,
        slug: {
          // _type: 'slug',
          current: normalizeSlug(tvEpisode?.slug, 'tvEpisode'),
        },
        publishedAt: tvEpisode?.postDate,
        number: tvEpisode?.number ? tvEpisode?.number : null,
        body: bodyBlocks,
        metaTitle: tvEpisode?.pageTitle ? tvEpisode?.pageTitle : null,
        metaDescription: tvEpisode?.seoDescription ? tvEpisode?.seoDescription : null,
        // relatedRecipes
        // relatedArticles
        craftData: {
          id: tvEpisode?.id,
          importedFromCraft: true,
        },
      }

      if (tvEpisode?.videoUrl) {
        const videoTypeAndId = getVideoTypeAndId(tvEpisode?.videoUrl)
        if (videoTypeAndId) {
          tvEpisodeRecord.video = {
            type: videoTypeAndId.type,
            youtubeId: videoTypeAndId.type === 'youtube' ? videoTypeAndId.id : null,
            vimeoId: videoTypeAndId.type === 'vimeo' ? videoTypeAndId.id : null,
            url: videoTypeAndId.type === 'url' ? videoTypeAndId.url : null,
          }
        }
      }

      if (tvEpisode?.tvSeason?.length) {
        const firstItem = tvEpisode?.tvSeason[0]
        if (firstItem?.id) {
          tvEpisodeRecord.season = {
            _type: 'reference',
            _ref: SEASON_HASHMAP[firstItem?.id as keyof typeof SEASON_HASHMAP]._id,
          }
        }
      }

      if (tvEpisode?.relatedRecipes?.length) {
        tvEpisodeRecord.relatedRecipes = tvEpisode.relatedRecipes
          .map((recipe: any, index: number) => {
            const itemInHashmap = sanityIdHashmap[getSanityIdFromCraftId(recipe.id, 'recipe')]
            if (itemInHashmap) {
              return {
                _type: 'reference',
                _ref: itemInHashmap._id,
                _key: `${itemInHashmap._id}-${index}`,
              }
            }

            return null
          })
          .filter((item: any) => item !== null)
      }

      if (image) {
        tvEpisodeRecord.image = image
      }

      if (openGraphImage) {
        tvEpisodeRecord.openGraphImage = openGraphImage
      }

      toUpload.push(tvEpisodeRecord)
    }
  })

  return toUpload
}

const createTvEpisodesInSanity = async (tvEpisodesFormatted: any) => {
  await patchAllDocsById(tvEpisodesFormatted)
}

const deleteAll = async () => {
  await unsetAndDeleteAllItems('tvEpisode', '*[_type == "tvEpisode" && craftData.importedFromCraft == true]', [
    'relatedRecipes',
    'relatedArticles',
  ])
}

const perPageFunction = async (page: number, totalPages: number, perPage?: number) => {
  const _perPage = perPage || PER_PAGE
  try {
    console.log(`Fetching Craft TV Episodes`)
    console.log(`(Items ${page * _perPage + 1} - ${page * _perPage + _perPage} of ${totalPages * _perPage})...`)
    const tvEpisodes = await getAllTvEpisodes(page * _perPage, _perPage)

    if (!tvEpisodes?.length) {
      console.log('No tv episodes found.')
      return
    } else {
      console.log(`Found ${tvEpisodes?.length} tv episodes.`)
    }
    console.log('Formatting tvEpisodes to schema...')
    const tvEpisodesFormatted = await formatTvEpisodes(tvEpisodes)
    console.log('Creating tvEpisodes in Sanity...')
    await createTvEpisodesInSanity(tvEpisodesFormatted)
    console.log(`Done page ${page + 1} of ${totalPages}!`)
    console.log(`===============================================`)
  } catch (error) {
    console.error('Error fetching tv episodes:', error)
    process.exit(1)
  }
}

async function main({ perPage }: { perPage?: number }) {
  const _perPage = perPage || PER_PAGE
  const totalTvEpisodes = await getTvEpisodesLength()
  const totalPages = Math.ceil(totalTvEpisodes / _perPage)

  for (let i = 0; i <= totalPages; i++) {
    // for (let i = 1; i <= 1; i++) {
    await perPageFunction(i, totalPages, _perPage)
  }
}

async function createDocumentsInSanity() {
  await createAllDocumentsFromCraft(
    `
      {
        entries(section: "tv", status: "live") {
          id
        }
      }
    `,
    'data.entries',
    'tvEpisode',
  )
}

// testRun()
// connectReferences()
// main({ perPage: 100 })

const moduleExports = {
  // connectReferences,
  createDocumentsInSanity,
  deleteAll,
  main,
}

export default moduleExports
