/* eslint-disable */

import { sendCraftRequest } from '../utils/craftCms'
import { getSanityImageArrayFromCraftImageHashmap, uploadImagesToSanity } from '../utils/image'
import {
  fetchDataByLinkObject,
  formatLinksInBlocks,
  getAllLinksFromLinkMarks,
  getBlocksFromHtml,
  wipeLinksNotFoundFile,
  writeLinksNotFoundToFile,
} from '../utils/richText'
import {
  createAllDocumentsFromCraft,
  getSanityClient,
  getSanityIdFromCraftId,
  patchAllDocsById,
  sanityIdToCraftId,
  unsetAndDeleteAllItems,
} from '../utils/sanity'
import { checkSanityForIds, htmlToPortableTextParagraph, normalizeSlug } from '../utils/utils'
import { htmlToBlocks } from '@portabletext/block-tools'
import { Schema } from '@sanity/schema'
import path from 'path'
import tvEpisode from './tvEpisode'
const { JSDOM } = require('jsdom')

// Import the schema using the correct relative path from website directory
const magazineIssueSchema = require(path.resolve(__dirname, '../../../cms/schemaTypes/documents/magazineIssue')).default
const linkObjectFile = require(path.resolve(__dirname, '../../../cms/schemaTypes/objects/link')).default

// Create a schema with the radio episode type
const compiledSchemas = new Schema({
  name: 'default',
  types: [magazineIssueSchema, linkObjectFile.linkNoLabel, linkObjectFile.link],
})

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const editorsNoteType = compiledSchemas
  .get('magazineIssue')
  .fields.find((field: any) => field.name === 'editorsNote').type

const TYPE_HANDLES_ALLOWED = ['articles', 'posts']
const PER_PAGE = 200

const getTotalLength = async () => {
  let items: any = await sendCraftRequest(
    `
      {
        entries(section: "articles", status: "live") {
          id
          typeHandle
        }
      }
    `,
  )

  items = items?.data?.data?.entries?.filter((item: any) => TYPE_HANDLES_ALLOWED.includes(item?.typeHandle))

  return items?.length
}

export const getAllItems = async (offset: number, perPage?: number) => {
  const _perPage = perPage || PER_PAGE
  let items: any = await sendCraftRequest(
    `
      {
        entries(section: "articles", status: "live", limit: ${_perPage}, offset: ${offset}) {
          typeHandle
          postDate
          id
          title
          slug
          subHeader
          updatedDate
          lead
          heroImage {
            id
            url
          }
          heroImageLink
          byline {
            id
          }
          credits
          sponsor {
            id
          }
          friendsOfMilkStreetSponsor {
            id
          }
          magazineIssue {
            id
          }
          magazineColumn {
            id
          }
          relatedRecipes {
            id
          }
          relatedEpisodes {
            id
          }   
          seoImage {
            id
            url
          }
          pageTitle
          seoDescription       
        }
      }
  `,
  )

  items = items?.data?.data?.entries?.filter((item: any) => TYPE_HANDLES_ALLOWED.includes(item?.typeHandle))

  const value = items

  return value
}

export const formatItems = async (items: any) => {
  const toUpload: any[] = []
  const slugHashmap: any = {}
  let linkSlugHashmap: any = {}
  let imageAssetsByCraftId: any = {}
  let imageIdsToUpload: any[] = []
  let slugsToSearchFor: any[] = []
  let referenceIdsToCheckIfExists: any = []
  let sanityIdHashmap: any = {}

  // postDate
  // id
  // title
  // slug
  // subHeader
  // updatedDate
  // lead
  // heroImage {
  //   id
  //   url
  // }
  // heroImageLink
  // byline {
  //   id
  // }
  // credits
  // sponsor {
  //   id
  // }
  // friendsOfMilkStreetSponsor {
  //   id
  // }
  // magazineIssue {
  //   id
  // }
  // magazineColumn {
  //   id
  // }
  // relatedRecipes {
  //   id
  // }
  // relatedEpisodes {
  //   id
  // }
  // seoImage
  // pageTitle
  // seoDescription

  items.forEach(async (item: any) => {
    // Images
    if (item?.heroImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...item.heroImage]
    }

    if (item?.seoImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...item.seoImage]
    }

    // References
    if (item?.relatedRecipes?.length) {
      const itemsToAdd = item.relatedRecipes.map((item: any) => getSanityIdFromCraftId(item.id, 'recipe'))
      referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd]
    }

    if (item?.byline?.length) {
      const itemsToAdd = item.byline.map((item: any) => getSanityIdFromCraftId(item.id, 'person'))
      referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd]
    }

    if (item?.sponsor?.length) {
      const itemsToAdd = item.sponsor.map((item: any) => getSanityIdFromCraftId(item.id, 'subcategory'))
      referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd]
    }

    if (item?.friendsOfMilkStreetSponsor?.length) {
      const itemsToAdd = item.friendsOfMilkStreetSponsor.map((item: any) =>
        getSanityIdFromCraftId(item.id, 'subcategory'),
      )
      referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd]
    }

    if (item?.magazineColumn?.length) {
      const itemsToAdd = item.magazineColumn.map((item: any) => getSanityIdFromCraftId(item.id, 'subcategory'))
      referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd]
    }

    if (item?.relatedEpisodes?.length) {
      const itemsToAdd1 = item.relatedEpisodes.map((item: any) => getSanityIdFromCraftId(item.id, 'tvEpisode'))
      const itemsToAdd2 = item.relatedEpisodes.map((item: any) => getSanityIdFromCraftId(item.id, 'radioEpisode'))
      referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd1, ...itemsToAdd2]
    }
  })

  if (referenceIdsToCheckIfExists.length) {
    sanityIdHashmap = await checkSanityForIds(referenceIdsToCheckIfExists)
  }

  if (slugsToSearchFor.length) {
    const { slugHashmap: _linkSlugHashmap, linksThatHaveNoReference } = await fetchDataByLinkObject(slugsToSearchFor)
    linkSlugHashmap = _linkSlugHashmap
    await writeLinksNotFoundToFile(linksThatHaveNoReference)
  }

  if (imageIdsToUpload.length) {
    imageAssetsByCraftId = await uploadImagesToSanity(imageIdsToUpload)
  }

  // Record
  items.forEach(async (item: any) => {
    const isValidType = TYPE_HANDLES_ALLOWED.includes(item?.typeHandle)

    let postType = null
    if (item?.typeHandle === 'posts') {
      postType = 'blogPost'
    } else if (item?.typeHandle === 'articles') {
      postType = 'article'
    }

    if (item?.id && item?.title && postType) {
      slugHashmap[item?.slug] = true

      const image = getSanityImageArrayFromCraftImageHashmap(item?.heroImage, imageAssetsByCraftId)[0]
      const openGraphImage = getSanityImageArrayFromCraftImageHashmap(item?.seoImage, imageAssetsByCraftId)[0]

      const authors = item?.byline?.length
        ? item?.byline
            .map((item: any, index: number) => {
              if (sanityIdHashmap[getSanityIdFromCraftId(item.id, 'person')]) {
                return {
                  _type: 'reference',
                  _ref: getSanityIdFromCraftId(item.id, 'person'),
                  _key: `${item.id}-${item?.id}-${index}`,
                }
              } else {
                console.log(`Author not found: ${item.id}`)
                return null
              }
            })
            .filter((item: any) => item !== null)
        : []

      const sponsors = item?.sponsor?.length
        ? item?.sponsor
            .map((item: any, index: number) => {
              if (sanityIdHashmap[getSanityIdFromCraftId(item.id, 'subcategory')]) {
                return {
                  _type: 'reference',
                  _ref: getSanityIdFromCraftId(item.id, 'subcategory'),
                  _key: `${item.id}-${item?.id}-${index}`,
                }
              } else {
                console.log(`Sponsor not found: ${item.id}`)
                return null
              }
            })
            .filter((item: any) => item !== null)
        : []

      const friendsOfMilkStreetSponsors = item?.friendsOfMilkStreetSponsor?.length
        ? item?.friendsOfMilkStreetSponsor
            .map((item: any, index: number) => {
              if (sanityIdHashmap[getSanityIdFromCraftId(item.id, 'subcategory')]) {
                return {
                  _type: 'reference',
                  _ref: getSanityIdFromCraftId(item.id, 'subcategory'),
                  _key: `${item.id}-${item?.id}-${index}`,
                }
              } else {
                console.log(`Friend of Milk Street Sponsor not found: ${item.id}`)
                return null
              }
            })
            .filter((item: any) => item !== null)
        : []

      const relatedRecipes = item?.relatedRecipes?.length
        ? item?.relatedRecipes
            .map((item: any, index: number) => {
              if (sanityIdHashmap[getSanityIdFromCraftId(item.id, 'recipe')]) {
                return {
                  _type: 'reference',
                  _ref: getSanityIdFromCraftId(item.id, 'recipe'),
                  _key: `${item.id}-${item?.id}-${index}`,
                }
              } else {
                console.log(`Related Recipe not found: ${item.id}`)
                return null
              }
            })
            .filter((item: any) => item !== null)
        : []

      const magazineColumn = item?.magazineColumn?.length
        ? item?.magazineColumn
            .map((item: any, index: number) => {
              if (sanityIdHashmap[getSanityIdFromCraftId(item.id, 'subcategory')]) {
                return {
                  _type: 'reference',
                  _ref: getSanityIdFromCraftId(item.id, 'subcategory'),
                  _key: `${item.id}-${item?.id}-${index}`,
                }
              } else {
                console.log(`Magazine Column not found: ${item.id}`)
                return null
              }
            })
            .filter((item: any) => item !== null)
        : []

      const relatedEpisodes = item?.relatedEpisodes?.length
        ? item?.relatedEpisodes
            .map((item: any, index: number) => {
              let foundType = null

              if (sanityIdHashmap[getSanityIdFromCraftId(item.id, 'tvEpisode')]) {
                foundType = 'tvEpisode'
              }

              if (sanityIdHashmap[getSanityIdFromCraftId(item.id, 'radioEpisode')]) {
                foundType = 'radioEpisode'
              }

              if (foundType) {
                return {
                  _type: 'reference',
                  _ref: getSanityIdFromCraftId(item.id, foundType),
                  _key: `${item.id}-${item?.id}-${index}`,
                }
              } else {
                console.log(`Radio/TV Episode not found: ${item.id}`)
                return null
              }
            })
            .filter((item: any) => item !== null)
        : []

      const itemRecord: any = {
        _id: getSanityIdFromCraftId(item?.id, 'article'),
        _type: 'article',
        postType,
        title: item?.title,
        subheader: item?.subHeader ? item?.subHeader : null,
        lead: item?.lead ? item?.lead : null,
        slug: {
          // _type: 'slug',
          current: normalizeSlug(item?.slug, 'article'),
        },
        publishedAt: item?.postDate ? item?.postDate : null,
        updatedAt: item?.updatedDate ? item?.updatedDate : null,
        metaTitle: item?.pageTitle ? item?.pageTitle : null,
        metaDescription: item?.seoDescription ? item?.seoDescription : null,
        authors,
        sponsors,
        friendsOfMilkStreetSponsors,
        relatedRecipes,
        credits: item?.credits ? item?.credits : null,
        magazineColumn,
        relatedEpisodes,
        craftData: {
          id: item?.id,
          importedFromCraft: true,
        },
      }

      if (item?.heroImageLink) {
        itemRecord.imageLink = {
          linkType: 'external',
          link: item?.heroImageLink,
        }
      }

      if (openGraphImage) {
        itemRecord.openGraphImage = openGraphImage
      }

      if (image) {
        itemRecord.image = image
      }

      toUpload.push(itemRecord)
    }
  })

  return toUpload
}

const updateItemsInSanity = async (itemsFormatted: any) => {
  await patchAllDocsById(itemsFormatted)
}

const perPageFunction = async (page: number, totalPages: number, perPage?: number) => {
  const _perPage = perPage || PER_PAGE
  try {
    console.log(`Fetching Craft Articles`)
    console.log(`(Items ${page * _perPage + 1} - ${page * _perPage + _perPage} of ${totalPages * _perPage})...`)
    const items = await getAllItems(page * _perPage, _perPage)

    if (!items?.length) {
      console.log('No articles found.')
      return
    } else {
      console.log(`Found ${items?.length} articles.`)
    }
    console.log('Formatting articles to schema...')
    const itemsFormatted = await formatItems(items)
    console.log('Creating articles in Sanity...')
    await updateItemsInSanity(itemsFormatted)
    console.log(`Done page ${page + 1} of ${totalPages}!`)
    console.log(`===============================================`)
  } catch (error) {
    console.error('Error fetching articles:', error)
    process.exit(1)
  }
}

async function main({ perPage }: { perPage?: number }) {
  const _perPage = perPage || PER_PAGE
  const totalItems = await getTotalLength()
  const totalPages = Math.ceil(totalItems / _perPage)

  console.log(`Total items: ${totalItems}`)

  for (let i = 0; i <= totalPages; i++) {
    // for (let i = 1; i <= 1; i++) {
    await perPageFunction(i, totalPages, _perPage)
  }
}

async function createDocumentsInSanity() {
  await createAllDocumentsFromCraft(
    `
      {
        entries(section: "articles", status: "live") {
          typeHandle
          id
        }
      }
    `,
    'data.entries',
    'article',
    items => {
      return items.filter((item: any) => TYPE_HANDLES_ALLOWED.includes(item?.typeHandle))
    },
  )
}

const deleteAll = async () => {
  const query = `*[_type == "article" && craftData.importedFromCraft == true]`
  // const query = `*[_type == "article" && title != "Article Title"]`
  await unsetAndDeleteAllItems('article', query, [
    'authors',
    'sponsors',
    'friendsOfMilkStreetSponsors',
    'relatedRecipes',
    'magazineColumn',
    'relatedEpisodes',
  ])
}

// deleteAll()
// createDocumentsInSanity()
// main({ perPage: 5 })
// createDocumentsInSanity()

const moduleExports = {
  createDocumentsInSanity,
  main,
  deleteAll,
}

export default moduleExports
