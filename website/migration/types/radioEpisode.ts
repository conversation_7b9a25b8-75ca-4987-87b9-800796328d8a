/* eslint-disable */

import { sendCraftRequest } from '../utils/craftCms'
import { getSanityImageArrayFromCraftImageHashmap, uploadImagesToSanity } from '../utils/image'
import {
  fetchDataByLinkObject,
  formatLinksInBlocks,
  getAllLinksFromLinkMarks,
  getBlocksFromHtml,
  wipeLinksNotFoundFile,
  writeLinksNotFoundToFile,
} from '../utils/richText'
import {
  createAllDocumentsFromCraft,
  getSanityClient,
  getSanityIdFromCraftId,
  patchAllDocsById,
  unsetAndDeleteAllItems,
} from '../utils/sanity'
import { checkSanityForIds, htmlToPortableTextParagraph, normalizeSlug } from '../utils/utils'
import { htmlToBlocks } from '@portabletext/block-tools'
import { Schema } from '@sanity/schema'
import path from 'path'
import tvEpisode from './tvEpisode'
const { JSDOM } = require('jsdom')

// Import the schema using the correct relative path from website directory
const radioEpisodeSchema = require(path.resolve(__dirname, '../../../cms/schemaTypes/documents/radioEpisode')).default
const linkObjectFile = require(path.resolve(__dirname, '../../../cms/schemaTypes/objects/link')).default

// Create a schema with the radio episode type
const compiledSchemas = new Schema({
  name: 'default',
  types: [radioEpisodeSchema, linkObjectFile.linkNoLabel, linkObjectFile.link],
})

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const legacyBodyType = compiledSchemas.get('radioEpisode').fields.find((field: any) => field.name === 'legacyBody').type
const transcriptType = compiledSchemas.get('radioEpisode').fields.find((field: any) => field.name === 'transcript').type

// const legacyBodyType = radioEpisodeSchemaCompiled
//   .get('blogPost')
//   .fields.find((field) => field.name === 'body').type

const PER_PAGE = 200

const getRadioEpisodesLength = async () => {
  const radioEpisodes = await sendCraftRequest(
    `
      {
        entries(section: "radio", status: "live") {
          id
        }
      }
    `,
  )
  return radioEpisodes?.data?.data?.entries?.length
}

export const getAllRadioEpisodes = async (offset: number, perPage?: number) => {
  const _perPage = perPage || PER_PAGE
  const radioEpisodes = await sendCraftRequest(
    `
      {
        entries(section: "radio", status: "live", limit: ${_perPage}, offset: ${offset}) {
          postDate
          id
          title
          slug
          subHeader
          body
          heroImage {
            id
            url
          }
          number
          podcastUrl
          iTunesUrl
          spotifyUrl
          tuneInUrl
          amazonMusicUrl
          podcastTranscript
          originalEpisode {
            id
          }
          relatedRecipes {
            id
          }
          globalTags
          pageTitle
          seoDescription
          seoImage {
            id
            url
          }
        }
      }
  `,
  )

  const value = radioEpisodes?.data?.data?.entries

  return value
}

export const formatRadioEpisodes = async (radioEpisodes: any) => {
  const toUpload: any[] = []
  const slugHashmap: any = {}
  let linkSlugHashmap: any = {}
  let imageAssetsByCraftId: any = {}
  let imageIdsToUpload: any[] = []
  let slugsToSearchFor: any[] = []
  let referenceIdsToCheckIfExists: any = []
  let sanityIdHashmap: any = {}

  radioEpisodes.forEach(async (radioEpisode: any) => {
    // Images
    if (radioEpisode?.heroImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...radioEpisode.heroImage]
    }
    if (radioEpisode?.seoImage?.length) {
      imageIdsToUpload = [...imageIdsToUpload, ...radioEpisode?.seoImage]
    }

    // Links
    const legacyBodyBlocks = getBlocksFromHtml(radioEpisode?.body, legacyBodyType)
    const legacyBodyLinks = getAllLinksFromLinkMarks(legacyBodyBlocks, `Radio Episode ${radioEpisode?.slug}`)
    slugsToSearchFor = [...slugsToSearchFor, ...legacyBodyLinks]

    // References
    if (radioEpisode?.relatedRecipes?.length) {
      const itemsToAdd = radioEpisode.relatedRecipes.map((item: any) => getSanityIdFromCraftId(item.id, 'recipe'))
      referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd]
    }
    if (radioEpisode?.originalEpisode?.length) {
      const itemsToAdd = radioEpisode.originalEpisode.map((item: any) =>
        getSanityIdFromCraftId(item.id, 'radioEpisode'),
      )
      referenceIdsToCheckIfExists = [...referenceIdsToCheckIfExists, ...itemsToAdd]
    }
  })

  if (referenceIdsToCheckIfExists.length) {
    sanityIdHashmap = await checkSanityForIds(referenceIdsToCheckIfExists)
  }

  if (slugsToSearchFor.length) {
    const { slugHashmap: _linkSlugHashmap, linksThatHaveNoReference } = await fetchDataByLinkObject(slugsToSearchFor)
    linkSlugHashmap = _linkSlugHashmap
    await writeLinksNotFoundToFile(linksThatHaveNoReference)
  }

  if (imageIdsToUpload.length) {
    imageAssetsByCraftId = await uploadImagesToSanity(imageIdsToUpload)
  }

  // Record
  radioEpisodes.forEach(async (radioEpisode: any) => {
    if (radioEpisode?.id === 2525911) {
      console.log(radioEpisode)
    }

    if (radioEpisode?.id && radioEpisode?.title) {
      slugHashmap[radioEpisode?.slug] = true

      const image = getSanityImageArrayFromCraftImageHashmap(radioEpisode?.heroImage, imageAssetsByCraftId)[0]
      const openGraphImage = getSanityImageArrayFromCraftImageHashmap(radioEpisode?.seoImage, imageAssetsByCraftId)[0]

      let legacyBodyBlocks = getBlocksFromHtml(radioEpisode?.body, legacyBodyType)
      legacyBodyBlocks = formatLinksInBlocks(legacyBodyBlocks, linkSlugHashmap)

      let transcriptBlocks: any[] = []
      if (radioEpisode?.transcript) {
        transcriptBlocks = htmlToBlocks(radioEpisode?.transcript, transcriptType, {
          parseHtml: html => new JSDOM(html).window.document,
        })
      }

      const radioEpisodeRecord: any = {
        _id: getSanityIdFromCraftId(radioEpisode?.id, 'radioEpisode'),
        _type: 'radioEpisode',
        title: radioEpisode?.title,
        slug: {
          // _type: 'slug',
          current: normalizeSlug(radioEpisode?.slug, 'radioEpisode'),
        },
        publishedAt: radioEpisode?.postDate,
        summary: radioEpisode?.subHeader ? htmlToPortableTextParagraph(radioEpisode?.subHeader) : [],
        episodeNumber: radioEpisode?.number ? `${radioEpisode?.number}` : null,
        audioUrl: radioEpisode?.podcastUrl ? radioEpisode?.podcastUrl : null,
        useLegacyBody: true,
        legacyBody: legacyBodyBlocks,
        iTunesUrl: radioEpisode?.iTunesUrl,
        tuneInUrl: radioEpisode?.tuneInUrl,
        spotifyUrl: radioEpisode?.spotifyUrl,
        pandoraUrl: radioEpisode?.pandoraUrl,
        amazonMusicUrl: radioEpisode?.amazonMusicUrl,
        transcript: transcriptBlocks,
        metaTitle: radioEpisode?.pageTitle ? radioEpisode?.pageTitle : null,
        metaDescription: radioEpisode?.seoDescription ? radioEpisode?.seoDescription : null,
        // originalEpisode
        // relatedRecipes
        craftData: {
          id: radioEpisode?.id,
          importedFromCraft: true,
        },
      }

      if (radioEpisode?.relatedRecipes?.length) {
        radioEpisodeRecord.relatedRecipes = radioEpisode.relatedRecipes
          .map((recipe: any, index: number) => {
            const itemInHashmap = sanityIdHashmap[getSanityIdFromCraftId(recipe.id, 'recipe')]

            if (itemInHashmap) {
              return {
                _type: 'reference',
                _ref: itemInHashmap._id,
                _key: `${itemInHashmap._id}-${index}`,
              }
            }

            return null
          })
          .filter((item: any) => item !== null)
      }

      if (radioEpisode?.originalEpisode[0]) {
        const itemInHashmap =
          sanityIdHashmap[getSanityIdFromCraftId(radioEpisode?.originalEpisode[0].id, 'radioEpisode')]
        if (itemInHashmap) {
          radioEpisodeRecord.originalEpisode = {
            _type: 'reference',
            _ref: itemInHashmap._id,
          }
        }
      }

      if (image) {
        radioEpisodeRecord.image = image
      }

      if (openGraphImage) {
        radioEpisodeRecord.openGraphImage = openGraphImage
      }

      toUpload.push(radioEpisodeRecord)
    }
  })

  return toUpload
}

const createRadioEpisodesInSanity = async (radioEpisodesFormatted: any) => {
  await patchAllDocsById(radioEpisodesFormatted)
}

const deleteAll = async () => {
  const query = `*[_type == "radioEpisode" && craftData.importedFromCraft == true]`
  await unsetAndDeleteAllItems('radioEpisode', query, ['relatedRecipes', 'originalEpisode'])
}

const perPageFunction = async (page: number, totalPages: number, perPage?: number) => {
  const _perPage = perPage || PER_PAGE
  try {
    console.log(`Fetching Craft Radio Episodes`)
    console.log(`(Items ${page * _perPage + 1} - ${page * _perPage + _perPage} of ${totalPages * _perPage})...`)
    const radioEpisodes = await getAllRadioEpisodes(page * _perPage, _perPage)

    if (!radioEpisodes?.length) {
      console.log('No radio episodes found.')
      return
    } else {
      console.log(`Found ${radioEpisodes?.length} radio episodes.`)
    }
    console.log('Formatting radioEpisodes to schema...')
    const radioEpisodesFormatted = await formatRadioEpisodes(radioEpisodes)
    console.log('Creating Radio Episodes in Sanity...')
    await createRadioEpisodesInSanity(radioEpisodesFormatted)
    console.log(`Done page ${page + 1} of ${totalPages}!`)
    console.log(`===============================================`)
  } catch (error) {
    console.error('Error fetching radio episodes:', error)
    process.exit(1)
  }
}

async function main({ perPage }: { perPage?: number }) {
  const _perPage = perPage || PER_PAGE
  const totalRadioEpisodes = await getRadioEpisodesLength()
  const totalPages = Math.ceil(totalRadioEpisodes / _perPage)

  for (let i = 0; i <= totalPages; i++) {
    // for (let i = 1; i <= 1; i++) {
    await perPageFunction(i, totalPages, _perPage)
  }
}

async function createDocumentsInSanity() {
  await createAllDocumentsFromCraft(
    `
      {
        entries(section: "radio", status: "live") {
          id
        }
      }
    `,
    'data.entries',
    'radioEpisode',
  )
}

// testRun()
// deleteAll()
// main({ perPage: 100 })
// connectReferences()

const moduleExports = {
  createDocumentsInSanity,
  deleteAll,
  main,
}

export default moduleExports
