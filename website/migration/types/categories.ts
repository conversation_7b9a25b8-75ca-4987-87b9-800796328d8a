/* eslint-disable */

import { sendCraftRequest } from '../utils/craftCms'
import {
  checkIfIdExistsInSanity,
  createAllDocumentsFromCraft,
  getSanityClient,
  getSanityIdFromCraftId,
  patchAllDocsById,
  unsetAndDeleteAllItems,
} from '../utils/sanity'
import { normalizeSlug, stringToSentenceCase, wait } from '../utils/utils'

const { default: PQueue } = require('p-queue')
const queue = new PQueue({
  concurrency: 1,
  interval: 100,
})

export const getAllCategories = async () => {
  const categories = await sendCraftRequest(
    `
      {
        categories {
          id
          uid
          dateCreated	
          dateUpdated
          title
          slug
          groupId
          groupHandle
        }
      }
  `,
  )

  const value = categories?.data?.data?.categories

  return value
}

export const formatParentCategories = (categories: any) => {
  const hashmap: any = {}

  categories.forEach((category: any) => {
    if (category?.groupId && category?.groupHandle && !hashmap[category?.groupId]) {
      hashmap[category?.groupId] = {
        _id: getSanityIdFromCraftId(category?.groupId, 'category'),
        _type: 'category',
        slug: {
          current: normalizeSlug(category?.groupHandle, 'category'),
        },
        title: stringToSentenceCase(category?.groupHandle),
        craftData: {
          id: `${category?.groupId}`,
          importedFromCraft: true,
        },
      }
    }
  })

  return Object.values(hashmap)
}

const createParentCategoriesInSanity = async (categories: any) => {
  await patchAllDocsById(categories)
}

const formatChildCategories = (categories: any) => {
  const toUpload: any[] = []

  categories.forEach((category: any, index: number) => {
    if (category?.id) {
      const value = {
        _id: getSanityIdFromCraftId(category?.id, 'subcategory'),
        _type: 'subcategory',
        slug: {
          current: normalizeSlug(category?.slug, 'subcategory'),
        },
        title: category?.title,
        parentCategory: {
          _type: 'reference',
          _ref: getSanityIdFromCraftId(category?.groupId, 'category'),
        },
        craftData: {
          id: `${category?.id}`,
          groupId: `${category?.groupId}`,
          groupHandle: category?.groupHandle,
          importedFromCraft: true,
        },
      }

      toUpload.push(value)
    }
  })

  return toUpload
}

const createChildCategoriesInSanity = async (categories: any) => {
  await patchAllDocsById(categories)
}

const deleteAll = async () => {
  await unsetAndDeleteAllItems(
    'category/subcategory',
    `*[(_type == "category" || _type == "subcategory") && craftData.importedFromCraft == true]`,
  )
}

async function main() {
  try {
    console.log('Fetching categories...')
    const categories = await getAllCategories()
    console.log('Formatting parent categories...')
    const parentCategories = formatParentCategories(categories)
    console.log('Creating/Updating parent categories...')
    await createParentCategoriesInSanity(parentCategories)
    console.log('Parent categories created/updated!')
    console.log('Formatting child categories...')
    const childCategories = formatChildCategories(categories)
    console.log('Creating/Updating child categories...')
    await createChildCategoriesInSanity(childCategories)
    console.log('Child categories created/updated!')
    console.log('Done!')
  } catch (error) {
    console.error('Error fetching categories:', error)
    process.exit(1)
  }
}

// main()

const moduleExports = {
  deleteAll,
  main,
}

export default moduleExports
