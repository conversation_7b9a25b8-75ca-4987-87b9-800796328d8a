import { ClientConfig, createClient } from 'next-sanity'
import imageUrlBuilder from '@sanity/image-url'
import { SanityImageSource } from '@sanity/image-url/lib/types/types'
import { SiteSettings } from '@/types'

// Content Type Queries
import { getArticleQuery } from './queries/documents/article'
import { categoryQuery } from './queries/documents/category'
import { getClassQuery } from './queries/documents/class'
import { getPageQuery } from './queries/documents/page'
import { getProductQuery } from './queries/documents/product'
import { getRadioEpisodeQuery } from './queries/documents/radioEpisode'
import { getRecipeQuery } from './queries/documents/recipe'
import { getTvEpisodeQuery } from './queries/documents/tvEpisode'
import { getPageSectionExampleQuery } from './queries/documents/pageSectionExample'

// Site Settings and Layout Queries
import { siteSettingsQuery, headerNavQuery } from './queries/siteSettings'
import { DOC_TYPES, SEARCH_PER_PAGE } from '.'
import { getMagazineIssueQuery } from './queries/documents/magazine'
import { cardFields } from './queries/_shared'
import { formatFilteredCategoriesToOperatorsAndIds } from '@/lib/utils'

// ==========================================
// Client Configuration
// ==========================================

// Determine the perspective based on environment and preview token
let perspective = process.env.SANITY_PREVIEW_TOKEN === undefined ? 'published' : 'drafts'
if (process.env.NODE_ENV === 'development') {
  perspective = 'raw'
}

const SANITY_CONFIG: ClientConfig = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION,
  useCdn: process.env.SANITY_PREVIEW_TOKEN === undefined ? true : false,
  token: process.env.SANITY_PREVIEW_TOKEN,
  perspective: perspective as 'raw' | 'published' | 'previewDrafts',
}

export const client = createClient(SANITY_CONFIG)

// Initialize image URL builder
export const imageBuilder = imageUrlBuilder(client)

export function urlFor(source: SanityImageSource) {
  return imageBuilder.image(source)
}

/**
 * Generate a URL for a Sanity image
 * @param source The Sanity image source
 * @param width Optional width for the image
 * @param height Optional height for the image
 * @returns The URL for the image
 */
export function urlForImage(source: SanityImageSource, width?: number, height?: number) {
  const builder = imageBuilder.image(source)
  if (width) builder.width(width)
  if (height) builder.height(height)
  return builder.url()
}

/**
 * Generate a URL for a Sanity image with specific dimensions
 * @param source The Sanity image source
 * @param width Width for the image
 * @param height Height for the image
 * @returns The URL for the image
 */
export function urlForImageWithDimensions(source: SanityImageSource, width: number, height: number) {
  return imageBuilder.image(source).width(width).height(height).url()
}

// ==========================================
// Cache Configuration
// ==========================================

const revalidateDefaultPeriod = parseInt(process.env.REVALIDATE || '604800') // 1 week revalidation
const revalidate = process.env.NODE_ENV !== 'production' ? 0 : revalidateDefaultPeriod

// ==========================================
// Content Type Fetching Functions
// ==========================================

interface FetchOptions {
  isPreview?: boolean
  tags?: string[]
  fetchAll?: boolean
}

/**
 * Generic fetch function with preview support and caching
 */
async function fetchWithOptions<T>(
  query: string,
  params: Record<string, unknown> = {},
  options: FetchOptions = {},
): Promise<T> {
  const { isPreview = false, tags = [] } = options

  try {
    const result = await client.fetch(
      query,
      { ...params, isPreview },
      {
        next: {
          revalidate,
          tags,
        },
      },
    )

    return result
  } catch (error) {
    console.error('Fetch error:', error)
    throw error
  }
}

export async function getArticle(slug?: string, options: FetchOptions = {}) {
  try {
    const variables = slug ? { slug } : {}
    const result = await fetchWithOptions(getArticleQuery({ all: options.fetchAll }), variables, {
      ...options,
      tags: ['article', `article:${slug}`],
    })

    if (!result) {
      // eslint-disable-next-line no-console
      console.log('No article found for slug:', slug)
    }

    return result
  } catch (error) {
    console.error('Error fetching article:', error)
    throw error
  }
}

export async function getCategory(slug: string, options: FetchOptions = {}) {
  return fetchWithOptions(
    categoryQuery,
    { slug },
    {
      ...options,
      tags: ['category', `category:${slug}`],
    },
  )
}

export async function getClass(slug: string, options: FetchOptions = {}) {
  return fetchWithOptions(
    getClassQuery({ all: options.fetchAll }),
    { slug },
    {
      ...options,
      tags: ['class', `class:${slug}`],
    },
  )
}

export async function getPage(slug?: string, options: FetchOptions = {}) {
  try {
    const variables = slug ? { slug } : {}
    const result = await fetchWithOptions(getPageQuery({ all: options.fetchAll }), variables, {
      ...options,
      tags: ['page', `page:${slug}`],
    })

    if (!result) {
      // eslint-disable-next-line no-console
      console.log('No page found for slug:', slug)
    }

    return result
  } catch (error) {
    console.error('Error fetching page:', error)
    throw error
  }
}

export async function getPageSectionExample(slug?: string, options: FetchOptions = {}) {
  try {
    const variables = slug ? { slug } : {}
    const result = await fetchWithOptions(getPageSectionExampleQuery({ all: options.fetchAll }), variables, {
      ...options,
      tags: ['pageSectionExample', `pageSectionExample:${slug}`],
    })

    if (!result && !slug) {
      // eslint-disable-next-line no-console
      console.log('No page section example found for slug:', slug)
    }

    return result
  } catch (error) {
    console.error('Error fetching page section example:', error)
    throw error
  }
}

export async function getProduct(slug: string, options: FetchOptions = {}) {
  return fetchWithOptions(
    getProductQuery({ all: options.fetchAll }),
    { slug },
    {
      ...options,
      tags: ['product', `product:${slug}`],
    },
  )
}

export async function getRadioEpisode(slug?: string, options: FetchOptions = {}) {
  try {
    const variables = slug ? { slug } : {}
    const result = await fetchWithOptions(getRadioEpisodeQuery({ all: options.fetchAll }), variables, {
      ...options,
      tags: ['radioEpisode', `radioEpisode:${slug}`],
    })

    if (!result && !slug) {
      // eslint-disable-next-line no-console
      console.log('No radio episode found for slug:', slug)
    }

    return result
  } catch (error) {
    console.error('Error fetching radio episode:', error)
    throw error
  }
}

export async function getMagazineIssue(slug?: string, options: FetchOptions = {}) {
  try {
    const variables = slug ? { slug } : {}
    const result = await fetchWithOptions(getMagazineIssueQuery({ all: options.fetchAll }), variables, {
      ...options,
      tags: ['magazineIssue', `magazineIssue:${slug}`],
    })

    if (!result && !slug) {
      // eslint-disable-next-line no-console
      console.log('No magazine issue found for slug:', slug)
    }

    return result
  } catch (error) {
    console.error('Error fetching magazine issue:', error)
    throw error
  }
}

export async function getRecipe(slug?: string, options: FetchOptions = {}): Promise<SanityRecipe | null> {
  try {
    const variables = slug ? { slug } : {}
    const result = await fetchWithOptions<SanityRecipe>(getRecipeQuery({ all: options.fetchAll }), variables, {
      ...options,
      tags: ['recipe', `recipe:${slug}`],
    })

    if (!result) {
      // eslint-disable-next-line no-console
      console.log('No recipe found for slug:', slug)
      return null
    }

    return result
  } catch (error) {
    console.error('Error fetching recipe:', error)
    throw error
  }
}

export async function getTvEpisode(slug?: string, options: FetchOptions = {}) {
  try {
    const variables = slug ? { slug } : {}
    const result = await fetchWithOptions(getTvEpisodeQuery({ all: options.fetchAll }), variables, {
      ...options,
      tags: ['tvEpisode', `tvEpisode:${slug}`],
    })

    if (!result) {
      // eslint-disable-next-line no-console
      console.log('No tv episode found for slug:', slug)
      return null
    }

    return result
  } catch (error) {
    console.error('Error fetching tv episode:', error)
    throw error
  }
}

export async function getSearchBarContent(
  options: {
    typesToQuery?: string[]
    offset?: number
    limit?: number
    categoryFilters?: SanitySearchBarFilterData
  } = {
    typesToQuery: [],
    offset: 0,
    limit: SEARCH_PER_PAGE,
    categoryFilters: {},
  },
) {
  if (!options?.typesToQuery) {
    throw new Error('No typesToQuery supplied to getSearchBarContent')
  }

  if (options?.offset === undefined || options?.offset === null) {
    throw new Error('No offset supplied to getSearchBarContent')
  }

  let mainQuery = '_type in $typesToQuery'

  if (options?.categoryFilters) {
    if (Object.keys(options?.categoryFilters).length > 0) {
      const formattedByOperatorAndIds = formatFilteredCategoriesToOperatorsAndIds(options.categoryFilters)

      const queries: string[] = []
      formattedByOperatorAndIds.forEach(item => {
        if (item.operator === 'AND' && !!item?.ids?.length) {
          queries.push(`(${item.ids.map(id => `references("${id}")`).join(' && ')})`)
        } else if (item.operator === 'OR' && !!item?.ids?.length) {
          queries.push(`(${item.ids.map(id => `references("${id}")`).join(' || ')})`)
        } else if (item.operator === 'TEXT_SEARCH' && item?.text) {
          // Need to specify field type because we can't have the query become too
          // large and unnecessary
          const fieldsToQuery = [
            {
              field: 'title',
              type: 'string',
            },
            {
              field: 'summary',
              type: ['string', 'portableText'],
            },
            {
              field: 'description',
              type: ['string', 'portableText'],
            },
            {
              field: 'lead',
              type: 'string',
            },
          ]

          const matchQueries: string[] = []

          fieldsToQuery.forEach(field => {
            const typeAsArray = Array.isArray(field.type) ? field.type : [field.type]
            typeAsArray.forEach(type => {
              if (type === 'string') {
                matchQueries.push(`${field.field} match "*${item.text}*"`)
              } else if (type === 'portableText') {
                matchQueries.push(`pt::text(${field.field}) match "*${item.text}*"`)
              }
            })
          })

          if (matchQueries.length > 0) {
            queries.push(`(${matchQueries.join(' || ')})`)
          }
        }
      })

      if (queries.length > 0) {
        mainQuery = `${mainQuery} && (${queries.join(' && ')})`
      }
    }
  }

  const query = `
    {
      "totalCount": count(*[${mainQuery}]),
      "items": *[${mainQuery}] | order(publishedAt asc)[$offset...$limit] {
        ${cardFields}
      }
    }
  `

  const queryAsTag = decodeURIComponent(mainQuery)
    .replace(/[^a-zA-Z0-9-]/g, '')
    .slice(0, 256)

  try {
    const result = await fetchWithOptions(
      query,
      {
        typesToQuery: options.typesToQuery ? options.typesToQuery : [],
        offset: options.offset ? options.offset : 0,
        limit: options.limit ? options.limit : SEARCH_PER_PAGE,
      },
      {
        tags: ['searchBarContent', `searchBarContent:${queryAsTag}`],
      },
    )

    if (!result) {
      // eslint-disable-next-line no-console
      console.log('No search bar content found')
      return null
    }

    return result
  } catch (error) {
    console.error('Error fetching search bar content:', error)
    throw error
  }
}

// ==========================================
// Site Settings and Layout Functions
// ==========================================

/**
 * Fetch all site settings with caching
 */
export const getSiteSettings = async (options: FetchOptions = {}): Promise<SiteSettings> => {
  return fetchWithOptions<SiteSettings>(
    siteSettingsQuery,
    {},
    {
      ...options,
      tags: ['siteSettings'],
    },
  )
}

/**
 * Fetch header navigation data
 */
export async function getHeaderNav(options: FetchOptions = {}) {
  return fetchWithOptions(
    headerNavQuery,
    {},
    {
      ...options,
      tags: ['navigation', 'headerNav'],
    },
  )
}

export const getPageDataByType = {
  [DOC_TYPES.PAGE]: getPage,
  [DOC_TYPES.RECIPE]: getRecipe,
  [DOC_TYPES.PAGE_SECTION_EXAMPLE]: getPageSectionExample,
  [DOC_TYPES.TV_EPISODE]: getTvEpisode,
  [DOC_TYPES.RADIO_EPISODE]: getRadioEpisode,
  [DOC_TYPES.MAGAZINE]: getMagazineIssue,
  [DOC_TYPES.ARTICLE]: getArticle,
}
