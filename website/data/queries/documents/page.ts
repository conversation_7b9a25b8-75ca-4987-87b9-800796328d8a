import { groq } from 'next-sanity'
import renderSections from '../renderSections'
import metadata from '../metadata'
import shared from './shared'
import { getPageQueryFirstLine } from '@/lib/utils/pageQueries'
import { GetPageQueryFirstLineOptions } from '@/lib/utils/pageQueries'
import { DOC_TYPES } from '@/data'
import { SanityPageType } from '@/types/sanity/SanityPage'

export const getPageQuery = (options?: GetPageQueryFirstLineOptions) => {
  return groq`
    ${getPageQueryFirstLine(DOC_TYPES.PAGE as SanityPageType, options)} {
      ${shared.pageBasicFields},
      ${metadata.fields},
      content[] {${renderSections.fields}},
    }
  `
}
