import { groq } from 'next-sanity'
import { DOC_TYPES } from '@/data'
import { getPageQueryFirstLine, GetPageQueryFirstLineOptions } from '@/lib/utils/pageQueries'
import { SanityPageType } from '@/types/sanity/SanityPage'
import metadata from '../metadata'
import shared from './shared'

export const getMagazineIssueQuery = (options?: GetPageQueryFirstLineOptions) => {
  return groq`
    ${getPageQueryFirstLine(DOC_TYPES.MAGAZINE as SanityPageType, options)} {
      ${shared.pageBasicFields},
      ${metadata.fields}
    }
  `
}
