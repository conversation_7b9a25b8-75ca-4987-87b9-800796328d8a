import { groq } from 'next-sanity'
import renderSections from '../renderSections'
import shared from './shared'
import { DOC_TYPES } from '@/data'
import { getPageQueryFirstLine, GetPageQueryFirstLineOptions } from '@/lib/utils/pageQueries'
import { SanityPageType } from '@/types/sanity/SanityPage'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getPageSectionExampleQuery = (options?: GetPageQueryFirstLineOptions) => {
  return groq`
    ${getPageQueryFirstLine(DOC_TYPES.PAGE_SECTION_EXAMPLE as SanityPageType, options)} {
      ${shared.pageBasicFields},
      figmaLink,
      content[] {${renderSections.fields}},
    }
  `
}
