import { groq } from 'next-sanity'
import { richTextFields } from '../_shared'
import { DOC_TYPES } from '@/data'
import { getPageQueryFirstLine, GetPageQueryFirstLineOptions } from '@/lib/utils/pageQueries'
import { SanityPageType } from '@/types/sanity/SanityPage'
import shared from './shared'
import metadata from '../metadata'

export const getProductQuery = (options?: GetPageQueryFirstLineOptions) => {
  return groq`
    ${getPageQueryFirstLine(DOC_TYPES.PRODUCT as SanityPageType, options)} {
      ${shared.pageBasicFields},
      ${metadata.fields},
      description[]{${richTextFields}},
      mainImage,
      price,
      ...
    }
  `
}
