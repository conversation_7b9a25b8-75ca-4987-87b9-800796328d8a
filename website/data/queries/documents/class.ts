import { groq } from 'next-sanity'
import { richTextFields } from '../_shared'
import { DOC_TYPES } from '@/data'
import { getPageQueryFirstLine, GetPageQueryFirstLineOptions } from '@/lib/utils/pageQueries'
import { SanityPageType } from '@/types/sanity/SanityPage'
import metadata from '../metadata'
import shared from './shared'

export const getClassQuery = (options?: GetPageQueryFirstLineOptions) => {
  return groq`
    ${getPageQueryFirstLine(DOC_TYPES.CLASS as SanityPageType, options)} {
      ${shared.pageBasicFields},
      ${metadata.fields},
      body[]{${richTextFields}},
      mainImage,
      ...
    }
  `
}
