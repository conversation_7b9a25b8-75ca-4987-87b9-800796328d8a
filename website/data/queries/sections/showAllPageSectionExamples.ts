import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import imageAsset from '../imageAsset'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  "items": *[_type == "pageSectionExample"] {
      title,
      slug,
      figmaLink,
      ${imageAsset.fragment('imageExample')}
  }
`

export const fragment = (name = 'showAllPageSectionExamples') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
