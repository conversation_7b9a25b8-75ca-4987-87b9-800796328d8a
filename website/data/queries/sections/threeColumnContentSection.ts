import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import imageAsset from '../imageAsset'
import link from '../link'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  items[] {
    _type == 'imageAsset' => {
      _type,
      ${imageAsset.fields}
    },
    _type == 'titleTextAndCta' => {
      _type,
      title,
      description,
      cta {
        ${link.fields}
      }
    },
    _type == 'eyebrowWithLineItems' => {
      _type,
      eyebrow,
      items
    }
  }
`

export const fragment = (name = 'threeColumnContentSection') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
