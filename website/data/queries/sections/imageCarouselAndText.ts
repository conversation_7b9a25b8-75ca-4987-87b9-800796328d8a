import { cardFields, personFields } from '../_shared'
import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import imageAsset from '../imageAsset'
import link from '../link'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  useContentReference,
  useContentReference == true => {
    referenceForContent->{
      ${cardFields}
    },
  },
  title,
  authors[]->{${personFields}},
  description,
  images[]{${imageAsset.fields}},
  ${link.fragment('link')},
  referenceForContent->_type == 'recipe' => {
    count(images) == 0 => {
      "images": [
        referenceForContent->.mainImage{${imageAsset.fields}},
        ...referenceForContent->additionalImages[]{${imageAsset.fields}}
      ],
    },
    "authors": [
      referenceForContent->author->{${personFields}}
    ]
  }
`

export const fragment = (name = 'imageCarouselAndText') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
