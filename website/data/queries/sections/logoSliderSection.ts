import { richTextFields } from '../_shared'
import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import imageAsset from '../imageAsset'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  content[]{${richTextFields}},
  logoItems[] {
    ${imageAsset.fragment('image')},
    desktopWidth,
    mobileWidth,
  }
`

export const fragment = (name = 'logoSliderSection') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
