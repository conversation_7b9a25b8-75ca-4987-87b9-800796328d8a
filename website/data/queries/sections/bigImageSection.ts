import { groq } from 'next-sanity'
import imageAsset from '../imageAsset'
import cmsSettings from '../cmsSettings'

import video from '../video'

export const fields = groq`
  _key,
  _type,
  _id,
  ${cmsSettings()},
  mediaType,
  aspectRatio,
  media[]{
    _type == 'imageAsset' => {
      ${imageAsset.fields}
    },
    _type == 'video' => {
      ${video.fields}
    }
  }
`

export const fragment = (name = 'bigImageSection') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
