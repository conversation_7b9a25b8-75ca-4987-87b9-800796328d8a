import category from '../category'
import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import cardStyle from '../cardStyle'
import displayedCardFields from '../displayedCardFields'
import subcategory from '../subcategory'
export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  title,
  searchFieldLabel,
  "emptyInitially": coalesce(emptyInitially, false),
  "disableUrlUpdates": coalesce(disableUrlUpdates, false),
  typesToQuery,
  searchBarCategories[]->{
    ${category.fields},
    "children": *[_type == "subcategory" && references(^._id)] {
      ${subcategory.fields},
    }
  },
  filterCategories[]->{
    ${category.fields},
    "children": *[_type == "subcategory" && references(^._id)] {
      ${subcategory.fields},
    }
  },
  ${cardStyle.fragment('cardStyle')},
  ${displayedCardFields.fragment('displayedCardFields')}
`

export const fragment = (name = 'searchBarAndContent') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
