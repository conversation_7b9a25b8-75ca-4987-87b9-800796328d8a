import { cardFields, richTextFields } from '../_shared'
import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import imageAsset from '../imageAsset'
import link from '../link'
import video from '../video'
import sidebar from '../sidebar'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  title,
  ${sidebar.fragment('sidebar')},
  description[]{${richTextFields}},
  items[] {
    title,
    description,
    ${imageAsset.fragment('image')},
    ${link.fragment('link')}
  },
  ${video.fragment('video')},
  referencedItems[]->{
    ${cardFields}
  }
`

export const fragment = (name = 'exampleSection') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
