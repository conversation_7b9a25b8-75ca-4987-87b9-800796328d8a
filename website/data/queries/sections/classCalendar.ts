import { getDateRange } from '@/lib/utils/date'
import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import { cardFieldsByType } from '../_shared'

const now = new Date()
const oneYearFromNow = new Date(now)
oneYearFromNow.setFullYear(now.getFullYear() + 1)

const { startDate, endDate } = getDateRange({
  startDate: now,
  endDate: oneYearFromNow,
})

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  title,
  "classes": *[_type == "class" && date >= "${startDate}" && date <= "${endDate}"] | order(date asc) {
    ${cardFieldsByType.classCard}
  }
`

export const fragment = (name = 'classCalendar') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
