import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import imageAsset from '../imageAsset'
import link from '../link'
import { cardFields } from '../_shared'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  useContentReference,
  useContentReference == true => {
    referenceForContent->{
      ${cardFields}
    },
  },
  linkLabel,
  eyebrow,
  title,
  description,
  price,
  discountPrice,
  ${link.fragment('link')},
  ${imageAsset.fragment('image')}
`

export const fragment = (name = 'textAndImageSection') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
