import { getRichTextFields } from '../_shared'
import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import link from '../link'
import imageAsset from '../imageAsset'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  hasBreadcrumb,
  title[]{${getRichTextFields({
    additionalFields: groq`
      _type == "subnavTitleImage" => {
        title,
        ${imageAsset.fragment('image')},
        "desktopWidth": coalesce(desktopWidth, 80),
        "mobileWidth": coalesce(mobileWidth, 50)
      }
    `,
  })}},
  links[]{${link.fields}}
`

export const fragment = (name = 'headerWithSubnav') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
