import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import imageAsset from '../imageAsset'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  title,
  description,
  ${imageAsset.fragment('image')},
  imageCaption,
  imageSide,
  columns
`

export const fragment = (name = 'textAndStaticImageSection') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
