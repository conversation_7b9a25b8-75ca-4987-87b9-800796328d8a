import { groq } from 'next-sanity'
import imageAsset from '../imageAsset'
import cmsSettings from '../cmsSettings'

export const fields = groq`
    _type,
    _key,
    _id,
    ${cmsSettings()},
    title,
    description,
    points[] {
      title,
      text
    },
    imageSide,
    ${imageAsset.fragment('image')}
`

export const fragment = (name = 'textAndPointsWithImageSection') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
