import { groq } from 'next-sanity'
import cmsSettings from '../cmsSettings'
import { getRichTextFields } from '../_shared'
import imageAsset from '../imageAsset'
import video from '../video'
export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  media[]{
    _type,
    _type == "imageAsset" => {
      ${imageAsset.fields}
    },
    _type == "video" => {
      ${video.fields}
    }
  },
  description[]{
    ${getRichTextFields({})}
  }
`

export const fragment = (name = 'imageAndRichTextWithButton') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
