import { groq } from 'next-sanity'
import link from '../link'

export const fields = groq`
  _type == "titleTextAndCtaSection" => {
    _type,
    _key,
    className,
    content {
      title,
      description,
      ${link.fragment('cta')}
    }
  }
`
export const fragment = (name = 'titleTextAndCtaSection') => `${name}{ ${fields} }`

export const exported = {
  fields,
  fragment,
}

export default exported
