import cmsSettings from '../cmsSettings'
import { groq } from 'next-sanity'
import link from '../link'
import imageAsset from '../imageAsset'

export const fields = groq`
  _type,
  _id,
  ${cmsSettings()},
  title,
  subtitle,
  items[] {
    ${imageAsset.fragment('image')},
    title,
    subtitle,
    cost,
    cta {
      ${link.fields}
    },
    items
  }
`

export const fragment = (name = 'subscriptionPlansSection') => `${name}{ ${fields} }`

const exported = {
  fields,
  fragment,
}

export default exported
