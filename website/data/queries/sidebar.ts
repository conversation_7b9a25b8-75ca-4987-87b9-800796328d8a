import { groq } from 'next-sanity'
import link from './link'
import cardItem from './cardItem'
import cardItems from './cardItems'
import button from './button'
import imageAsset from './imageAsset'
export const fields = groq`
  items[]{
    _type == "titleAndText" => {
      _type,
      title,
      text,
      ${link.fragment('link')}
    },
    _type == "heading" => {
      _type,
      title,
      "titleSize": coalesce(titleSize, 'md'),
      ${link.fragment('link')},
      "hasLine": coalesce(hasLine, true)
    },
    _type == "cardItem" => {
      _type,
      ${cardItem.fields}
    },
    _type == "cardItems" => {
      _type,
      ${cardItems.fields}
    },
    _type == "button" => {
      _type,
      ${button.fields}
    },
    _type == "textAndImage" => {
      _type,
      title,
      ${link.fragment('link')},
      ${imageAsset.fragment('image')},
      "desktopImageWidth": coalesce(desktopImageWidth, 240),
      "mobileImageWidth": coalesce(mobileImageWidth, 150)
    }
  }
`

export const fragment = (name: string) => `${name}{
	${fields}
}`

const exports = { fields, fragment }

export default exports
