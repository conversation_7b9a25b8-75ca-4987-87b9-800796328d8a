/* stylelint-disable property-disallowed-list */

/* =================================
Text
================================= */

@mixin font-text-sm {
  font-family: var(#{$body-font-variable});
  font-style: normal;
  font-weight: 400;
  font-size: px(14);
  line-height: px(24);
}

@mixin font-text-sm-display {
  @include font-text-sm;
  font-family: $display-fonts;
}

@mixin font-text-base {
  font-family: var(#{$body-font-variable});
  font-style: normal;
  font-weight: 400;
  font-size: px(14);
  line-height: 150%;

  @include bp(mobile) {
    font-size: px(16);
  }
}

@mixin font-text-base-snug {
  @include font-text-base;
  line-height: 120%;

  @include bp(mobile) {
    line-height: 120%;
  }
}

@mixin font-text-lg {
  font-family: var(#{$body-font-variable});
  font-style: normal;
  font-weight: 400;
  font-size: px(16);
  line-height: 150%;
  letter-spacing: 0.01em;

  @include bp(mobile) {
    font-size: px(20);
  }
}

@mixin font-text-lg-snug {
  @include font-text-lg;
  line-height: 100%;

  @include bp(mobile) {
    line-height: 100%;
  }
}

/* =================================
Eyebrow
================================= */

@mixin font-heading-eyebrow-sm {
  font-family: var(#{$body-font-variable});
  font-style: normal;
  font-weight: 700;
  font-size: px(10);
  line-height: px(12);
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

@mixin font-heading-eyebrow-md {
  font-family: var(#{$body-font-variable});
  font-style: normal;
  font-weight: 700;
  font-size: px(12);
  line-height: 120%;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

@mixin font-heading-eyebrow-lg {
  font-family: var(#{$body-font-variable});
  font-style: normal;
  font-weight: 700;
  font-size: px(16);
  line-height: 1.3;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

/* =================================
Heading
================================= */

@mixin font-heading-xs {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 400;
  font-size: px(16);
  line-height: 1.125;
}

@mixin font-heading-sm {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 400;
  font-size: px(16);
  line-height: 1.2;

  @include bp(mobile) {
    font-size: px(20);
  }
}

@mixin font-heading-md {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 400;
  font-size: px(20);
  line-height: 120%;

  @include bp(mobile) {
    font-size: px(24);
  }
}

@mixin font-heading-lg {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 400;
  font-size: px(24);
  line-height: 120%;

  @include bp(mobile) {
    font-size: px(36);
  }
}

@mixin font-heading-xl {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 400;
  font-size: px(36);
  line-height: 110%;

  @include bp(mobile) {
    font-size: px(48);
  }
}

@mixin font-heading-2xl {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 400;
  font-size: px(48);
  line-height: 120%;

  @include bp(mobile) {
    font-size: px(64);
  }
}

/* =================================
Byline
================================= */

@mixin font-byline-xs {
  font-family: $display-fonts;
  font-style: italic;
  font-weight: 400;
  font-size: px(10);
  line-height: px(12);
}

@mixin font-byline-sm {
  font-family: $display-fonts;
  font-style: italic;
  font-weight: 400;
  font-size: px(12);
  line-height: px(20);
}

@mixin font-byline-md {
  font-family: $display-fonts;
  font-style: italic;
  font-weight: 400;
  font-size: px(14);
  line-height: px(20);
}

/* =================================
Date
================================= */

@mixin font-date-xs {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 400;
  font-size: px(10);
  line-height: px(12);
}

@mixin font-date-sm {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 400;
  font-size: px(12);
  line-height: px(20);
}

@mixin font-date-md {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 400;
  font-size: px(14);
  line-height: px(20);
}

/* =================================
Link Italic
================================= */

@mixin font-link-italic-lg {
  font-family: var(#{$body-font-variable});
  font-style: italic;
  font-weight: 400;
  font-size: px(16);
  line-height: 120%;
  text-decoration-line: underline;

  @include bp(mobile) {
    font-size: px(20);
  }
}

/* =================================
Link Inline
================================= */

@mixin font-link-inline {
  font-family: var(#{$body-font-variable});
  font-style: normal;
  font-weight: 700;
  font-size: px(14);
  line-height: 150%;
  text-decoration-line: underline;

  @include bp(mobile) {
    font-size: px(16);
  }
}

/* =================================
Episode
================================= */

@mixin font-episode-sm {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 700;
  font-size: px(12);
  line-height: px(20);
}

@mixin font-episode {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 700;
  font-size: px(14);
  line-height: 150%;
}

/* =================================
Stars Rating
================================= */

@mixin font-stars-rating-sm {
  font-family: var(#{$body-font-variable});
  font-weight: 500;
  font-size: px(12);
}

@mixin font-stars-rating-md {
  font-family: var(#{$body-font-variable});
  font-weight: 500;
  font-size: px(24);
}

@mixin font-stars-rating-lg {
  font-family: var(#{$body-font-variable});
  font-weight: 500;
  font-size: px(40);
}

/* =================================
Calendar Card
================================= */

@mixin font-calendar-card-title {
  font-family: $display-fonts;
  font-style: normal;
  font-weight: 400;
  font-size: px(14);
  line-height: px(18);
}
