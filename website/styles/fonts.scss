@import 'vars';

$font-path: '../assets/fonts/' !default;

/*
100	Thin (Hairline)
200	Extra Light (Ultra Light)
300	Light
400	Normal (Regular)
500	Medium
600	Semi Bold (Demi Bold)
700	Bold
800	Extra Bold (Ultra Bold)
900	Black (Heavy)
950	Extra Black (Ultra Black)

@font-face {
    font-family: $faktum-font;
    src: url("#{$font-path}font-name-bold.woff") format("woff");
    font-weight: 600;
    font-display: swap;
}
*/

@font-face {
  font-family: $display-font-name;
  src:
    url('/fonts/TerzaReader-Regular-Web.woff2') format('woff2'),
    url('/fonts/TerzaReader-Regular-Web.woff') format('woff');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: $display-font-name;
  src:
    url('/fonts/TerzaReader-RegularItalic-Web.woff2') format('woff2'),
    url('/fonts/TerzaReader-RegularItalic-Web.woff') format('woff');
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: $display-font-name;
  src:
    url('/fonts/TerzaReader-Bold-Web.woff2') format('woff2'),
    url('/fonts/TerzaReader-Bold-Web.woff') format('woff');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: $display-font-name;
  src:
    url('/fonts/TerzaDisplay-Regular-Web.woff2') format('woff2'),
    url('/fonts/TerzaDisplay-Regular-Web.woff') format('woff');
  font-weight: 400;
  font-style: normal;
}
