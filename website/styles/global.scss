// Global styling, normalize overwritten
@import 'fonts';
@import 'shared';

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* stylelint-disable-next-line */
:root {
  // Colors
  --white: #{$white};
  --black: #{$black};
  --parchment: #{$parchment};
  --gray-quiet: #{$gray-quiet};
  --highlight-light: #{$highlight-light};
  --highlighter: #{$highlighter};
  --loud-warm: #{$loud-warm};
  --loud-warm-darker: #{$loud-warm-darker};
  --navy-dark: #{$navy-dark};
  --navy-light: #{$navy-light};
  --slate: #{$slate};

  // Grid
  --gutter: #{$gutter};
  --page-gutter: #{$page-gutter-mobile};

  @include bp(tablet) {
    --page-gutter: #{$page-gutter-tablet};
  }

  @include bp(desktop) {
    --page-gutter: #{$page-gutter};
  }

  // General
  --section-spacing: #{$section-spacing-mobile};
  --header-height: #{$header-height-mobile};
  --section-max-width: #{$section-max-width};

  @include bp(tablet) {
    --header-height: #{$header-height};
    --section-spacing: #{$section-spacing-desktop};
  }

  // aspect ratio
  --image-carousel-aspect-ratio: #{$image-carousel-aspect-ratio};
  --aspect-ratio-square: #{$aspect-ratio-square};
  --aspect-ratio-4-3: #{$aspect-ratio-4-3};
  --aspect-ratio-16-9: #{$aspect-ratio-16-9};
  --aspect-ratio-magazine: #{$aspect-ratio-magazine};
}

html {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: initial;
  min-height: 100%;
  height: auto;
}

body {
  font-family: var(#{$body-font-variable});
  margin: 0;
  padding: 0;
  min-height: 100%;
  /* stylelint-disable-next-line property-disallowed-list */
  font-size: px(16);
  height: auto;
  background-color: var(--white);
  color: var(--navy-dark);

  // Disabling interaction
  &[data-enable-interaction='false'] {
    pointer-events: none !important;

    * {
      pointer-events: none !important;
    }
  }

  // Disable scrolling
  &[data-disable-scroll='true'] {
    overflow: hidden;
    position: fixed;
    width: 100%;
    height: 100%;
  }
}

main {
  @include z-index(main);
  min-height: 100svh;
  position: relative;
}

a {
  text-decoration: none;
  color: inherit;
}

ul,
dl,
ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

li,
dt,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote {
  margin: 0;
  padding: 0;
}
