import { getPageDataByType } from '@/data/sanity'
import { SanityPageType } from '@/types/sanity/SanityPage'
import { getImageUrl } from '@/components/SanityImage/SanityImage.helper'
import { removeLeadingSlash, slugArrayToSlug } from '.'

/*
TODO:
- use images from page and not global meta image
- Ensure page meta description is in and formatted for each type of page
*/

const getPageMetadata = async (docType: SanityPageType, slug: string[] | string, isDraftMode?: boolean) => {
  let slugAsString = slug
  if (Array.isArray(slug)) {
    slugAsString = slugArrayToSlug(slug)
  }
  slugAsString = removeLeadingSlash(slugAsString as string)

  if (!slugAsString) return {}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const page = (await getPageDataByType[docType](slugAsString as string, { isPreview: isDraftMode })) as any
  const globalMetaData = page?.globalMetaData

  if (!globalMetaData || !globalMetaData.defaultOpenGraphImage || !globalMetaData.favicon) return {}

  // Title
  const pageTitle = page?.metaTitle || page?.title || globalMetaData.title
  let title = `${pageTitle} | ${globalMetaData.title}`
  if (pageTitle === globalMetaData.title) {
    title = globalMetaData.title || ''
  }

  // Description
  let description = globalMetaData.description
  if (page?.metaDescription) {
    description = page?.metaDescription
  }

  // Keywords
  let keywords = globalMetaData.keywords
  if (!!page?.keywords?.length) {
    keywords = page?.keywords
  }

  // Image
  let image = globalMetaData.defaultOpenGraphImage
  if (page?.image) {
    image = page.image
  }
  if (page?.images?.length) {
    image = page.images[0]
  }
  if (page?.openGraphImage) {
    image = page.openGraphImage
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const data: any = {
    title,
    robots: {
      index: !page.noIndex,
      follow: !page.noIndex,
      nocache: page.noIndex,
      googleBot: {
        index: !page.noIndex,
        follow: !page.noIndex,
        noimageindex: page.noIndex,
      },
    },
    metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || ''),
    description,
    keywords: keywords,
    openGraph: {
      title,
      description,
      siteName: globalMetaData.title,
      images: [
        {
          url: getImageUrl(image, { width: 1200, height: 630, fit: 'crop' }) || '',
          width: 1200,
          height: 630,
          alt: globalMetaData.title,
        },
      ],
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [
        {
          url: getImageUrl(image, { width: 1024, height: 512, fit: 'crop' }) || '',
          width: 1024,
          height: 512,
          alt: globalMetaData.title,
        },
      ],
    },
  }

  return data
}

export default getPageMetadata
