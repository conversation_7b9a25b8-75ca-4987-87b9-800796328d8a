type PageType = 'page' | 'blog' | 'article' | 'recipe'

export const getUrlFromPageData = (type: string | undefined, slug: string) => {
  const typeToPath: Record<PageType, string> = {
    page: '',
    blog: '/blog',
    article: '/articles',
    recipe: '/recipes',
  }
  return `${typeToPath[type as PageType] || ''}/${slug}`
}

export const getDomainNameFromUrlString = (urlString: string) => {
  try {
    const url = new URL(urlString)
    return url.hostname
  } catch (error) {
    console.error('Error getting domain name from URL string:', error)
    return ''
  }
}

export const isValidUrl = (urlString: string) => {
  try {
    new URL(urlString)
    return true
  } catch (error) {
    console.error('Error validating URL:', error)
    return false
  }
}
