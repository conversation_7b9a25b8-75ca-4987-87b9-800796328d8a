export function formatDate(dateString: string, options: Intl.DateTimeFormatOptions = {}): string {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options,
  }).format(date)
}

export function getDateParts(isoString: string) {
  const date = new Date(isoString)

  return {
    day: date.getDate(),
    month: date.toLocaleString('en-US', { month: 'short' }),
  }
}

export function getTime(isoString: string) {
  if (!isoString) return null

  const date = new Date(isoString)
  const hours = date.getHours()
  const minutes = date.getMinutes()
  const ampm = hours >= 12 ? 'pm' : 'am'
  const formattedHours = hours % 12 || 12 // Convert 0 to 12 for 12 AM

  return `${formattedHours}:${minutes.toString().padStart(2, '0')}${ampm}`
}

export function getDateRange({ startDate = new Date(), endDate = new Date() }: { startDate: Date; endDate: Date }) {
  return {
    startDate: new Date(startDate).toISOString().split('T')[0],
    endDate: new Date(endDate).toISOString().split('T')[0],
  }
}
