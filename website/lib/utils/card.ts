import { DOC_TYPES } from '@/data'
import { CardProps } from '@/types/components/CardProps'

const DEFAULT_ALLOWED_FIELDS = ['image', 'description', 'starRating', 'tag', 'authorAndDate']

export const formatCardFieldsForComponent = ({
  props,
  allowedFields = DEFAULT_ALLOWED_FIELDS,
  cardStyle = null,
}: {
  props: CardProps
  allowedFields?: string[]
  cardStyle?: SanityCardStyle | null
}) => {
  // eslint-disable-next-line
  const fields: any = props

  // Defauls
  fields.padding = false

  // Get link
  if (fields.slug && fields._type) {
    fields.link = {
      linkType: 'internal',
      link: {
        slug: fields.slug,
        _type: fields._type,
      },
    }
  } else if (fields.link) {
    fields.link = props.link
  }

  // Allow favoriting for recipes
  if (fields._type === 'recipe') {
    fields.allowFavoriting = true
  }

  // Tags
  if (!props.tagProps) {
    const tagObject: TagProps = {
      text: '',
    }
    switch (props._type) {
      case DOC_TYPES.TV_EPISODE:
        tagObject.text = 'TV Episode'
        break
      case DOC_TYPES.RADIO_EPISODE:
        tagObject.text = 'Radio Episode'
        break
      case DOC_TYPES.CLASS:
        tagObject.text = 'Class'
        break
      case DOC_TYPES.RECIPE:
        tagObject.text = 'Recipe'
        break
      case DOC_TYPES.ARTICLE:
        tagObject.text = 'Story'
        break
      case DOC_TYPES.PRODUCT:
        tagObject.text = 'Product'
        break
      case DOC_TYPES.MAGAZINE:
        tagObject.text = 'Magazine'
        break
    }

    fields.tagProps = tagObject
  }

  // Override card style
  if (cardStyle?.useDefaultStyle === false) {
    if (cardStyle?.orientation) {
      fields.orientation = cardStyle?.orientation
    }

    if (cardStyle?.imageSide) {
      fields.imageSide = cardStyle?.imageSide
    }

    if (cardStyle?.imageOrientation) {
      fields.imageOrientation = cardStyle?.imageOrientation
    }

    if (cardStyle?.padding !== undefined) {
      fields.padding = cardStyle?.padding
    }
  }

  // Remove fields that are not allowed
  const fieldsToDelete = DEFAULT_ALLOWED_FIELDS.filter(field => !allowedFields.includes(field))
  fieldsToDelete.forEach(field => {
    if (field === 'image') {
      delete fields['image']
    }
    if (field === 'description') {
      delete fields['description']
    }
    if (field === 'starRating') {
      delete fields['starsRatingProps']
    }
    if (field === 'tag') {
      delete fields['tagProps']
    }
    if (field === 'authorAndDate') {
      delete fields['author']
      delete fields['date']
    }
  })

  return fields
}

export const getCardPropsFromCardItems = (cardItems?: SanityCardItems) => {
  if (!cardItems) return []

  if (cardItems.type === 'multiReference') {
    return cardItems?.references || []
  }

  if (cardItems.type === 'collection') {
    return cardItems?.collection?.items || []
  }

  return []
}

export const formatCardItemsObjectToItems = (cardItemsObject?: SanityCardItems) => {
  const cardItems = getCardPropsFromCardItems(cardItemsObject)
  const cardItemsFormatted = cardItems
    .map((card, i) => {
      const capAt = cardItemsObject?.capItems || 15

      if (i >= capAt) return null

      return formatCardFieldsForComponent({
        props: card,
        allowedFields: cardItemsObject?.displayedCardFields?.fieldsAllowed,
        cardStyle: cardItemsObject?.cardStyle,
      })
    })
    .filter(n => n)

  return cardItemsFormatted
}

export const formatCardItemObjectToCardProps = (cardItemObject?: SanityCardItem) => {
  const type = cardItemObject?.type

  if (type === 'reference') {
    return formatCardFieldsForComponent({
      props: cardItemObject?.reference,
      allowedFields: cardItemObject?.referenceDisplayFields?.fieldsAllowed,
      cardStyle: cardItemObject?.referenceCardStyle,
    })
  }

  if (type === 'forumCard') {
    return {
      _type: 'forumCard',
      ...cardItemObject?.forumCard,
    }
  }

  if (type === 'customCard') {
    const props: CardProps = {
      _type: 'card',
      image: cardItemObject?.image || undefined,
      description: cardItemObject?.description || undefined,
      title: cardItemObject?.title || '',
      link: cardItemObject?.link || null,
    }

    if (cardItemObject?.tag) {
      props.tagProps = {
        text: cardItemObject?.tag || '',
      }
    }

    return formatCardFieldsForComponent({
      props,
      cardStyle: cardItemObject?.customCardStyle,
    })
  }
}
