import { DIRECTORY_NAMES, DOC_TYPES, HOME_SLUG } from '@/data'

export const getPagePathBySlug = (slug: string) => {
  if (slug === HOME_SLUG) return '/'
  return `/${slug}`
}

export const getRecipePathBySlug = (slug: string) => {
  return `/${DIRECTORY_NAMES.RECIPES}/${slug}`
}

export const getArticlePathBySlug = (slug: string) => {
  return `/${DIRECTORY_NAMES.BLOG}/${slug}`
}

export const getTvEpisodePathBySlug = (slug: string) => {
  return `/${DIRECTORY_NAMES.TV}/${slug}`
}

export const getRadioEpisodePathBySlug = (slug: string) => {
  return `/${DIRECTORY_NAMES.RADIO}/${slug}`
}

export const getClassPathBySlug = (slug: string) => {
  return `/${DIRECTORY_NAMES.CLASSES}/${slug}`
}

export const getPageSectionExamplePathBySlug = (slug: string) => {
  return `/${DIRECTORY_NAMES.PAGE_SECTION_EXAMPLE}/${slug}`
}

export const getUrlFromPageData = (pageType: string, slug: string): string => {
  let url = ''

  switch (pageType) {
    case DOC_TYPES.PAGE:
      url = getPagePathBySlug(slug)
      break
    case DOC_TYPES.RECIPE:
      url = getRecipePathBySlug(slug)
      break
    case DOC_TYPES.ARTICLE:
      url = getArticlePathBySlug(slug)
      break
    case DOC_TYPES.CLASS:
      url = getClassPathBySlug(slug)
      break
    case DOC_TYPES.TV_EPISODE:
      url = getTvEpisodePathBySlug(slug)
      break
    case DOC_TYPES.RADIO_EPISODE:
      url = getRadioEpisodePathBySlug(slug)
      break
    case DOC_TYPES.PAGE_SECTION_EXAMPLE:
      url = getPageSectionExamplePathBySlug(slug)
      break
    default:
      break
  }

  return url
}

export const slugArrayToSlug = (slug: string | string[]): string => {
  if (!slug) return slug

  // NEEDS TO BE DECODED
  // Because vercel specifically needs this.
  // NO fricken idea why.
  if (typeof slug === 'string') {
    // Decode URL-encoded string
    return decodeURIComponent(slug)
  }

  return decodeURIComponent(slug.join('/'))
}

export const slugPathToSlugArray = (slug: string): string[] => {
  return slug.split('/')
}

export const removeLeadingSlash = (str: string): string => {
  return str.charAt(0) === '/' ? str.slice(1) : str
}

export type FilterByOperatorAndIds = {
  operator: 'AND' | 'OR' | 'TEXT_SEARCH'
  ids?: string[]
  text?: string
}

export const formatFilteredCategoriesToOperatorsAndIds = (
  filteredCategories: SanitySearchBarFilterData,
): FilterByOperatorAndIds[] => {
  const value = Object.values(filteredCategories)
    .map((category: SanitySearchBarFilterItem) => {
      if (!category) return null

      let operator: 'AND' | 'OR' | 'TEXT_SEARCH' | null = null
      switch (category.type) {
        case 'checkbox':
          operator = 'OR'
          break
        case 'radio':
          operator = 'AND'
          break
        case 'dropdown':
          operator = 'AND'
          break
        case 'text':
          operator = 'TEXT_SEARCH'
          break
        default:
          break
      }

      if (operator === 'TEXT_SEARCH' && category.text) {
        return {
          operator,
          text: category.text,
        }
      } else if (!!category?.ids?.length) {
        return {
          operator,
          ids: category.ids,
        }
      }
    })
    .filter(item => item && item.operator && (!!item?.ids?.length || item?.text))

  return value as FilterByOperatorAndIds[]
}
