import { DOC_TYPES } from '@/data'
import { getSearchBarContent } from '@/data/sanity'
import { SanityPage } from '@/types/sanity/SanityPage'

export const formatPageDataToPageSections = async (pageData: SanityPage) => {
  let formattedPageData = pageData

  if (pageData._type === DOC_TYPES.RECIPE) {
    formattedPageData = formatRecipePage(pageData)
  }

  if (pageData._type === DOC_TYPES.ARTICLE) {
    formattedPageData = formatArticlePage(pageData)
  }

  if (pageData._type === DOC_TYPES.MAGAZINE) {
    formattedPageData = formatMagazineIssuePage(pageData)
  }

  if (pageData._type === DOC_TYPES.TV_EPISODE) {
    formattedPageData = formatTvEpisodePage(pageData)
  }

  if (pageData._type === DOC_TYPES.RADIO_EPISODE) {
    formattedPageData = formatRadioEpisodePage(pageData)
  }

  formattedPageData = await genericFormatter(pageData)

  return formattedPageData
}

const genericFormatter = async (pageData: SanityPage) => {
  if (!!pageData?.content?.length) {
    let i = 0

    for (const content of pageData.content) {
      if (content._type === 'searchBarAndContent') {
        pageData.content[i] = await formatSearchBarAndContent(content)
      }

      i++
    }
  }

  return pageData
}

const formatSearchBarAndContent = async (content: SanitySearchBarAndContent) => {
  if (!content.typesToQuery || content.emptyInitially) {
    return {
      ...content,
      totalItems: 0,
      items: [],
    }
  }

  const results = await getSearchBarContent({
    typesToQuery: content.typesToQuery,
    offset: 0,
  })

  const value = {
    ...content,
    ...results,
  }

  return value
}

const formatRecipePage = (pageData: SanityPage) => {
  pageData.content = [
    {
      _type: 'recipePage',
      pageData: pageData,
    },
  ]

  return pageData
}

const formatArticlePage = (pageData: SanityPage) => {
  pageData.content = [
    {
      _type: 'articlePage',
      pageData: pageData,
    },
  ]

  return pageData
}

const formatTvEpisodePage = (pageData: SanityPage) => {
  pageData.content = [
    {
      _type: 'headerWithSubnav',
      hasBreadcrumb: false,
      title: pageData.title,
      description: pageData.title,
    },
  ]

  return pageData
}

const formatRadioEpisodePage = (pageData: SanityPage) => {
  pageData.content = [
    {
      _type: 'headerWithSubnav',
      hasBreadcrumb: false,
      title: pageData.title,
      description: pageData.title,
    },
  ]

  return pageData
}

const formatMagazineIssuePage = (pageData: SanityPage) => {
  pageData.content = [
    {
      _type: 'headerWithSubnav',
      hasBreadcrumb: false,
      title: pageData.title,
      description: pageData.title,
    },
  ]

  return pageData
}
