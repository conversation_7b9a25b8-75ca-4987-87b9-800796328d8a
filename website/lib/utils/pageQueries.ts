import { DOC_TYPES } from '@/data'
import { SanityPageType } from '@/types/sanity/SanityPage'
import { groq } from 'next-sanity'

export type GetPageQueryFirstLineOptions = {
  all?: boolean
}

export const getPageQueryFirstLine = (
  _type: SanityPageType = DOC_TYPES.PAGE as SanityPageType,
  { all = false }: GetPageQueryFirstLineOptions = {},
) => {
  const index = all ? '' : groq`[0]`
  const slugLine = all ? '' : groq` && slug.current == $slug`

  const query = groq`*[_type == "${_type}" ${slugLine}]${index}`

  return query
}
