import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

type StoreValues = {
  // Global Settings
  canInteract: boolean

  // Navigation
  navIsOpen: boolean
}

type StoreSetters = {
  // Global Settings
  setCanInteract: (value: StoreValues['canInteract']) => void

  // Navigtion
  setNavIsOpen: (value: StoreValues['navIsOpen']) => void
}

type CombinedStoreValuesAndSetters = StoreValues & StoreSetters

export const useStore = create(
  subscribeWithSelector<CombinedStoreValuesAndSetters>(set => ({
    // Global Settings
    canInteract: true,
    setCanInteract: canInteract => set({ canInteract }),

    // Navigation
    navIsOpen: false,
    setNavIsOpen: navIsOpen => set({ navIsOpen }),
  })),
)

export default useStore
