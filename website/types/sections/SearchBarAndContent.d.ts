type SanitySearchBarFilterItem = {
  id: string
  slug?: string
  type: 'dropdown' | 'checkbox' | 'radio' | 'text'
  ids?: string[]
  text?: string
}

type SanitySearchBarFilterData = {
  [key: string]: SanitySearchBarFilterItem
}

type SanitySearchBarCategory = SanityCategory & {
  children: SanitySubcategory[]
}

type SanitySearchBarAndContent = SectionCMSInterface & {
  title?: string
  searchFieldLabel?: string
  typesToQuery: string[]
  searchBarCategories: SanitySearchBarCategory[]
  filterCategories: SanitySearchBarCategory[]
  totalCount: number
  items: Card[]
  cardStyle: SanityCardStyle
  displayedCardFields: SanityDisplayedCardFields
  emptyInitially: boolean
  disableUrlUpdates: boolean
}
