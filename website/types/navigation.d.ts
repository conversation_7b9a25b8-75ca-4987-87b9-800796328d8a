/**
 * Types for navigation components and data structures
 */

export type NavGroupLink = {
  title: string
  link: string
  isNew?: boolean
  isExternal?: boolean
  openInNewTab?: boolean
}

export type NavMenuHeader = {
  _type: 'header'
  title: string
  titleLink?: SanityLink
}

export type NavMenuLink = {
  _type: 'link'
  title: string
  linkData: SanityLink
}

export type NavMenuItem = NavMenuHeader | NavMenuLink

export type NavSubMenuGroup = {
  items: NavMenuItem[]
}

export type HeaderNavItem = {
  title: string
  isLink?: boolean
  link?: string
  linkData?: SanityLink
  isHighlighted?: boolean
  subMenuGroups?: NavSubMenuGroup[]
  _key?: string // Added for Sanity data structure
}

export type HeaderNavProps = {
  headerNav: HeaderNavItem[]
}
