type IconNames =
  | 'amazonMusic'
  | 'arrowLeft'
  | 'arrowRight'
  | 'bookmark'
  | 'bookmarkFilled'
  | 'bullets'
  | 'calendar'
  | 'caretDown'
  | 'caretLeft'
  | 'caretRight'
  | 'caretUp'
  | 'checkmark'
  | 'close'
  | 'copy'
  | 'document'
  | 'download'
  | 'facebook'
  | 'hamburger'
  | 'instagram'
  | 'magnifyingGlass'
  | 'pandora'
  | 'pinterest'
  | 'play'
  | 'playCircle'
  | 'podcasts'
  | 'printer'
  | 'profile'
  | 'spotify'
  | 'star'
  | 'starHollow'
  | 'tuneIn'
  | 'twitter'
  | 'wordmark'
  | 'youtube'
  | 'jumpAhead'
  | 'jumpBack'
  | 'audioNext'
  | 'audioBack'
  | 'closedCaptioning'
  | 'share'
  | 'basket'
  | 'pause'
  | 'filter'

type IconProps = {
  name: IconNames
  className?: string
}
