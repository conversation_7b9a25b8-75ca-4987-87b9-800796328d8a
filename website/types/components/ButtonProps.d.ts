type ButtonImperativeHandle = {
  getElement: () => HTMLElement | null
  setIsHover: (isHover: boolean) => void
}

type ButtonProps = {
  className?: string
  label?: string
  children?: ReactElement
  onFocus?: MouseEvent<HTMLButtonElement | HTMLSpanElement>
  onBlur?: MouseEvent<HTMLButtonElement | HTMLSpanElement>
  onMouseEnter?: MouseEvent<HTMLButtonElement | HTMLSpanElement>
  onMouseLeave?: MouseEvent<HTMLButtonElement | HTMLSpanElement>
  onClick?: MouseEvent<HTMLButtonElement | HTMLSpanElement>
  isHoverState?: boolean
  element?: 'button' | 'span' | 'label'
  link?: SanityLink
  linkClassName?: string
  disableHoverAnimation?: boolean
  disabled?: boolean
  ariaLabel?: string
  disableOpenNewTab?: boolean
  style?: 'primary' | 'secondary' | 'block' | 'rounded' | 'roundedFilled' | 'bare' | 'iconOnly' | 'iconOnlyFilled'
  htmlFor?: string
  icon?: IconNames
  iconPosition?: 'left' | 'right'
  labelSize?: 'sm' | 'md' | 'lg'
  bareColor?: 'default' | 'loud' | 'slate'
  bareFont?: 'default' | 'eyebrow'
  iconSize?: 'sm' | 'md'
  iconOnlySize?: 'md' | 'lg'
}
