type SanityLinkInternal = {
  _id?: string
  _type: string
  slug: string
}

type SanityLinkVideoTypes = 'vimeo' | 'youtube'

type SanityLink = {
  label?: string
  linkType: 'internal' | 'external' | 'disabled' | 'videoPopout' | 'file'
  link: SanityLinkInternal | string
  videoPopoutType?: SanityLinkVideoTypes
  videoId?: string
  hash?: string
  icon?: string
  navigationOffset?: bool
}

type LinkProps = {
  className?: string
  children?: ReactElement
  onMouseEnter?: MouseEvent<HTMLAnchorElement>
  onMouseLeave?: MouseEvent<HTMLAnchorElement>
  linkOnly?: boolean
  link: SanityLink
  ariaLabel?: string
  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void
  onFocus?: (e: MouseEvent<HTMLAnchorElement>) => void
  disableOpenNewTab?: boolean
  activeClass?: string
  tabIndexHidden?: boolean
  queryParamString?: string
  getIsSamePage?: (value: boolean) => void
}
