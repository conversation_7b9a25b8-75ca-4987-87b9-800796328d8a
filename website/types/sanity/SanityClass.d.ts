interface SanityClass {
  _id: string
  _type: string
  title: string
  slug: {
    current: string
  }
  mainImage: SanityImage
  date: string
  endDate?: string
  location: string
  instructor: SanityPerson
  description?: SanityContentBlockProps[]
  price: number
  memberPrice?: number
  capacity?: number
  skillLevel?: 'beginner' | 'intermediate' | 'advanced' | 'all'
  duration?: number
  whatToBring?: string[]
  whatIsProvided?: string[]
  recipes?: SanityRecipe[]
  bookingUrl?: string
  isSoldOut?: boolean
  metaTitle?: string
  metaDescription?: string
  openGraphImage?: SanityImage
}
