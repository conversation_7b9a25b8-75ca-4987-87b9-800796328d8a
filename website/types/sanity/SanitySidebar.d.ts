type SanitySidebarTitleAndText = {
  _type: 'titleAndText'
  title: string
  text: string
  link: SanityLink
}

type SanitySidebarHeading = {
  _type: 'heading'
  title: string
  titleSize: 'sm' | 'md'
  link: SanityLink
  hasLine: boolean
}

type SanitySidebarTextAndImage = {
  _type: 'textAndImage'
  title: string
  link: SanityLink
  image: SanityImage
  desktopImageWidth: number
  mobileImageWidth: number
}

type SanitySidebarItem =
  | SanitySidebarTitleAndText
  | SanitySidebarHeading
  | SanityCardItem
  | SanityCardItems
  | SanityButton
  | SanitySidebarTextAndImage

type SanitySidebar = {
  _type?: string
  items: SanitySidebarItem[]
}
