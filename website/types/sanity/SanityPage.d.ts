import { SanityImage } from './SanityImage'

export type SanityPageType =
  | 'page'
  | 'pageSectionExample'
  | 'recipe'
  | 'article'
  | 'product'
  | 'radioEpisode'
  | 'tvEpisode'
  | 'class'
export interface SanityPage {
  _id: string
  _type: string
  slug: {
    current: string
  }
  globalMetaData: Pick<SiteSettings, 'title' | 'description'> &
    Pick<SiteSettings, 'defaultOpenGraphImage', 'keywords', 'favicon'>
  isPaywalled: boolean
  authors?: SanityPerson[]
  expiryDate?: string
  content: SanityAllSectionTypes[]
  metaTitle?: string
  metaDescription?: string
  openGraphImage?: SanityImage
  keywords?: string[]
  noIndex?: boolean
  title?: string
  description?: string
}
