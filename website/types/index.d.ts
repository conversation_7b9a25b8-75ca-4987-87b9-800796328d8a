/**
 * Main type definitions index file
 */

// Site Settings Types
export interface SiteSettings {
  title?: string
  description?: string
  logo?: SanityImage
  footerLogo?: SanityImage
  footerNav?: FooterNavSection[]
  footerNewsletter?: FooterNewsletter
  socialLinks?: SocialLink[]
  footerLegalLinks?: LegalLink[]
  copyrightText?: string
  socialSharing?: {
    favicon?: SanityImage
    defaultOpenGraphImage?: SanityImage
  }
}

// Footer Types
export interface FooterNavSection {
  title: string
  isLink: boolean
  link?: SanityLink
  links?: FooterLink[]
}

export interface FooterLink {
  linkData: SanityLink
}

export interface FooterNewsletter {
  title?: string
  description?: string
  placeholderText?: string
  buttonText?: string
}

export interface SocialLink {
  platform: string
  url: string
}

export interface LegalLink {
  text: string
  isLink: boolean
  linkData?: SanityLink
}

// Navigation Types
export interface NavItem {
  linkData: SanityLink
  isHighlighted: boolean
  title: string
  isLink?: boolean
  link?: string
  subMenuGroups?: {
    title?: string
    titleLink?: SanityLink
    links: {
      linkData: SanityLink
      title: string
      link: string
      isNew?: boolean
      isExternal?: boolean
      openInNewTab?: boolean
    }[]
  }[]
}

// Component Props Types
export interface HeaderProps {
  siteSettings: SiteSettings
  headerNav: NavItem[]
  headerData: HeaderData
}

export interface MobileMenuProps {
  headerNav: NavItem[]
}

export interface FooterProps {
  siteSettings: SiteSettings
}
