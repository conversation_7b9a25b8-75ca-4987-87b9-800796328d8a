/**
 * Types for site settings and configuration
 */

export interface LogoSettings {
  url: string
  width?: number
  height?: number
}

export interface SocialSharingSettings {
  keywords?: string[]
  favicon?: SanityImage
  defaultOpenGraphImage?: SanityImage
}

export interface ContactInfo {
  email?: string
}

export interface CookieConsentSettings {
  enabled?: boolean
  text?: string
  privacyPolicyLink?: string
  settingsButtonText?: string
  acceptButtonText?: string
}

export interface GlobalAnnouncementSettings {
  enabled?: boolean
  backgroundColor?: string
  textColor?: string
  text?: string
}

export interface NewsletterSettings {
  title?: string
  description?: string
  buttonText?: string
  placeholderText?: string
}

export interface FooterNewsletterSettings extends NewsletterSettings {}

export interface NewsletterPopupSettings extends NewsletterSettings {
  enabled?: boolean
  frequency?: number
  delay?: number
}

export interface MembershipPromoSettings {
  enabled?: boolean
  title?: string
  description?: string
  buttonText?: string
  buttonLink?: string
}

export interface SocialLink {
  _key?: string
  platform: string
  url: string
}

export interface SiteSettings {
  title?: string
  description?: string
  logo?: LogoSettings
  socialSharing?: SocialSharingSettings
  contactInfo?: ContactInfo
  cookieConsent?: CookieConsentSettings
  globalAnnouncement?: GlobalAnnouncementSettings
  footerNewsletter?: FooterNewsletterSettings
  newsletterPopup?: NewsletterPopupSettings
  membershipPromo?: MembershipPromoSettings
  socialLinks?: SocialLink[]
  copyrightText?: string
  headerNav?: any[] // This will be typed as NavItem[] from navigation.d.ts
  footerNav?: any[]
  footerLegalLinks?: any[]
}
