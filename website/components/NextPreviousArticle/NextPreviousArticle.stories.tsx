import type { Meta, StoryObj } from '@storybook/react'
import NextPreviousArticle from './NextPreviousArticle'
import { SANITY_IMAGE_STUB_SOURCE } from '@/components/SanityImage/SanityImage.stub'
import React from 'react'
import styles from './NextPreviousArticle.stories.module.scss'
import { STUB_LINK } from '@/components/Link/Link.stub'

const meta = {
  title: 'Components/NextPreviousArticle',
  component: NextPreviousArticle,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    direction: {
      control: 'radio',
      options: ['next', 'previous'],
    },
  },
} satisfies Meta<typeof NextPreviousArticle>

export default meta

type Story = StoryObj<typeof meta>

export const Next: Story = {
  args: {
    direction: 'next',
    title: 'Article Headline',
    image: SANITY_IMAGE_STUB_SOURCE.ARTICLE,
    episodeTitle: {
      season: 8,
      episode: 821,
      date: '2025-01-29T00:00:00.000Z',
    },
    link: STUB_LINK.TEST_WITH_LABEL as SanityLink,
  },
}

export const Previous: Story = {
  args: {
    direction: 'previous',
    title: 'Article Headline',
    image: SANITY_IMAGE_STUB_SOURCE.ARTICLE,
    episodeTitle: {
      season: 8,
      episode: 821,
      date: '2025-01-29T00:00:00.000Z',
    },
    link: STUB_LINK.TEST_WITH_LABEL as SanityLink,
  },
}

export const WithoutEpisodeTitle: Story = {
  args: {
    direction: 'next',
    title: 'Article Headline',
    image: SANITY_IMAGE_STUB_SOURCE.ARTICLE,
    link: STUB_LINK.TEST_WITH_LABEL as SanityLink,
  },
}

const NextAndPreviousTemplate = () => {
  return (
    <div className={styles.nextPreviousContainer}>
      <div className={styles.articleItem}>
        <NextPreviousArticle
          direction="previous"
          title="Article Headline"
          image={SANITY_IMAGE_STUB_SOURCE.ARTICLE}
          episodeTitle={{
            season: 8,
            episode: 821,
            date: '2025-01-29T00:00:00.000Z',
          }}
          link={STUB_LINK.TEST_WITH_LABEL as SanityLink}
        />
      </div>
      <div className={styles.articleItem}>
        <NextPreviousArticle
          direction="next"
          title="Article Headline"
          image={SANITY_IMAGE_STUB_SOURCE.ARTICLE}
          episodeTitle={{
            season: 8,
            episode: 821,
            date: '2025-01-29T00:00:00.000Z',
          }}
          link={STUB_LINK.TEST_WITH_LABEL as SanityLink}
        />
      </div>
    </div>
  )
}

const NextAndPreviousLongTitleTemplate = () => {
  return (
    <div className={styles.nextPreviousContainer}>
      <div className={styles.articleItem}>
        <NextPreviousArticle
          direction="previous"
          title="This is a much longer article headline that will definitely break onto two lines"
          image={SANITY_IMAGE_STUB_SOURCE.ARTICLE}
          episodeTitle={{
            season: 8,
            episode: 821,
            date: '2025-01-29T00:00:00.000Z',
          }}
          link={STUB_LINK.TEST_WITH_LABEL as SanityLink}
        />
      </div>
      <div className={styles.articleItem}>
        <NextPreviousArticle
          direction="next"
          title="This is a much longer article headline that will definitely break onto two lines"
          image={SANITY_IMAGE_STUB_SOURCE.ARTICLE}
          episodeTitle={{
            season: 8,
            episode: 821,
            date: '2025-01-29T00:00:00.000Z',
          }}
          link={STUB_LINK.TEST_WITH_LABEL as SanityLink}
        />
      </div>
    </div>
  )
}

export const NextAndPrevious: Story = {
  args: {
    direction: 'next',
    title: 'Article Headline',
    image: SANITY_IMAGE_STUB_SOURCE.ARTICLE,
    link: STUB_LINK.TEST_WITH_LABEL as SanityLink,
  },
  parameters: {
    layout: 'fullscreen',
  },
  render: () => <NextAndPreviousTemplate />,
}

export const WithLongTitleNext: Story = {
  args: {
    direction: 'next',
    title: 'This is a much longer article headline that will definitely break onto two lines when displayed',
    image: SANITY_IMAGE_STUB_SOURCE.ARTICLE,
    episodeTitle: {
      season: 8,
      episode: 821,
      date: '2025-01-29T00:00:00.000Z',
    },
    link: STUB_LINK.TEST_WITH_LABEL as SanityLink,
  },
}

export const WithLongTitlePrevious: Story = {
  args: {
    direction: 'previous',
    title: 'This is a much longer article headline that will definitely break onto two lines when displayed',
    image: SANITY_IMAGE_STUB_SOURCE.ARTICLE,
    episodeTitle: {
      season: 8,
      episode: 821,
      date: '2025-01-29T00:00:00.000Z',
    },
    link: STUB_LINK.TEST_WITH_LABEL as SanityLink,
  },
}

export const NextAndPreviousWithLongTitles: Story = {
  args: {
    direction: 'next',
    title: 'This is a much longer article headline that will definitely break onto two lines when displayed',
    image: SANITY_IMAGE_STUB_SOURCE.ARTICLE,
    link: STUB_LINK.TEST_WITH_LABEL as SanityLink,
  },
  parameters: {
    layout: 'fullscreen',
  },
  render: () => <NextAndPreviousLongTitleTemplate />,
}
