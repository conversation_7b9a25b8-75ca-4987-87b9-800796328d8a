'use client'

import classnames from 'classnames'
import styles from './NextPreviousArticle.module.scss'
import SanityImage from '@/components/SanityImage/SanityImage'
import EpisodeTitle from '@/components/EpisodeTitle/EpisodeTitle'
import Link from '@/components/Link/Link'
import Button from '@/components/Button/Button'

const NextPreviousArticle = ({
  className,
  direction = 'next',
  title,
  image,
  episodeTitle,
  link,
}: NextPreviousArticleProps) => {
  const isNext = direction === 'next'
  const labelText = isNext ? 'Next Article' : 'Previous Article'

  if (!title || !image || !link) return null

  return (
    <Link link={link}>
      <div
        className={classnames(styles.NextPreviousArticle, className)}
        data-next-previous-article-direction={direction}
      >
        <div className={styles.content}>
          <div className={styles.imageWrapper}>
            <SanityImage
              source={image}
              isCover
              columns={{
                md: 3,
                sm: 5,
              }}
            />
          </div>
          <div className={styles.textContent}>
            <Button
              className={styles.button}
              element="span"
              label={labelText}
              icon={direction === 'next' ? 'arrowRight' : 'arrowLeft'}
              iconPosition={direction === 'next' ? 'right' : 'left'}
              style="bare"
              bareColor="slate"
              bareFont="eyebrow"
              disableHoverAnimation
            />
            <h3 className={styles.title}>{title}</h3>
            {episodeTitle && (
              <EpisodeTitle
                hasIcon={false}
                size="sm"
                {...episodeTitle}
              />
            )}
          </div>
        </div>
      </div>
    </Link>
  )
}

NextPreviousArticle.displayName = 'NextPreviousArticle'

export default NextPreviousArticle
