.ul {
  padding-left: 1em;
}

.li {
  position: relative;
  padding-left: 0.5em;
  list-style: initial;

  &:not(:last-child) {
    margin-bottom: 0.2em;
  }
}

.buttonLink {
  display: block;
  box-sizing: border-box;
  margin: px(24) 0;

  .button {
    display: inline-flex;
  }
}

// Storybook
.StorybookContainer {
  @include rich-text-base-styling;

  [data-h2] {
    @include font-heading-sm;
  }
}
