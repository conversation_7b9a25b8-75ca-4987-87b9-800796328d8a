import type { Meta, StoryObj } from '@storybook/react'
import RichTextV2 from './RichTextV2'
import RICH_TEXT_STUB_DATA from './RichTextV2.stub'
import styles from './RichTextV2.module.scss'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const StyledRichTextV2 = ({ content }: any) => {
  return (
    <div className={styles.StorybookContainer}>
      <RichTextV2 content={content} />
    </div>
  )
}

const meta = {
  title: 'Components/RichTextV2',
  component: RichTextV2,
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof RichTextV2>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    content: RICH_TEXT_STUB_DATA.EXAMPLE_1_H2_P_UL,
  },
  render: StyledRichTextV2,
}
