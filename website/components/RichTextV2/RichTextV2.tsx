'use client'

import { PortableText, PortableTextReactComponents } from '@portabletext/react'
import Link from '@/components/Link/Link'
import styles from './RichTextV2.module.scss'
import SanityButtonComponent from '../_sections/SanityButton/SanityButton'

export const portableTextSerializer: Partial<PortableTextReactComponents> = {
  block: {
    normal: ({ children }) => <p data-p>{children}</p>,
    h1: ({ children }) => <h1 data-h1>{children}</h1>,
    h2: ({ children }) => <h2 data-h2>{children}</h2>,
    h3: ({ children }) => <h3 data-h3>{children}</h3>,
    h4: ({ children }) => <h4 data-h4>{children}</h4>,
  },
  listItem: {
    bullet: ({ children }) => (
      <li
        className={styles.li}
        data-li
      >
        {children}
      </li>
    ),
  },
  list: {
    bullet: ({ children }) => (
      <ul
        className={styles.ul}
        data-ul
      >
        {children}
      </ul>
    ),
  },
  marks: {
    link: ({ value, text }) => {
      return (
        <Link
          link={{
            ...value,
            label: text,
          }}
        />
      )
    },
  },
  types: {
    button: ({ value }) => {
      return (
        <SanityButtonComponent
          className={styles.button}
          linkClassName={styles.buttonLink}
          {...value}
        />
      )
    },
  },
}

const RichTextV2 = ({ content, serializer }: SanityContentBlockProps) => {
  return (
    <PortableText
      value={content}
      onMissingComponent={false}
      components={serializer || portableTextSerializer}
    />
  )
}

RichTextV2.displayName = 'RichTextV2'

export default RichTextV2
