'use client'

import classnames from 'classnames'
import styles from './CardCarousel.module.scss'
import Swiper from 'swiper'
import { SwiperOptions } from 'swiper/types'
import { CSSProperties, useEffect, useMemo, useRef } from 'react'
import useWindowResize from '@/hooks/use-window-resize'
import Card from '@/components/Card/Card'
import Button from '@/components/Button/Button'
import 'swiper/css'
import useBreakpoint from '@/hooks/use-breakpoint'

const SLIDES_PER_VIEW = {
  desktop: 4,
  mobile: 1,
}

const CardCarousel = ({ className, items }: CardCarouselProps) => {
  const carouselInstance = useRef<Swiper | null>(null)
  const $carousel = useRef<HTMLDivElement | null>(null)
  const resizeKey = useWindowResize()
  const { isMobile } = useBreakpoint()

  const perView = useMemo(() => {
    const requiredLength = isMobile ? SLIDES_PER_VIEW.mobile : SLIDES_PER_VIEW.desktop
    return requiredLength
  }, [isMobile])

  const isLessThan = useMemo(() => {
    return items.length <= perView
  }, [items.length, perView])

  const gridLength = useMemo(() => {
    if (isMobile) {
      return 1
    } else {
      return Math.max(items.length, 3)
    }
  }, [items.length, isMobile])

  useEffect(() => {
    if (!$carousel.current) return

    if (carouselInstance.current) {
      carouselInstance.current.destroy()
    }

    if (isLessThan) return

    const settings: SwiperOptions = {
      slidesPerView: perView,
      loop: false,
      spaceBetween: isMobile ? 16 : 28,
    }

    carouselInstance.current = new Swiper($carousel.current, settings)

    return () => {
      if (carouselInstance.current) {
        carouselInstance.current.destroy()
      }
    }
  }, [resizeKey, isMobile, perView, isLessThan])

  if (!items?.length) return null

  return (
    <div
      className={classnames(styles.CardCarousel, className, { [styles.isLessThan]: isLessThan })}
      style={
        {
          '--card-carousel-count': gridLength,
        } as CSSProperties
      }
    >
      <div className={styles.inner}>
        <div
          className={styles.carousel}
          ref={$carousel}
        >
          {!isMobile && !isLessThan && (
            <>
              <div className={styles.nextButtonContainer}>
                <Button
                  icon="caretRight"
                  className={styles.nextButton}
                  onClick={() => carouselInstance.current?.slideNext()}
                />
              </div>
              <div className={styles.prevButtonContainer}>
                <Button
                  icon="caretLeft"
                  className={styles.prevButton}
                  onClick={() => carouselInstance.current?.slidePrev()}
                />
              </div>
            </>
          )}
          <ul className={classnames(styles.carouselInner, 'swiper-wrapper')}>
            {items.map((item, i) => (
              <li
                className={classnames(styles.carouselItem, 'swiper-slide')}
                key={i}
              >
                <Card {...item} />
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
}

CardCarousel.displayName = 'CardCarousel'

export default CardCarousel
