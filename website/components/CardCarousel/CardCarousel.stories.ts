import type { Meta, StoryObj } from '@storybook/react'
import CardCarousel from './CardCarousel'
import { CARD_BASIC_PROPS } from '@/components/Card/Card.stub'

const CARDS = Array(10).fill(CARD_BASIC_PROPS)
const CARDS_LESS_THAN = Array(4).fill(CARD_BASIC_PROPS)

const meta = {
  title: 'Carousels/CardCarousel',
  component: CardCarousel,
  parameters: {
    layout: 'fullscreen',
  },
} satisfies Meta<typeof CardCarousel>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    items: CARDS,
  },
}

export const LessCardsThanRequired: Story = {
  args: {
    items: CARDS_LESS_THAN,
  },
}
