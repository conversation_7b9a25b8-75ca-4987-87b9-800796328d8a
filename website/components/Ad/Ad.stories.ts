import type { Meta, StoryObj } from '@storybook/react'
import Ad from './Ad'

const meta = {
  title: 'Components/Ad',
  component: Ad,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'radio',
      options: ['billboard', 'skyscraper'],
      description: 'Type of advertisement',
    },
    id: {
      control: 'text',
      description: 'HTML ID for the ad container',
    },
    className: {
      control: 'text',
      description: 'Optional CSS class name',
    },
  },
} satisfies Meta<typeof Ad>

export default meta

type Story = StoryObj<typeof meta>

export const Billboard: Story = {
  args: {
    type: 'billboard',
    id: 'ad-billboard-1',
  },
  parameters: {
    docs: {
      description: {
        story:
          'Billboard ads are horizontal banners typically displayed at the top or bottom of content at full width.',
      },
    },
  },
}

export const Skyscraper: Story = {
  args: {
    type: 'skyscraper',
    id: 'ad-skyscraper-1',
  },
  parameters: {
    docs: {
      description: {
        story: 'Skyscraper ads are vertical banners typically displayed on the sides of content at a fixed width.',
      },
    },
  },
}

export const WithCustomClassName: Story = {
  args: {
    type: 'billboard',
    id: 'ad-custom-class',
    className: 'custom-ad-class',
  },
  parameters: {
    docs: {
      description: {
        story: 'Ads can be customized with additional CSS classes for specific styling needs.',
      },
    },
  },
}
