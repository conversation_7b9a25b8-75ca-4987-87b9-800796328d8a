import React from 'react'
import classnames from 'classnames'
import styles from './Ad.module.scss'

const Ad = ({ className, title, type, id }: AdProps) => {
  return (
    <div
      data-ad-type={type}
      className={classnames(styles.Ad, className)}
    >
      <div className={styles.content}>
        <div className={styles.title}>{title || 'ADVERTISEMENT'}</div>
        <div
          className={styles.placeholder}
          id={id}
        />
      </div>
    </div>
  )
}

Ad.displayName = 'Ad'

export default Ad
