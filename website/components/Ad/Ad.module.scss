.Ad {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--gray-quiet);
  max-width: 100%;
  width: 100%;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: px(9) px(12);

  @include bp(tablet) {
    padding: px(12) var(--gutter);
  }
}

.title {
  @include font-heading-eyebrow-sm;
  color: var(--slate);
  margin-bottom: px(8);

  @include bp(tablet) {
    margin-bottom: px(12);
  }
}

.placeholder {
  background-color: rgba($black, 0.1);
  width: 100%;

  [data-ad-type='billboard'] & {
    aspect-ratio: 970 / 250;
    max-width: px(960);
  }

  [data-ad-type='skyscraper'] & {
    aspect-ratio: 368 / 500;
    max-width: px(360);
  }
}
