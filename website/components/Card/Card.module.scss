.Card {
  display: block;

  --card-margin: #{px(4)};
  --mobile-card-image-width: #{px(160)};

  @include bp(tablet) {
    --card-margin: #{px(8)};
  }
}

.cardInner {
  [data-card-orientation='landscape'] & {
    display: grid;
  }

  // Landscape xs
  [data-card-orientation='landscape'][data-card-image-orientation='square-xs'] & {
    grid-template-columns: #{px(64)} 1fr;
  }

  [data-card-orientation='landscape'][data-card-image-orientation='square-xs'][data-card-image-side='right'] & {
    grid-template-columns: 1fr #{px(64)};
  }

  // Landscape, sm
  [data-card-orientation='landscape'][data-card-image-orientation='square-sm'] &,
  [data-card-orientation='landscape'][data-card-image-orientation='magazine'] & {
    grid-template-columns: #{px(160)} 1fr;
  }

  [data-card-orientation='landscape'][data-card-image-orientation='square-sm'][data-card-image-side='right'] &,
  [data-card-orientation='landscape'][data-card-image-orientation='magazine'][data-card-image-side='right'] & {
    grid-template-columns: 1fr #{px(160)};
  }

  // Landscape, md
  [data-card-orientation='landscape'][data-card-image-orientation='square-md'] &,
  [data-card-orientation='landscape'][data-card-image-orientation='square'] &,
  [data-card-orientation='landscape'][data-card-image-orientation='4:3'] & {
    grid-template-columns: var(--mobile-card-image-width) 1fr;

    @include bp(tablet) {
      grid-template-columns: #{px(240)} 1fr;
    }
  }

  [data-card-orientation='landscape'][data-card-image-orientation='square-md'][data-card-image-side='right'] &,
  [data-card-orientation='landscape'][data-card-image-orientation='square'][data-card-image-side='right'] &,
  [data-card-orientation='landscape'][data-card-image-orientation='4:3'][data-card-image-side='right'] & {
    grid-template-columns: 1fr var(--mobile-card-image-width);

    @include bp(tablet) {
      grid-template-columns: 1fr #{px(240)};
    }
  }

  // Landscape, lg
  [data-card-orientation='landscape'][data-card-image-orientation='square-lg'] &,
  [data-card-orientation='landscape'][data-card-image-orientation='16:9'] & {
    grid-template-columns: var(--mobile-card-image-width) 1fr;

    @include bp(tablet) {
      grid-template-columns: #{px(312)} 1fr;
    }
  }

  [data-card-orientation='landscape'][data-card-image-orientation='square-lg'][data-card-image-side='right'] &,
  [data-card-orientation='landscape'][data-card-image-orientation='16:9'][data-card-image-side='right'] & {
    grid-template-columns: 1fr var(--mobile-card-image-width);

    @include bp(tablet) {
      grid-template-columns: 1fr #{px(312)};
    }
  }

  // Landscape, portrait
  [data-card-orientation='portrait'] & {
    display: flex;
    flex-direction: column;
  }
}

.imageWrapper {
  position: relative;
}

.imageWrapperAspectRatio {
  display: block;
  background-color: var(--gray-quiet);

  [data-card-image-orientation='square'] &,
  [data-card-image-orientation='square-xs'] &,
  [data-card-image-orientation='square-sm'] &,
  [data-card-image-orientation='square-md'] &,
  [data-card-image-orientation='square-lg'] & {
    aspect-ratio: var(--aspect-ratio-square);
  }

  [data-card-image-orientation='4:3'] & {
    aspect-ratio: var(--aspect-ratio-4-3);
  }

  [data-card-image-orientation='16:9'] & {
    aspect-ratio: var(--aspect-ratio-16-9);
  }

  [data-card-image-orientation='magazine'] & {
    aspect-ratio: var(--aspect-ratio-magazine);
  }

  // Landscape-specific styling
  [data-card-image-orientation='square-xs'] & {
    position: relative;
  }
}

.image {
  @include position-100(absolute);
}

.bookmarkButton {
  position: absolute;
  bottom: px(16);
  right: px(16);
}

.content {
  display: block;

  [data-card-image-side='right'][data-card-orientation='landscape'] & {
    order: -1;
  }
}

.contentInner {
  display: flex;
  flex-direction: column;

  [data-card-orientation='landscape'] & {
    padding: 0 px(16);
    height: 100%;
  }

  [data-card-orientation='landscape'][data-card-image-side='right']:not(.hasPadding) & {
    padding-left: 0;
  }

  [data-card-orientation='landscape'][data-card-image-side='left']:not(.hasPadding) & {
    padding-right: 0;
  }

  .hasPadding & {
    padding: px(16);
    background-color: var(--white);
    border: 1px solid rgba($navy-dark, 0.2);
  }

  [data-card-orientation='portrait']:not(.hasPadding) & {
    margin-top: px(16);
  }
}

.tag,
.episodeTitle {
  margin-bottom: var(--card-margin);
}

.title {
  @include font-heading-md;
  color: var(--navy-dark);

  [data-heading-size='sm'] & {
    @include font-heading-sm;
  }

  [data-heading-size='lg'] & {
    @include font-heading-xl;
  }
}

.description {
  @include font-text-base-snug;
  color: var(--navy-dark);
  margin-top: px(8);

  [data-card-orientation='landscape'][data-card-image-orientation='square-xs'] &,
  [data-card-orientation='landscape'][data-card-image-orientation='square-sm'] &,
  [data-card-orientation='landscape'][data-card-image-orientation='square-md'] & {
    margin-top: px(4);
  }

  .capLines & {
    overflow: hidden;
    text-overflow: ellipsis;

    /* stylelint-disable-next-line value-no-vendor-prefix */
    display: -webkit-box;
    -webkit-line-clamp: var(--capped-lines);
    line-clamp: var(--capped-lines);
    -webkit-box-orient: vertical;
  }
}

.additionalText {
  @include font-byline-md;
  color: var(--slate);
  margin-top: var(--card-margin);
}

.authorAndDate {
  margin-top: var(--card-margin);
}

.dateSquare {
  @include box(px(56));
  position: absolute;
  top: px(16);
  right: px(16);
  z-index: 2;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--white);
  color: var(--navy-dark);
}

.dateSquare__day {
  @include font-heading-md;
}

.dateSquare__month {
  @include font-heading-eyebrow-md;
}

.price {
  margin-top: var(--card-margin);
}

.price__default,
.price__memberPrice {
  @include font-text-base;
  font-weight: 700;
  color: var(--navy-dark);
  display: block;
}

.price__memberPrice {
  color: var(--loud-warm);
}

button.arrowButton {
  margin-top: var(--card-margin);
  pointer-events: none;
  transition: opacity $transition-short;

  .isHover & {
    opacity: 0.7;
  }
}

.starsRating {
  margin-top: px(11);
}

/* ===============================================
Storybook 
=============================================== */

.storybookPortraitContainer,
.storybookLandscapeContainer {
  width: 100%;
  margin: 0 auto;
}

.storybookPortraitContainer {
  max-width: px(400);
}

.storybookLandscapeContainer {
  max-width: px(600);
}
