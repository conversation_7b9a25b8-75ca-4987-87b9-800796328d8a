'use client'

import { type ElementType } from 'react'
import styles from './Section.module.scss'

/* INJECT_SECTIONS_IMPORT */
import SearchBarAndContent from '@/components/_sections/SearchBarAndContent/SearchBarAndContent'
import AdSection from '@/components/_sections/AdSection/AdSection'
import ShowAllPageSectionExamples from '@/components/_sections/ShowAllPageSectionExamples/ShowAllPageSectionExamples'
import Tip from '@/components/_sections/Tip/Tip'
import ImageCarouselAndText from '@/components/_sections/ImageCarouselAndText/ImageCarouselAndText'
import LogoSliderSection from '@/components/_sections/LogoSliderSection/LogoSliderSection'
import TextAndImageSection from '@/components/_sections/TextAndImageSection/TextAndImageSection'
import TextAndPointsWithImageSection from '@/components/_sections/TextAndPointsWithImageSection/TextAndPointsWithImageSection'
import HeaderWithSubnav from '@/components/_sections/HeaderWithSubnav/HeaderWithSubnav'
import MultiColumnContent from '@/components/_sections/MultiColumnContent/MultiColumnContent'
import ContentList from '@/components/_sections/ContentList/ContentList'
import ExampleSection from '@/components/_sections/ExampleSection/ExampleSection'
import RichTextSection from '@/components/_sections/RichTextSection/RichTextSection'
import ClassCalendar from '@/components/_sections/ClassCalendar/ClassCalendar'
import TextAndStaticImageSection from '@/components/_sections/TextAndStaticImage/TextAndStaticImage'
import BigImageSection from '@/components/_sections/BigImageSection/BigImageSection'
import ImageAndRichTextWithButton from '@/components/_sections/ImageAndRichTextWithButton/ImageAndRichTextWithButton'
import SubscriptionPlansSection from '@/components/_sections/SubscriptionPlansSection/SubscriptionPlansSection'
import RecipePage from '@/components/_sections/RecipePage/RecipePage'
import ArticlePage from '@/components/_sections/ArticlePage/ArticlePage'
import TitleTextAndCtaSection from '@/components/_sections/TitleTextAndCtaSection/TitleTextAndCtaSection'
import ThreeColumnContentSection from '@/components/_sections/ThreeColumnContentSection/ThreeColumnContentSection'
import EyebrowAndTitleSection from '@/components/_sections/EyebrowAndTitleSection/EyebrowAndTitleSection'

const SECTIONS: {
  [key: string]: ElementType
} = {
  /* INJECT_SECTIONS_COMPONENT_TYPE */
  searchBarAndContent: SearchBarAndContent,
  adSection: AdSection,
  subscriptionPlansSection: SubscriptionPlansSection,
  showAllPageSectionExamples: ShowAllPageSectionExamples,
  tip: Tip,
  imageCarouselAndText: ImageCarouselAndText,
  logoSliderSection: LogoSliderSection,
  textAndImageSection: TextAndImageSection,
  textAndPointsWithImageSection: TextAndPointsWithImageSection,
  headerWithSubnav: HeaderWithSubnav,
  multiColumnContent: MultiColumnContent,
  contentList: ContentList,
  exampleSection: ExampleSection,
  richTextSection: RichTextSection,
  classCalendar: ClassCalendar,
  textAndStaticImageSection: TextAndStaticImageSection,
  bigImageSection: BigImageSection,
  imageAndRichTextWithButton: ImageAndRichTextWithButton,
  recipePage: RecipePage,
  articlePage: ArticlePage,
  titleTextAndCtaSection: TitleTextAndCtaSection,
  threeColumnContentSection: ThreeColumnContentSection,
  eyebrowAndTitleSection: EyebrowAndTitleSection,
}

interface SectionProps {
  section: SanityAllSectionTypes
}

export function Section({ section }: SectionProps) {
  const SectionComponent = SECTIONS[section._type as keyof typeof SECTIONS]

  if (!SectionComponent) {
    console.warn(`Unknown section type: ${section._type}`)
    return null
  }

  return (
    <div className={styles.section}>
      <SectionComponent {...section} />
    </div>
  )
}
