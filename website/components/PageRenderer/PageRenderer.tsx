import { PageRendererProps } from '@/types/PageRenderer'
import { Section } from './Section'

/**
 * Renders a page with its sections, handling both direct sections and shared sections
 */
export default function PageRenderer({ content }: PageRendererProps) {
  return (
    <>
      {content?.map((section: SanityAllSectionTypes, index: number) => (
        <Section
          key={index}
          section={section}
        />
      ))}
    </>
  )
}
