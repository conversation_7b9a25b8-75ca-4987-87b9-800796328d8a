import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './ImageCarousel'
import { SANITY_IMAGE_STUB_SOURCE } from '@/components/SanityImage/SanityImage.stub'
import { SanityImage } from '@/types/sanity/SanityImage'

const GALLERY_IMAGES = [
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE_2,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE_2,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE_2,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE_2,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE_2,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE_2,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE_2,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
  SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE_2,
] as SanityImage[]

const meta = {
  title: 'Components/ImageCarousel',
  component: ImageCarousel,
  parameters: {
    layout: 'fullscreen',
  },
} satisfies Meta<typeof ImageCarousel>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    images: GALLERY_IMAGES,
  },
}

export const LessImages: Story = {
  args: {
    images: [
      SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
      SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE_2,
      SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
    ],
  },
}
