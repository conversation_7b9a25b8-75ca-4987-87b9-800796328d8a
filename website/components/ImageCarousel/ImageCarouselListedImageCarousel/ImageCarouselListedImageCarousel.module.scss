/* stylelint-disable-next-line block-no-empty */
.ImageCarouselListedImageCarousel {
  padding-top: px(8);

  @include bp(tablet) {
    padding-top: px(16);
  }
}

.carousel {
  width: 100%;
  position: relative;
  overflow: hidden;

  @include hover {
    .nextButton,
    .prevButton {
      opacity: 1;
    }
  }
}

.carouselInner {
  width: 100%;
}

.carouselItem {
  @include reset-button;
  aspect-ratio: var(--image-carousel-aspect-ratio);
  position: relative;
  background-color: var(--white);
  cursor: pointer;
}

.image {
  @include position-100(absolute);
  object-fit: cover;
  opacity: 0.4 !important;
  transition: opacity $transition-short;

  .isActive & {
    opacity: 1 !important;
  }
}

.nextButton,
.prevButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  opacity: 0;
  transition: opacity $transition-short;
}

.nextButton {
  right: px(10);
}

.prevButton {
  left: px(10);
}
