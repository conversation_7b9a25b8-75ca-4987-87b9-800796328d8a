'use client'

import classnames from 'classnames'
import styles from './ImageCarouselListedImageCarousel.module.scss'
import Swiper from 'swiper'
import { SwiperOptions } from 'swiper/types'
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef } from 'react'
import useWindowResize from '@/hooks/use-window-resize'
import SanityImage from '@/components/SanityImage/SanityImage'
import { SanityImage as SanityImageType } from '@/types/sanity/SanityImage'
import Button from '@/components/Button/Button'
import 'swiper/css'
import useBreakpoint from '@/hooks/use-breakpoint'

const IMAGE_CAP = {
  DESKTOP: 6,
  MOBILE: 4,
}

type FeaturedImageCarouselProps = {
  className?: string
  images: SanityImageType[]
  activeIndex: number

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setActiveIndex: any
}

export type ListedImageCarouselImperativeHandle = {
  getInstance: () => Swiper | null
}

const ImageCarouselListedImageCarousel = forwardRef<ListedImageCarouselImperativeHandle, FeaturedImageCarouselProps>(
  ({ className, images, setActiveIndex, activeIndex }, ref) => {
    const carouselInstance = useRef<Swiper | null>(null)
    const $carousel = useRef<HTMLDivElement | null>(null)
    const resizeKey = useWindowResize()
    const { isMobile } = useBreakpoint()
    const renderButtons = useMemo(() => {
      if (isMobile) {
        return images.length > IMAGE_CAP.MOBILE
      }

      return images.length > IMAGE_CAP.DESKTOP
    }, [isMobile, images.length])

    useEffect(() => {
      if (!$carousel.current) return

      if (carouselInstance.current) {
        carouselInstance.current.destroy()
      }

      const slidesPerView = isMobile ? IMAGE_CAP.MOBILE : IMAGE_CAP.DESKTOP

      const settings: SwiperOptions = {
        slidesPerView,
        slidesPerGroup: slidesPerView,
        loop: true,
        spaceBetween: isMobile ? 8 : 16,
      }

      carouselInstance.current = new Swiper($carousel.current, settings)

      return () => {
        if (carouselInstance.current) {
          carouselInstance.current.destroy()
        }
      }
    }, [resizeKey, isMobile])

    useImperativeHandle(ref, () => ({
      getInstance: () => {
        return carouselInstance.current
      },
    }))

    if (!images?.length) return null

    return (
      <div className={classnames(styles.ImageCarouselListedImageCarousel, className)}>
        <div
          className={styles.carousel}
          ref={$carousel}
        >
          {renderButtons && (
            <>
              <Button
                icon="caretRight"
                className={styles.nextButton}
                onClick={() => {
                  setActiveIndex((prev: number) => {
                    if (prev === images.length - 1) return 0
                    return prev + 1
                  })
                }}
              />
              <Button
                icon="caretLeft"
                className={styles.prevButton}
                onClick={() => {
                  setActiveIndex((prev: number) => {
                    if (prev === 0) return images.length - 1
                    return prev - 1
                  })
                }}
              />
            </>
          )}
          <ul className={classnames(styles.carouselInner, 'swiper-wrapper')}>
            {images.map((image, i) => (
              <button
                className={classnames(styles.carouselItem, 'swiper-slide', { [styles.isActive]: activeIndex === i })}
                key={i}
                onClick={() => {
                  setActiveIndex(i)
                }}
                aria-label={`Go to carousel item ${i + 1}`}
              >
                <SanityImage
                  source={image}
                  className={styles.image}
                  aspectRatio={1}
                  columns={{
                    md: 2,
                    sm: 4,
                  }}
                />
              </button>
            ))}
          </ul>
        </div>
      </div>
    )
  },
)

ImageCarouselListedImageCarousel.displayName = 'ImageCarouselListedImageCarousel'

export default ImageCarouselListedImageCarousel
