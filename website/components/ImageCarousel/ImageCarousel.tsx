'use client'

import classnames from 'classnames'
import styles from './ImageCarousel.module.scss'
import ImageCarouselFeaturedImageCarousel, {
  FeaturedImageCarouselImperativeHandle,
} from './ImageCarouselFeaturedImageCarousel/ImageCarouselFeaturedImageCarousel'
import ImageCarouselListedImageCarousel, {
  ListedImageCarouselImperativeHandle,
} from './ImageCarouselListedImageCarousel/ImageCarouselListedImageCarousel'
import { useEffect, useRef, useState } from 'react'

const ImageCarousel = ({ className, images }: ImageCarouselProps) => {
  const [activeIndex, setActiveIndex] = useState(0)
  const $featureCarousel = useRef<FeaturedImageCarouselImperativeHandle | null>(null)
  const $listedCarousel = useRef<ListedImageCarouselImperativeHandle | null>(null)

  useEffect(() => {
    if (!$featureCarousel.current || !$listedCarousel.current) return
    $featureCarousel.current.getInstance()?.slideTo(activeIndex)
    $listedCarousel.current.getInstance()?.slideTo(activeIndex)
  }, [activeIndex])

  if (!images?.length) return null

  return (
    <div className={classnames(styles.ImageCarousel, className)}>
      <div className={styles.inner}>
        <ImageCarouselFeaturedImageCarousel
          activeIndex={activeIndex}
          images={images}
          setActiveIndex={setActiveIndex}
          ref={$featureCarousel}
        />
        <ImageCarouselListedImageCarousel
          activeIndex={activeIndex}
          images={images}
          setActiveIndex={setActiveIndex}
          ref={$listedCarousel}
        />
      </div>
    </div>
  )
}

ImageCarousel.displayName = 'ImageCarousel'

export default ImageCarousel
