.ImageCarouselFeaturedImageCarousel {
  width: 100%;
}

.carousel {
  width: 100%;
  position: relative;
}

.carouselInner {
  width: 100%;
}

.carouselItem {
  aspect-ratio: var(--image-carousel-aspect-ratio);
  position: relative;
}

.image {
  @include position-100(absolute);
  object-fit: cover;
}

.nextButton,
.prevButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.nextButton {
  right: px(25);
}

.prevButton {
  left: px(25);
}
