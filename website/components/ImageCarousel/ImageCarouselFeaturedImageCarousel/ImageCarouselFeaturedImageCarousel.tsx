'use client'

import classnames from 'classnames'
import styles from './ImageCarouselFeaturedImageCarousel.module.scss'
import Swiper from 'swiper'
import { SwiperOptions } from 'swiper/types'
import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react'
import useWindowResize from '@/hooks/use-window-resize'
import SanityImage from '@/components/SanityImage/SanityImage'
import { SanityImage as SanityImageType } from '@/types/sanity/SanityImage'
import { EffectFade } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/effect-fade'
import Button from '@/components/Button/Button'

// eslint-disable-next-line react-hooks/rules-of-hooks
Swiper.use([EffectFade])

type FeaturedImageCarouselProps = {
  className?: string
  images: SanityImageType[]
  activeIndex: number

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setActiveIndex: any
}

export type FeaturedImageCarouselImperativeHandle = {
  getInstance: () => Swiper | null
}

const ImageCarouselFeaturedImageCarousel = forwardRef<
  FeaturedImageCarouselImperativeHandle,
  FeaturedImageCarouselProps
>(({ className, images, setActiveIndex }, ref) => {
  const carouselInstance = useRef<Swiper | null>(null)
  const $carousel = useRef<HTMLDivElement | null>(null)
  const resizeKey = useWindowResize()

  useEffect(() => {
    if (!$carousel.current) return

    if (carouselInstance.current) {
      carouselInstance.current.destroy()
    }

    const settings: SwiperOptions = {
      slidesPerView: 1,
      effect: 'fade',
      fadeEffect: { crossFade: true },
      loop: true,
    }

    carouselInstance.current = new Swiper($carousel.current, settings)

    return () => {
      if (carouselInstance.current) {
        carouselInstance.current.destroy()
      }
    }
  }, [resizeKey])

  useImperativeHandle(ref, () => ({
    getInstance: () => {
      return carouselInstance.current
    },
  }))

  if (!images?.length) return null

  return (
    <div className={classnames(styles.ImageCarouselFeaturedImageCarousel, className)}>
      <div
        className={styles.carousel}
        ref={$carousel}
      >
        <Button
          icon="caretRight"
          className={styles.nextButton}
          onClick={() => {
            setActiveIndex((prev: number) => {
              if (prev === images.length - 1) return 0
              return prev + 1
            })
          }}
        />
        <Button
          icon="caretLeft"
          className={styles.prevButton}
          onClick={() => {
            setActiveIndex((prev: number) => {
              if (prev === 0) return images.length - 1
              return prev - 1
            })
          }}
        />
        <ul className={classnames(styles.carouselInner, 'swiper-wrapper')}>
          {images.map((image, i) => (
            <li
              className={classnames(styles.carouselItem, 'swiper-slide')}
              key={i}
            >
              <SanityImage
                source={image}
                className={styles.image}
                aspectRatio={1}
                columns={8}
              />
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
})

ImageCarouselFeaturedImageCarousel.displayName = 'ImageCarouselFeaturedImageCarousel'

export default ImageCarouselFeaturedImageCarousel
