.AudioPlayer {
  width: 100%;
  position: relative;
  display: grid;
  grid-template-areas:
    'title'
    'progress'
    'time'
    'controls'
    'share-controls';
  grid-template-rows: auto auto auto auto auto;

  @include bp(tablet) {
    grid-template: auto auto / auto 1fr auto;
    grid-template-areas:
      'controls title share-controls'
      'progress progress progress';
    padding-top: px(16);
    border-bottom: none;
    border-top: 1px solid var(--slate);
  }
}

.progressBarContainer {
  grid-area: progress;
  width: 100%;

  @include bp(tablet) {
    margin-top: px(16);
  }
}

.progressBarWrapper {
  position: relative;
  height: px(6);
  background-color: rgba($slate, 0.2);

  @include bp(tablet) {
    height: px(8);
  }
}

.progressBarBackground {
  @include position-100(absolute);
  background-color: rgba($slate, 0.2);
}

.progressBar {
  @include position-100(absolute);
  background-color: var(--slate);
  transition: width var(--transition-short) linear;
}

.progressHandle {
  position: absolute;
  top: 50%;
  width: px(12);
  height: px(12);
  background-color: var(--slate);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: grab;
  transition: opacity var(--transition-short) ease;

  @include bp(tablet) {
    width: px(16);
    height: px(16);
    opacity: 0;

    @include hover {
      opacity: 1;
    }
  }

  &:active {
    cursor: grabbing;
    opacity: 1;
  }
}

.timeWrapperMobile {
  grid-area: time;
  @include font-heading-xs;
  color: var(--slate);
  display: flex;
  justify-content: space-between;
  margin-top: px(12);

  @include bp(tablet) {
    display: none;
  }
}

.mainControls {
  grid-area: controls;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: px(12) 0;
  border-bottom: 1px solid rgba($navy-dark, 0.2);

  @include bp(tablet) {
    justify-content: flex-start;
    margin-top: 0;
    border-top: none;
    padding: 0;
    border-bottom: none;
    gap: px(12);
  }
}

.controlButton {
  @include reset-button;
  color: var(--navy-dark);
}

.controlIcon {
  width: px(36);

  @include bp(tablet) {
    width: px(32);
  }
}

.shareIcon {
  width: px(24);

  @include bp(tablet) {
    width: px(32);
  }
}

.titleMobile {
  grid-area: title;
  @include font-heading-xs;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: px(12);

  @include bp(tablet) {
    display: none;
  }
}

.titleTimeWrapperDesktop {
  display: none;

  @include bp(tablet) {
    grid-area: title;
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    padding: 0 px(12);
  }
}

.titleDesktop {
  @include font-heading-xs;
  display: block;
  margin-bottom: px(4);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.timeDisplayDesktop {
  @include font-heading-xs;
  color: var(--slate);
}

.shareControls {
  grid-area: share-controls;
  display: flex;
  justify-content: flex-end;
  padding-top: px(12);

  @include bp(tablet) {
    padding-top: 0;
  }
}
