import type { Meta, StoryObj } from '@storybook/react'
import AudioPlayer from './AudioPlayer'

const meta = {
  title: 'Components/AudioPlayer',
  component: AudioPlayer,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    sourceUrl: { control: 'text' },
    title: { control: 'text' },
  },
} satisfies Meta<typeof AudioPlayer>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    sourceUrl:
      'https://www.podtrac.com/pts/redirect.mp3/tracking.swap.fm/track/sblTq32fyWAjsHzze2LG/dovetail.prxu.org/9135/530f0492-3534-419f-800a-c9752f207fd7/MSR910_Podcast.mp3',
    title: 'Behind the Scenes of Top Chef with <PERSON>',
  },
}

export const ShortTitle: Story = {
  args: {
    sourceUrl:
      'https://www.podtrac.com/pts/redirect.mp3/dovetail.prxu.org/9135/0cedd3dc-78a8-4c4c-8306-fa3751d3cda5/MSR805RR_ACast.mp3',
    title: '10 Biggest Food Lies',
  },
}

export const LongTitle: Story = {
  args: {
    sourceUrl:
      'https://www.podtrac.com/pts/redirect.mp3/tracking.swap.fm/track/sblTq32fyWAjsHzze2LG/dovetail.prxu.org/9135/cc9ebf21-6f13-44e8-aeff-b246630babc4/Final_Vegas_Episode.mp3',
    title: 'Chris\' Excellent Las Vegas Adventure! Pirate Cocktails, Peking Duck and Following the Rat Pack',
  },
}

export const VeryLongTitle: Story = {
  args: {
    sourceUrl:
      'https://www.podtrac.com/pts/redirect.mp3/tracking.swap.fm/track/sblTq32fyWAjsHzze2LG/dovetail.prxu.org/9135/cc9ebf21-6f13-44e8-aeff-b246630babc4/Final_Vegas_Episode.mp3',
    title:
      'The Ultimate Culinary Journey Through Southeast Asia: Exploring Street Food, Traditional Cooking Techniques, and Secret Family Recipes from Thailand, Vietnam, and Malaysia',
  },
}
