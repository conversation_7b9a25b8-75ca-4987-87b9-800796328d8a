'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import classnames from 'classnames'
import styles from './AudioPlayer.module.scss'
import Icon from '@/components/Icon/Icon'
import { formatTime } from '@/lib/utils/time'

const SKIP_BACK_SECONDS = 10
const SKIP_FORWARD_SECONDS = 30

const AudioPlayer = ({ className, sourceUrl, title }: AudioPlayerProps) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [duration, setDuration] = useState(0)
  const [isDragging, setIsDragging] = useState(false)

  // Refs for audio element and progress bar
  const audioRef = useRef<HTMLAudioElement>(null)
  const progressBarRef = useRef<HTMLDivElement>(null)

  // Refs for elements that need direct updates
  const progressBarElementRef = useRef<HTMLDivElement>(null)
  const progressHandleRef = useRef<HTMLDivElement>(null)
  const mobileCurrentTimeRef = useRef<HTMLDivElement>(null)
  const mobileRemainingTimeRef = useRef<HTMLDivElement>(null)
  const desktopTimeDisplayRef = useRef<HTMLDivElement>(null)

  // Update UI elements based on current time
  const updateUIForTime = useCallback(
    (time: number) => {
      if (!audioRef.current) return

      const currentTime = time
      const timeRemaining = duration - currentTime
      const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0

      // Update progress bar
      if (progressBarElementRef.current) {
        progressBarElementRef.current.style.width = `${progressPercentage}%`
      }

      // Update progress handle
      if (progressHandleRef.current) {
        progressHandleRef.current.style.left = `${progressPercentage}%`
      }

      // Update mobile time displays
      if (mobileCurrentTimeRef.current) {
        mobileCurrentTimeRef.current.textContent = formatTime(currentTime)
      }

      if (mobileRemainingTimeRef.current) {
        mobileRemainingTimeRef.current.textContent = `-${formatTime(timeRemaining)}`
      }

      // Update desktop time display
      if (desktopTimeDisplayRef.current) {
        desktopTimeDisplayRef.current.textContent = `${formatTime(currentTime)} / -${formatTime(timeRemaining)}`
      }
    },
    [duration],
  )

  const togglePlay = useCallback(() => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }, [isPlaying])

  // Handle back to beginning
  const handleBack = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0
      updateUIForTime(0)
    }
  }, [updateUIForTime])

  const handleSkipBack = useCallback(() => {
    if (audioRef.current) {
      const newTime = Math.max(0, audioRef.current.currentTime - SKIP_BACK_SECONDS)
      audioRef.current.currentTime = newTime
      updateUIForTime(newTime)
    }
  }, [updateUIForTime])

  const handleSkipForward = useCallback(() => {
    if (audioRef.current) {
      const newTime = Math.min(audioRef.current.duration, audioRef.current.currentTime + SKIP_FORWARD_SECONDS)
      audioRef.current.currentTime = newTime
      updateUIForTime(newTime)
    }
  }, [updateUIForTime])

  // Handle scrubber bar interactions
  const handleProgressBarClick = useCallback(
    (e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>) => {
      if (e instanceof TouchEvent) {
        e.preventDefault()
        e.stopPropagation()
      }

      if (audioRef.current && progressBarRef.current) {
        const rect = progressBarRef.current.getBoundingClientRect()
        const clientX = e instanceof TouchEvent ? e.touches[0].clientX : (e as React.MouseEvent).clientX
        const clickPosition = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width))
        const newTime = clickPosition * duration
        audioRef.current.currentTime = newTime
        updateUIForTime(newTime)
      }
    },
    [duration, updateUIForTime],
  )

  const handleDragStart = useCallback(
    (e?: React.MouseEvent | React.TouchEvent) => {
      if (e instanceof TouchEvent) {
        e.preventDefault()
        e.stopPropagation()
      }

      setIsDragging(true)
      if (audioRef.current && isPlaying) {
        audioRef.current.pause()
      }
    },
    [isPlaying],
  )

  const handleDragEnd = useCallback(() => {
    setIsDragging(false)
    if (audioRef.current && isPlaying) {
      audioRef.current.play()
    }
  }, [isPlaying])

  const handleDrag = useCallback(
    (e: MouseEvent | TouchEvent) => {
      if (isDragging && audioRef.current && progressBarRef.current) {
        const rect = progressBarRef.current.getBoundingClientRect()
        const clientX = e instanceof TouchEvent ? e.touches[0].clientX : (e as MouseEvent).clientX
        const clickPosition = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width))
        const newTime = clickPosition * duration
        audioRef.current.currentTime = newTime
        updateUIForTime(newTime)
      }
    },
    [isDragging, duration, updateUIForTime],
  )

  const handleShare = useCallback(() => {
    if (navigator.share) {
      navigator.share({
        title: title,
        url: window.location.href,
      })
    }
  }, [title])

  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    const handleTimeUpdate = () => {
      if (!isDragging && audio) {
        updateUIForTime(audio.currentTime)
      }
    }

    const handleLoadedMetadata = () => {
      if (audio) {
        setDuration(audio.duration)
        // Initialize UI with current time
        updateUIForTime(audio.currentTime)
      }
    }

    const handleEnded = () => {
      setIsPlaying(false)
      if (audio) {
        updateUIForTime(audio.duration)
      }
    }

    // Remove event listeners first to prevent duplicates
    audio.removeEventListener('timeupdate', handleTimeUpdate)
    audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
    audio.removeEventListener('ended', handleEnded)

    // Add event listeners
    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('ended', handleEnded)

    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate)
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
      audio.removeEventListener('ended', handleEnded)
    }
  }, [isDragging, updateUIForTime])

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      handleDrag(e)
    }

    const handleTouchMove = (e: TouchEvent) => {
      e.preventDefault()
      handleDrag(e)
    }

    const handleMouseUp = () => {
      handleDragEnd()
    }

    const handleTouchEnd = () => {
      handleDragEnd()
    }

    // Remove event listeners first to prevent duplicates
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.removeEventListener('touchmove', handleTouchMove)
    document.removeEventListener('touchend', handleTouchEnd)

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.addEventListener('touchmove', handleTouchMove)
      document.addEventListener('touchend', handleTouchEnd)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('touchmove', handleTouchMove)
      document.removeEventListener('touchend', handleTouchEnd)
    }
  }, [isDragging, handleDrag, handleDragEnd])

  return (
    <div className={classnames(styles.AudioPlayer, className)}>
      <audio
        ref={audioRef}
        src={sourceUrl}
      />

      {/* Title for mobile */}
      <div className={styles.titleMobile}>{title}</div>

      <div className={styles.progressBarContainer}>
        <div
          className={styles.progressBarWrapper}
          ref={progressBarRef}
          onClick={handleProgressBarClick}
          onTouchStart={handleProgressBarClick}
        >
          <div className={styles.progressBarBackground}></div>
          <div
            className={styles.progressBar}
            ref={progressBarElementRef}
            style={{ width: '0%' }}
          ></div>
          <div
            className={styles.progressHandle}
            ref={progressHandleRef}
            style={{ left: '0%' }}
            onMouseDown={handleDragStart}
            onTouchStart={handleDragStart}
          ></div>
        </div>
      </div>

      {/* Time display for mobile */}
      <div className={styles.timeWrapperMobile}>
        <div ref={mobileCurrentTimeRef}>00:00</div>
        <div ref={mobileRemainingTimeRef}>-00:00</div>
      </div>

      {/* Title and time for tablet/desktop */}
      <div className={styles.titleTimeWrapperDesktop}>
        <div className={styles.titleDesktop}>{title}</div>
        <div
          className={styles.timeDisplayDesktop}
          ref={desktopTimeDisplayRef}
        >
          00:00 / -00:00
        </div>
      </div>

      <div className={styles.mainControls}>
        <button
          className={styles.controlButton}
          onClick={handleBack}
          aria-label="Go to beginning"
          type="button"
        >
          <Icon
            className={styles.controlIcon}
            name="audioBack"
          />
        </button>
        <button
          className={styles.controlButton}
          onClick={handleSkipBack}
          aria-label="Skip back 15 seconds"
          type="button"
        >
          <Icon
            className={styles.controlIcon}
            name="jumpBack"
          />
        </button>
        <button
          className={styles.controlButton}
          onClick={togglePlay}
          aria-label={isPlaying ? 'Pause' : 'Play'}
          type="button"
        >
          <Icon
            className={styles.controlIcon}
            name={isPlaying ? 'pause' : 'play'}
          />
        </button>
        <button
          className={styles.controlButton}
          onClick={handleSkipForward}
          aria-label="Skip forward 15 seconds"
          type="button"
        >
          <Icon
            className={styles.controlIcon}
            name="jumpAhead"
          />
        </button>
      </div>

      <div className={styles.shareControls}>
        <button
          className={styles.controlButton}
          onClick={handleShare}
          aria-label="Share"
          type="button"
        >
          <Icon
            className={styles.shareIcon}
            name="share"
          />
        </button>
      </div>
    </div>
  )
}

AudioPlayer.displayName = 'AudioPlayer'

export default AudioPlayer
