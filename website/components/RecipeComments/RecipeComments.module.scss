.RecipeComments {
  width: 100%;
  margin: 0 auto;
  position: relative;
}

.container {
  display: flex;
  flex-direction: column;
  gap: px(40);

  @include bp(tablet) {
    flex-direction: row;
    align-items: flex-start;
  }
}

.ratingColumn {
  width: 100%;

  @include bp(tablet) {
    width: 25%;
    position: sticky;
    top: px(20);
  }
}

.commentsColumn {
  width: 100%;

  @include bp(tablet) {
    width: 75%;
    padding-left: px(40);
  }
}

.sectionTitle {
  @include font-heading-eyebrow-lg;
  margin-bottom: px(28);
}

.ratingSummary {
  margin-bottom: px(36);
}

.ratingStars {
  display: flex;
  align-items: center;
}

.starsContainer {
  display: flex;
  margin-right: px(12);
}

.ratingCount {
  @include font-text-lg;
  color: var(--navy-dark);
  font-weight: 700;
}

.yourRating {
  margin-top: px(36);
}

.yourRatingStars {
  display: flex;
}

.starButton {
  @include reset-button;
  margin-right: px(4);
  cursor: pointer;
}

.reviewFormOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.reviewForm {
  background-color: var(--parchment);
  width: 100%;
  max-width: px(500);
  padding: px(24);
  position: relative;
  box-shadow: 0 px(4) px(8) rgba(0, 0, 0, 0.1);
}

.formHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: px(24);
  padding-bottom: px(16);
}

.formTitle {
  @include font-heading-md;
  margin: 0;
}

.closeButton {
  @include reset-button;
}

.formGroup {
  margin-bottom: px(28);
}

.formLabel {
  @include font-text-base;
  font-weight: 700;
  color: var(--navy-dark);
  display: block;
  margin-bottom: px(6);
}

.ratingStarsForm {
  display: flex;
}

.formInput {
  @include font-text-base;
  width: 100%;
  height: 48px;
  padding: px(12) px(16);
  background-color: var(--gray-quiet);
  color: var(--navy-dark);
  border-radius: 0;

  &::placeholder {
    color: var(--slate);
    font-style: italic;
  }

  &:focus {
    outline: none;
    border-color: var(--navy-dark);
  }
}

.formTextarea {
  @include font-text-base;
  width: 100%;
  min-height: px(120);
  padding: px(12) px(16);
  background-color: var(--gray-quiet);
  color: var(--navy-dark);
  border-radius: 0;

  &::placeholder {
    color: var(--slate);
    font-style: italic;
  }

  &:focus {
    outline: none;
    border-color: var(--navy-dark);
  }
}

.errorMessage {
  @include font-text-base;
  color: var(--loud-warm);
  margin-bottom: px(16);
}

.successMessage {
  @include font-text-base;
  color: var(--loud-warm);
  margin-bottom: px(16);
}

.formActions {
  display: flex;
  justify-content: flex-end;
}

.submitButton {
  @include reset-button;
  @include font-text-base;
  color: var(--white);
  display: inline-block;
  font-weight: 700;
  padding: px(12) px(28);
  background-color: var(--loud-warm);
  cursor: pointer;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.commentsList {
  @include reset-ul;
  margin: 0;
  padding: 0;
}

.commentItem {
  padding: px(24) 0;

  &:first-child {
    padding-top: 0;
  }
}

.commentHeader {
  margin-bottom: px(12);
  display: flex;
  flex-direction: column;

  @include bp(tablet) {
    flex-direction: row;
    align-items: baseline;
  }
}

.commentAuthor {
  @include font-heading-md;
  margin-right: px(12);
  margin-bottom: px(4);

  @include bp(tablet) {
    margin-bottom: 0;
  }
}

.commentDate {
  @include font-byline-md;
  color: var(--slate);
}

.commentRating {
  display: flex;
  margin-bottom: px(12);
}

.commentTitle {
  @include font-text-base;
  font-weight: 700;
  margin-bottom: px(8);
  margin-top: 0;
}

.commentText {
  @include font-text-base;
  margin: 0;
}

.starIcon {
  color: var(--navy-dark);
  width: px(20);
  height: px(20);
}

.starIconLarge {
  color: var(--navy-dark);
  width: px(32);
  height: px(32);
}
