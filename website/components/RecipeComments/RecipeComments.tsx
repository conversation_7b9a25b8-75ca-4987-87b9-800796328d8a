'use client'

import { useState } from 'react'
import { format } from 'date-fns'
import classnames from 'classnames'
import styles from './RecipeComments.module.scss'

export interface Comment {
  id: string
  author: string
  date: string
  rating: number
  title: string
  text: string
}

interface RecipeCommentsProps {
  initialComments?: Comment[]
  initialRating?: number
  totalRatings?: number
  className?: string
}

export default function RecipeComments({
  initialComments,
  initialRating,
  totalRatings,
  className,
}: RecipeCommentsProps) {
  const [comments, setComments] = useState<Comment[]>(initialComments || [])
  const [rating, setRating] = useState<number>(initialRating || 0)
  const [totalRatingCount, setTotalRatingCount] = useState<number>(totalRatings || 0)

  const [name, setName] = useState('')
  const [title, setTitle] = useState('')
  const [commentText, setCommentText] = useState('')
  const [userRating, setUserRating] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [showCommentForm, setShowCommentForm] = useState(false)

  // Handle star rating selection
  const handleRatingClick = (selectedRating: number) => {
    setUserRating(selectedRating)
    setShowCommentForm(true)
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!name.trim()) {
      setError('Please enter your name')
      return
    }

    if (!title.trim()) {
      setError('Please enter a title for your review')
      return
    }

    if (!commentText.trim()) {
      setError('Please enter your comment')
      return
    }

    if (userRating === 0) {
      setError('Please select a rating')
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // Create new comment
      const newComment: Comment = {
        id: `comment-${Date.now()}`,
        author: name,
        date: new Date().toISOString(),
        rating: userRating,
        title: title,
        text: commentText,
      }

      setComments([newComment, ...comments])

      const newTotalRatings = totalRatingCount + 1
      const newRating = (rating * totalRatingCount + userRating) / newTotalRatings
      setRating(newRating)
      setTotalRatingCount(newTotalRatings)

      // Reset form
      setName('')
      setTitle('')
      setCommentText('')
      setUserRating(0)
      setSuccess(true)
      setShowCommentForm(false)

      // TODO: Remove after integration with API
      setTimeout(() => {
        // Simulate API request
        setSuccess(false)
      }, 3000)
    } catch (err) {
      setError('Failed to submit comment. Please try again.')
      console.error(err)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className={classnames(styles.RecipeComments, className)}>
      <div className={styles.container}>
        {/* Ratings */}
        <div className={styles.ratingColumn}>
          <h2 className={styles.sectionTitle}>RATING</h2>

          <div className={styles.ratingSummary}>
            <div className={styles.ratingStars}>
              <div className={styles.starsContainer}>
                {[1, 2, 3, 4, 5].map(star => (
                  <StarIcon
                    key={star}
                    filled={Math.round(rating) >= star}
                    className={styles.starIcon}
                  />
                ))}
              </div>
            </div>
            <div className={styles.ratingCount}>{totalRatingCount} ratings</div>
          </div>

          <div className={styles.yourRating}>
            <h3 className={styles.sectionTitle}>YOUR RATING</h3>
            <div className={styles.yourRatingStars}>
              {[1, 2, 3, 4, 5].map(star => (
                <button
                  key={star}
                  type="button"
                  onClick={() => handleRatingClick(star)}
                  className={styles.starButton}
                  aria-label={`Rate ${star} stars`}
                >
                  <StarIcon
                    filled={userRating >= star}
                    className={styles.starIconLarge}
                  />
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Comments */}
        <div className={styles.commentsColumn}>
          <h2 className={styles.sectionTitle}>COMMENTS</h2>
          <ul className={styles.commentsList}>
            {comments.map(comment => (
              <li
                key={comment.id}
                className={styles.commentItem}
              >
                <div className={styles.commentRating}>
                  {[1, 2, 3, 4, 5].map(star => (
                    <StarIcon
                      key={star}
                      filled={comment.rating >= star}
                      className={styles.starIcon}
                    />
                  ))}
                </div>
                <div className={styles.commentHeader}>
                  <span className={styles.commentAuthor}>{comment.author}</span>
                  <span className={styles.commentDate}>{format(new Date(comment.date), 'MMMM d, yyyy')}</span>
                </div>
                <h4 className={styles.commentTitle}>{comment.title}</h4>
                <p className={styles.commentText}>{comment.text}</p>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Review Form */}
      {showCommentForm && (
        <div className={styles.reviewFormOverlay}>
          <div className={styles.reviewForm}>
            <div className={styles.formHeader}>
              <h3 className={styles.formTitle}>Made this recipe? Write a review.</h3>
              <button
                className={styles.closeButton}
                onClick={() => setShowCommentForm(false)}
                aria-label="Close review form"
              >
                <CloseIcon />
              </button>
            </div>
            <form onSubmit={handleSubmit}>
              <div className={styles.formGroup}>
                <label
                  htmlFor="rating"
                  className={styles.formLabel}
                >
                  Rating
                </label>
                <div className={styles.ratingStarsForm}>
                  {[1, 2, 3, 4, 5].map(star => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setUserRating(star)}
                      className={styles.starButton}
                      aria-label={`Rate ${star} stars`}
                    >
                      <StarIcon
                        filled={userRating >= star}
                        className={styles.starIconLarge}
                      />
                    </button>
                  ))}
                </div>
              </div>

              <div className={styles.formGroup}>
                <label
                  htmlFor="name"
                  className={styles.formLabel}
                >
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  value={name}
                  onChange={e => setName(e.target.value)}
                  className={styles.formInput}
                  placeholder="Your name"
                />
              </div>

              <div className={styles.formGroup}>
                <label
                  htmlFor="title"
                  className={styles.formLabel}
                >
                  Title
                </label>
                <input
                  type="text"
                  id="title"
                  value={title}
                  onChange={e => setTitle(e.target.value)}
                  className={styles.formInput}
                  placeholder="Review title"
                />
              </div>

              <div className={styles.formGroup}>
                <label
                  htmlFor="comment"
                  className={styles.formLabel}
                >
                  Your comment
                </label>
                <textarea
                  id="comment"
                  value={commentText}
                  onChange={e => setCommentText(e.target.value)}
                  rows={4}
                  className={styles.formTextarea}
                  placeholder="Share your comment"
                />
              </div>

              {error && <div className={styles.errorMessage}>{error}</div>}
              {success && <div className={styles.successMessage}>Your comment has been submitted!</div>}

              <div className={styles.formActions}>
                <button
                  type="submit"
                  className={styles.submitButton}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

function StarIcon({ filled, className }: { filled: boolean; className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill={filled ? 'currentColor' : 'none'}
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
    </svg>
  )
}

function CloseIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <line
        x1="3.95065"
        y1="21.1464"
        x2="20.9212"
        y2="4.17588"
        stroke="#1B2D41"
      />
      <line
        x1="4.65775"
        y1="4.14645"
        x2="21.6283"
        y2="21.117"
        stroke="#1B2D41"
      />
    </svg>
  )
}

RecipeComments.displayName = 'RecipeComments'
