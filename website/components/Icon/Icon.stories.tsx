import type { Meta, StoryObj } from '@storybook/react'
import Icon from './Icon'
import { ICONS } from './Icon'
import { useMemo } from 'react'
import styles from './Icon.module.scss'

const meta = {
  title: 'Components/Icon',
  component: Icon,
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof Icon>

export default meta

type Story = StoryObj<typeof meta>

const AllIconsComponent = () => {
  const iconNames = useMemo(() => {
    return Object.keys(ICONS)
  }, [])

  return (
    <div className={styles.storybook__icons}>
      {iconNames.map(name => {
        return (
          <div
            key={name}
            className={styles.storybook__iconContainer}
          >
            <Icon
              // eslint-disable-next-line
              name={name as any}
              className={styles.storybook__icon}
            />
            <span className={styles.storybook__iconText}>{name}</span>
          </div>
        )
      })}
    </div>
  )
}

export const All: Story = {
  args: { name: 'arrowLeft' },
  render: AllIconsComponent,
}
