'use client'

import classnames from 'classnames'
import styles from './Icon.module.scss'
import AmazonMusicSvg from '@/components/_svgs/AmazonMusic'
import ArrowLeftSvg from '@/components/_svgs/ArrowLeft'
import ArrowRightSvg from '@/components/_svgs/ArrowRight'
import BookmarkSvg from '@/components/_svgs/Bookmark'
import BookmarkFilledSvg from '@/components/_svgs/BookmarkFilled'
import BulletsSvg from '@/components/_svgs/Bullets'
import CalendarSvg from '@/components/_svgs/Calendar'
import CaretDownSvg from '@/components/_svgs/CaretDown'
import CaretLeftSvg from '@/components/_svgs/CaretLeft'
import CaretRightSvg from '@/components/_svgs/CaretRight'
import CaretUpSvg from '@/components/_svgs/CaretUp'
import CheckmarkSvg from '@/components/_svgs/Checkmark'
import CloseSvg from '@/components/_svgs/Close'
import CopySvg from '@/components/_svgs/Copy'
import DocumentSvg from '@/components/_svgs/Document'
import DownloadSvg from '@/components/_svgs/Download'
import FacebookSvg from '@/components/_svgs/Facebook'
import HamburgerSvg from '@/components/_svgs/Hamburger'
import InstagramSvg from '@/components/_svgs/Instagram'
import MagnifyingGlassSvg from '@/components/_svgs/MagnifyingGlass'
import PandoraSvg from '@/components/_svgs/Pandora'
import PinterestSvg from '@/components/_svgs/Pinterest'
import PlaySvg from '@/components/_svgs/Play'
import PlayCircleSvg from '@/components/_svgs/PlayCircle'
import PodcastsSvg from '@/components/_svgs/Podcasts'
import PrinterSvg from '@/components/_svgs/Printer'
import ProfileSvg from '@/components/_svgs/Profile'
import SpotifySvg from '@/components/_svgs/Spotify'
import StarSvg from '@/components/_svgs/Star'
import StarHollowSvg from '@/components/_svgs/StarHollow'
import TuneInSvg from '@/components/_svgs/TuneIn'
import TwitterSvg from '@/components/_svgs/Twitter'
import WordmarkSvg from '@/components/_svgs/Wordmark'
import YoutubeSvg from '@/components/_svgs/Youtube'
import JumpAheadSvg from '@/components/_svgs/JumpAhead'
import JumpBackSvg from '@/components/_svgs/JumpBack'
import AudioNextSvg from '@/components/_svgs/AudioNext'
import AudioBackSvg from '@/components/_svgs/AudioBack'
import ShareSvg from '@/components/_svgs/Share'
import ClosedCaptioningSvg from '@/components/_svgs/ClosedCaptioning'
import BasketSvg from '@/components/_svgs/Basket'
import PauseSvg from '@/components/_svgs/Pause'
import FilterSvg from '@/components/_svgs/Filter'
export const ICONS = {
  amazonMusic: AmazonMusicSvg,
  arrowLeft: ArrowLeftSvg,
  arrowRight: ArrowRightSvg,
  bookmark: BookmarkSvg,
  bookmarkFilled: BookmarkFilledSvg,
  bullets: BulletsSvg,
  calendar: CalendarSvg,
  caretDown: CaretDownSvg,
  caretLeft: CaretLeftSvg,
  caretRight: CaretRightSvg,
  caretUp: CaretUpSvg,
  checkmark: CheckmarkSvg,
  close: CloseSvg,
  copy: CopySvg,
  document: DocumentSvg,
  download: DownloadSvg,
  facebook: FacebookSvg,
  hamburger: HamburgerSvg,
  instagram: InstagramSvg,
  magnifyingGlass: MagnifyingGlassSvg,
  pandora: PandoraSvg,
  pinterest: PinterestSvg,
  play: PlaySvg,
  jumpAhead: JumpAheadSvg,
  jumpBack: JumpBackSvg,
  audioNext: AudioNextSvg,
  audioBack: AudioBackSvg,
  closedCaptioning: ClosedCaptioningSvg,
  playCircle: PlayCircleSvg,
  podcasts: PodcastsSvg,
  printer: PrinterSvg,
  profile: ProfileSvg,
  spotify: SpotifySvg,
  star: StarSvg,
  starHollow: StarHollowSvg,
  tuneIn: TuneInSvg,
  twitter: TwitterSvg,
  wordmark: WordmarkSvg,
  youtube: YoutubeSvg,
  share: ShareSvg,
  basket: BasketSvg,
  pause: PauseSvg,
  filter: FilterSvg,
} as const

const Icon = ({ className, name }: IconProps) => {
  if (!ICONS[name]) {
    console.warn(`No icon with name ${name} exists.`)
    return null
  }

  const Icon = ICONS[name]

  return <Icon className={classnames(styles.Icon, className)} />
}

Icon.displayName = 'Icon'

export default Icon
