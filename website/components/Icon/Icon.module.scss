/* stylelint-disable-next-line block-no-empty */
.Icon {
}

.storybook__icons {
  display: grid;
  grid-template-columns: repeat(6, minmax(1px, 1fr));
  gap: px(1);
  max-width: px(900);
  width: 100%;
}

.storybook__iconContainer {
  @include flex-center;
  flex-direction: column;
  outline: 1px solid var(--navy-dark);
  padding: 10px;
}

.storybook__icon {
  width: 100%;
  max-width: px(30);
}

.storybook__iconText {
  @include font-text-sm;
  margin-top: px(5);
}
