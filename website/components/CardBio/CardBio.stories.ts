import type { <PERSON>a, StoryObj } from '@storybook/react'
import CardBio from './CardBio'
import { SANITY_IMAGE_STUB_SOURCE } from '@/components/SanityImage/SanityImage.stub'

const meta = {
  title: 'Cards/CardBio',
  component: Card<PERSON><PERSON>,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'radio',
      options: ['sm', 'md'],
      description: 'Size of the card',
    },
    image: {
      description: 'Sanity image object',
    },
    title: {
      control: 'text',
      description: 'Name of the person',
    },
    description: {
      control: 'text',
      description: 'Bio description',
    },
    socialLinks: {
      description: 'Social media links',
    },
  },
} satisfies Meta<typeof CardBio>

export default meta

type Story = StoryObj<typeof meta>

const mockImage = SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE

export const Default: Story = {
  args: {
    image: mockImage,
    title: 'The Invisible Apple Cake',
    description: 'Gâteau invisible offers super-thin slices of apple barely held together with a warm-spiced custard',
    socialLinks: {
      facebook: 'https://facebook.com/milkstreet',
      twitter: 'https://twitter.com/177milkstreet',
      instagram: 'https://instagram.com/177milkstreet',
    },
  },
}

export const Small: Story = {
  args: {
    ...Default.args,
    size: 'sm',
  },
}

export const WithoutDescription: Story = {
  args: {
    ...Default.args,
    description: undefined,
  },
}

export const WithAllSocialLinks: Story = {
  args: {
    ...Default.args,
    socialLinks: {
      facebook: 'https://facebook.com/milkstreet',
      twitter: 'https://twitter.com/177milkstreet',
      youtube: 'https://youtube.com/milkstreet',
      pinterest: 'https://pinterest.com/177milkstreet',
      instagram: 'https://instagram.com/177milkstreet',
    },
  },
}

export const WithoutSocialLinks: Story = {
  args: {
    ...Default.args,
    socialLinks: undefined,
  },
}
