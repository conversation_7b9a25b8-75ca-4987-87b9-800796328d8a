.CardBio {
  display: flex;
  gap: px(16);
  width: 100%;

  @include bp(tablet) {
    gap: px(24);
  }
}

.imageWrapper {
  position: relative;
  overflow: hidden;
  flex-shrink: 0;

  [data-card-bio-size='md'] & {
    width: px(64);
    height: px(64);

    @include bp(tablet) {
      width: px(240);
      height: px(240);
    }
  }

  [data-card-bio-size='sm'] & {
    width: px(64);
    height: px(64);
    border-radius: 50%;

    @include bp(tablet) {
      width: px(160);
      height: px(160);
    }
  }
}

.content {
  color: var(--color-navy-dark);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title {
  margin-bottom: px(12);

  @include bp(tablet) {
    margin-bottom: px(16);
  }

  [data-card-bio-size='md'] & {
    @include font-heading-lg;
  }

  [data-card-bio-size='sm'] & {
    @include font-heading-md;
  }
}

.description {
  @include font-text-base;
  margin-bottom: px(12);

  @include bp(tablet) {
    margin-bottom: px(16);
  }
}

.socialLinks {
  display: flex;
  gap: px(16);
}

.socialLink {
  @include reset-button;
  @include flex-center;
  color: var(--color-navy-dark);
  width: px(24);

  @include hover {
    color: var(--loud-warm);
  }
}
