'use client'

import classnames from 'classnames'
import styles from './CardBio.module.scss'
import SanityImage from '@/components/SanityImage/SanityImage'
import Link from '@/components/Link/Link'
import Icon from '@/components/Icon/Icon'

const CardBio = ({ className, image, title, description, socialLinks, size = 'md' }: CardBioProps) => {
  if (!image || !title) {
    return null
  }

  return (
    <div
      className={classnames(styles.CardBio, className)}
      data-card-bio-size={size}
    >
      <div className={styles.imageWrapper}>
        <SanityImage
          source={image}
          isCover={true}
          columns={{
            sm: 3,
            md: 2,
          }}
        />
      </div>
      <div className={styles.content}>
        <h3 className={styles.title}>{title}</h3>
        {description && <p className={styles.description}>{description}</p>}
        <SocialLinks socialLinks={socialLinks} />
      </div>
    </div>
  )
}

const SocialLinks = ({ socialLinks, className }: CardBioSocialLinkProps) => {
  if (!socialLinks || !Object.values(socialLinks).some(link => !!link)) {
    return null
  }

  return (
    <div className={classnames(styles.socialLinks, className)}>
      {Object.entries(socialLinks).map(
        ([socialPlatform, link]) =>
          link && (
            <Link
              key={socialPlatform}
              link={{
                linkType: 'external',
                link: link,
              }}
              className={styles.socialLink}
              ariaLabel={socialPlatform}
            >
              <Icon name={socialPlatform as IconNames} />
            </Link>
          ),
      )}
    </div>
  )
}

CardBio.displayName = 'CardBio'

export default CardBio
