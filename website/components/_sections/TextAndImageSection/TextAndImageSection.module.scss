.TextAndImageSectionContainer {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
}

.TextAndImageSection {
  position: relative;
  border: 1px solid rgba($navy-dark, 0.2);
  background-color: var(--white);
  overflow: hidden;
  width: 100%;
  display: flex;
  flex-direction: column;

  @include bp(tablet) {
    display: grid;
    grid-template-columns: 0.5fr 0.5fr;
    grid-template-areas: 'content image';
    height: px(480);
  }
}

.imageWrapper {
  position: relative;
  width: 100%;
  aspect-ratio: var(--aspect-ratio-square);

  @include bp(tablet) {
    height: 100%;
    grid-area: image;
    aspect-ratio: auto;
  }
}

.image {
  @include position-100(absolute);
}

.content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: px(28) px(16);

  @include bp(tablet) {
    grid-area: content;
    padding: px(40) px(80);
  }
}

.eyebrow {
  @include font-heading-eyebrow-md;
  color: var(--navy-dark);
  margin-bottom: px(12);

  @include bp(tablet) {
    margin-bottom: px(16);
  }
}

.title {
  @include font-heading-md;
  color: var(--navy-dark);
  margin-bottom: px(12);

  @include bp(tablet) {
    margin-bottom: px(24);
  }
}

.description {
  @include font-text-base;
  color: var(--navy-dark);
  overflow: hidden;
  margin-bottom: px(12);
  max-height: px(100);

  @include bp(tablet) {
    margin-bottom: px(24);
    max-height: px(120);
  }
}

.price {
  margin-bottom: px(12);

  @include bp(tablet) {
    margin-bottom: px(24);
  }

  [data-price-text] {
    @include font-text-lg;
    color: var(--navy-dark);
  }

  [data-sale-price] {
    color: var(--loud-warm);
  }
}

.button {
  align-self: flex-start;
}

.arrow {
  width: px(24);
  height: px(24);
}
