'use client'

import classnames from 'classnames'
import styles from './TextAndImageSection.module.scss'
import Link from '@/components/Link/Link'
import SanityImage from '@/components/SanityImage/SanityImage'
import { truncateText } from '@/lib/text'
import Button from '@/components/Button/Button'
import PriceDisplay from '@/components/PriceDisplay/PriceDisplay'

const TextAndImageSection = ({
  className,
  useContentReference,
  referenceForContent,
  eyebrow,
  title,
  description,
  price,
  discountPrice,
  link,
  linkLabel,
  image,
}: SanityTextAndImageSection) => {
  const isContentReference = useContentReference === true && referenceForContent
  link = isContentReference
    ? {
        linkType: 'internal',
        label: linkLabel || 'Learn More',
        link: {
          _type: referenceForContent?._type,
          slug: referenceForContent?.slug,
        },
      }
    : link
  image = image || referenceForContent?.image
  title = title || referenceForContent?.title
  description = description || referenceForContent?.description

  if (isContentReference) {
    price = referenceForContent?.price?.default || null
    discountPrice = referenceForContent?.price?.memberPrice || null
  }

  if (!image?.asset || !title) return null

  const content = (
    <div className={classnames(styles.TextAndImageSection, className)}>
      <div className={styles.imageWrapper}>
        <SanityImage
          source={image}
          className={styles.image}
          columns={{
            md: 6,
            sm: 12,
          }}
          aspectRatio={{
            md: 4 / 3,
            sm: 1,
          }}
          isCover={true}
        />
      </div>
      <div className={styles.content}>
        {eyebrow && <div className={styles.eyebrow}>{eyebrow}</div>}
        <h2 className={styles.title}>{title}</h2>
        {description && <div className={styles.description}>{truncateText(description, 200)}</div>}
        {price && (
          <PriceDisplay
            className={styles.price}
            price={price}
            discountPrice={discountPrice}
          />
        )}
        {link && link?.linkType !== 'disabled' && (
          <Button
            className={styles.button}
            label={link?.label}
            element="span"
            icon="arrowRight"
            iconPosition="right"
            style="bare"
            bareColor="loud"
            disableHoverAnimation
          />
        )}
      </div>
    </div>
  )

  if (link && link?.linkType !== 'disabled') {
    return (
      <TextAndImageSectionContainer>
        {' '}
        <Link link={link}>{content}</Link>
      </TextAndImageSectionContainer>
    )
  } else {
    return <TextAndImageSectionContainer>{content}</TextAndImageSectionContainer>
  }
}

const TextAndImageSectionContainer = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className={styles.TextAndImageSectionContainer}>
      <div className={styles.inner}>{children}</div>
    </div>
  )
}

TextAndImageSection.displayName = 'TextAndImageSection'

export default TextAndImageSection
