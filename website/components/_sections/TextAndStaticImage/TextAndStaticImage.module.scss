.TextAndStaticImageContainer {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
}

.grid {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: px(24);

  @include bp(tablet) {
    display: grid;
    grid-gap: px(40);
  }

  [data-text-and-static-image-columns='50-50'] & {
    grid-template-columns: 1fr 1fr;
  }

  [data-text-and-static-image-columns='5-7'] & {
    grid-template-columns: 5fr 7fr;
  }
}

.imageSide {
  order: 2;

  [data-text-and-static-image-side='left'] & {
    @include bp(tablet) {
      order: 1;
    }
  }

  [data-text-and-static-image-side='right'] & {
    @include bp(tablet) {
      order: 2;
    }
  }
}

.imageWrapper {
  position: relative;
  width: 100%;
  aspect-ratio: var(--aspect-ratio);
}

.image {
  @include position-100(absolute);
  object-fit: cover;
}

.imageCaption {
  @include font-byline-md;
  color: var(--navy-dark);
  margin-top: px(8);
}

.content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  order: 1;

  [data-text-and-static-image-side='left'] & {
    @include bp(tablet) {
      order: 2;
    }
  }

  [data-text-and-static-image-side='right'] & {
    @include bp(tablet) {
      order: 1;
    }
  }
}

.title {
  @include font-heading-lg;
  color: var(--navy-dark);
  margin-bottom: px(24);
}

.description {
  @include font-text-base;
  color: var(--navy-dark);
}
