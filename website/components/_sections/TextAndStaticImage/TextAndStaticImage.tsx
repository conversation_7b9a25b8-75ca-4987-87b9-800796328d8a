'use client'

import classnames from 'classnames'
import styles from './TextAndStaticImage.module.scss'
import SanityImage from '@/components/SanityImage/SanityImage'
import RichTextV2 from '@/components/RichTextV2/RichTextV2'
import { CSSProperties } from 'react'

const TextAndStaticImage = ({
  className,
  title,
  description,
  image,
  imageCaption,
  imageSide = 'right',
  columns = '50-50',
}: SanityTextAndStaticImageSection) => {
  const aspect = image?.asset?.aspectRatio || 4 / 3

  if (!image) return null

  return (
    <div
      className={classnames(styles.TextAndStaticImageContainer, className)}
      data-text-and-static-image-side={imageSide}
      data-text-and-static-image-columns={columns}
    >
      <div className={styles.inner}>
        <div className={styles.grid}>
          <div className={styles.imageSide}>
            <div
              className={styles.imageWrapper}
              style={
                {
                  '--aspect-ratio': aspect,
                } as CSSProperties
              }
            >
              <SanityImage
                source={image}
                columns={{
                  md: columns === '50-50' ? 6 : columns === '5-7' ? 5 : 6,
                  sm: 12,
                }}
                aspectRatio={{
                  md: aspect,
                  sm: aspect,
                }}
                className={styles.image}
              />
            </div>
            {imageCaption && <div className={styles.imageCaption}>{imageCaption}</div>}
          </div>
          <div className={styles.content}>
            {title && <h2 className={styles.title}>{title}</h2>}
            {description && (
              <div className={styles.description}>
                <RichTextV2 content={description} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

TextAndStaticImage.displayName = 'TextAndStaticImage'

export default TextAndStaticImage
