'use client'

import classnames from 'classnames'
import styles from './RichTextSection.module.scss'
import RichTextV2 from '@/components/RichTextV2/RichTextV2'

const RichTextSection = ({ className, content }: SanityRichTextSection) => {
  if (!content?.length) return null

  return (
    <div className={classnames(styles.RichTextSection, className)}>
      <div className={styles.inner}>
        <div className={styles.richText}>
          <RichTextV2 content={content} />
        </div>
      </div>
    </div>
  )
}

RichTextSection.displayName = 'RichTextSection'

export default RichTextSection
