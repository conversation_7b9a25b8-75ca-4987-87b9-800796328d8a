'use client'

import React, { useMemo } from 'react'
import styles from './BigImageSection.module.scss'
import SanityImage from '@/components/SanityImage/SanityImage'
import classnames from 'classnames'
import Video from '@/components/Video/Video'

const defaultAspect = 16 / 9

const BigImageSection = ({ className, mediaType, media, aspectRatio }: SanityBigImageSection) => {
  const aspectRatioValue = useMemo(() => {
    if (aspectRatio === 'imageAspect' && media[0]?.asset?.aspectRatio) return media[0]?.asset?.aspectRatio
    if (aspectRatio === '16:9') return 16 / 9
    return defaultAspect
  }, [aspectRatio, media])

  const mediaItem = media && media.length > 0 ? media[0] : null
  if (!mediaItem) return null

  return (
    <div className={classnames(styles.BigImageSection, className)}>
      <div className={styles.inner}>
        <div className={styles.mediaContainer}>
          {mediaType === 'image' && mediaItem?.asset && (
            <div
              className={styles.imageContainer}
              style={{
                aspectRatio: aspectRatioValue,
              }}
            >
              <SanityImage
                source={mediaItem}
                className={styles.image}
                columns={12}
                aspectRatio={aspectRatioValue}
              />
            </div>
          )}
          {mediaType === 'video' && (
            <Video
              type={mediaItem.type}
              youtubeId={mediaItem.youtubeId}
              vimeoId={mediaItem.vimeoId}
              url={mediaItem.url}
              previewImage={mediaItem.previewImage}
            />
          )}
        </div>
      </div>
    </div>
  )
}

BigImageSection.displayName = 'BigImageSection'

export default BigImageSection
