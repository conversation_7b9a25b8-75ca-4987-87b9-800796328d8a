/* stylelint-disable-next-line block-no-empty */
.ExampleSection {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);

  @include bp(tablet) {
    display: grid;
    grid-template-columns: #{px(300)} 1fr;
    gap: var(--gutter);
  }

  @include bp(laptop) {
    grid-template-columns: #{px(400)} 1fr;
  }
}

.title {
  @include font-heading-lg;
  margin-bottom: px(24);
}

.description {
  @include rich-text-base-styling;

  [data-h2] {
    @include font-heading-sm;
  }
}

.items {
  @include reset-ul;
  display: grid;
  width: 100%;
  margin-top: px(40);
  grid-template-columns: repeat(1, 1fr);
  gap: var(--gutter);

  @include bp(tablet) {
    grid-template-columns: repeat(3, 1fr);
  }
}

.item__link {
  display: block;
}

.item__img {
  display: block;
  object-fit: cover;
  aspect-ratio: 1;
  margin-bottom: px(10);

  @include bp(tablet) {
    aspect-ratio: 1.5;
  }
}

.item__title {
  @include font-heading-sm;
  margin-bottom: px(10);
}

.item__description {
  @include font-text-base;
}

.video {
  margin-top: px(40);
}

.referencedContent {
  margin-top: px(40);
}

.referencedContent__title {
  @include font-heading-lg;
  margin-bottom: px(24);
}

.referencedContent__grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--gutter);

  @include bp(tablet) {
    grid-template-columns: repeat(3, 1fr);
  }
}
