'use client'

import classnames from 'classnames'
import styles from './ExampleSection.module.scss'
import RichTextV2 from '@/components/RichTextV2/RichTextV2'
import Link from '@/components/Link/Link'
import SanityImage from '@/components/SanityImage/SanityImage'
import Video from '@/components/Video/Video'
import Card from '@/components/Card/Card'
import { formatCardFieldsForComponent } from '@/lib/utils/card'
import Sidebar from '../Sidebar/Sidebar'

const ExampleSection = ({
  className,
  title,
  description,
  items,
  video,
  referencedItems,
  sidebar,
}: SanityExampleSection) => {
  if (!title || !description?.length || !items?.length) return null

  return (
    <div className={classnames(styles.ExampleSection, className)}>
      <div className={styles.inner}>
        <div className={styles.sidebarContainer}>
          <Sidebar items={sidebar.items} />
        </div>
        <div className={styles.content}>
          <h1 className={styles.title}>{title}</h1>
          <div className={styles.description}>
            <RichTextV2 content={description} />
          </div>
          <ul className={styles.items}>
            {items.map((item, i) => (
              <li
                className={styles.item}
                key={i}
              >
                <Link
                  link={item.link}
                  className={styles.item__link}
                >
                  <SanityImage
                    className={styles.item__img}
                    source={item.image}
                    aspectRatio={{
                      md: 1.5,
                      sm: 1,
                    }}
                    columns={{
                      md: 4,
                      sm: 12,
                    }}
                  />
                  <p className={styles.item__title}>{item.title}</p>
                  <p className={styles.item__description}>{item.title}</p>
                </Link>
              </li>
            ))}
          </ul>
          <Video
            {...video}
            className={styles.video}
          />
          <div className={styles.referencedContent}>
            <h2 className={styles.referencedContent__title}>Referenced Content</h2>
            <div className={styles.referencedContent__grid}>
              {!!referencedItems?.length && (
                <>
                  {referencedItems.map((item, i) => (
                    <Card
                      {...formatCardFieldsForComponent({
                        props: item,
                      })}
                      capLines={true}
                      key={i}
                      className={styles.referencedContent__gridItem}
                    />
                  ))}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

ExampleSection.displayName = 'ExampleSection'

export default ExampleSection
