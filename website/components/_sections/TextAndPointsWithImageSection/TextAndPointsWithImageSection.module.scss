.TextAndPointsWithImageSection {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
  flex-direction: column;
  gap: px(40);
  display: flex;
  align-items: center;

  @include bp(tablet) {
    gap: px(80);
    flex-direction: row;
  }
}

.descriptionAndPointsContainer {
  order: 2;

  @include bp(tablet) {
    padding-bottom: px(24);
  }

  [data-text-and-points-with-image-section-image-side='left'] & {
    @include bp(tablet) {
      order: 2;
    }
  }

  [data-text-and-points-with-image-section-image-side='right'] & {
    @include bp(tablet) {
      order: 1;
    }
  }
}

.title {
  @include font-heading-lg;
  color: var(--navy-dark);
  margin-bottom: px(16);
}

.description {
  @include font-text-lg-snug;
  color: var(--slate);
  margin-bottom: px(24);
}

.pointsSection {
  border-top: 1px solid rgba($navy-dark, 0.2);
}

.pointsList {
  @include reset-ul;
}

.pointItem {
  margin-top: px(24);
}

.pointTitle {
  @include font-heading-eyebrow-lg;
  color: var(--navy-dark);
  margin-bottom: px(8);
}

.pointDescription {
  @include font-heading-sm;
  font-style: italic;
  color: var(--slate);
}

.imageContainer {
  position: relative;
  width: 100%;

  [data-text-and-points-with-image-section-image-side='left'] & {
    order: 1;
  }

  [data-text-and-points-with-image-section-image-side='right'] & {
    order: 2;
  }
}

.sectionImage {
  @include position-100(absolute);
  object-fit: cover;
}
