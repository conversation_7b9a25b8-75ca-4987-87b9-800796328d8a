'use client'

import classnames from 'classnames'
import styles from './TextAndPointsWithImageSection.module.scss'
import RichTextV2 from '@/components/RichTextV2/RichTextV2'
import SanityImage from '@/components/SanityImage/SanityImage'

const TextAndPointsWithImageSection = ({
  className,
  title,
  description,
  points,
  image,
  imageSide,
}: SanityTextAndPointsWithImageSection) => {
  if (!title || !image) return null

  return (
    <div
      className={classnames(styles.TextAndPointsWithImageSection, className)}
      data-text-and-points-with-image-section-image-side={imageSide}
    >
      <div className={styles.inner}>
        <div className={styles.descriptionAndPointsContainer}>
          <h2 className={styles.title}>{title}</h2>

          {description && description.length > 0 && (
            <div className={styles.description}>
              <RichTextV2 content={description} />
            </div>
          )}

          {points && points.length > 0 && (
            <div className={styles.pointsSection}>
              <ul className={styles.pointsList}>
                {points.map((point, index) => (
                  <li
                    key={index}
                    className={styles.pointItem}
                  >
                    <h3 className={styles.pointTitle}>{point.title}</h3>
                    <p className={styles.pointDescription}>{point.text}</p>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <div
          className={styles.imageContainer}
          style={{
            aspectRatio: image.asset?.aspectRatio,
          }}
        >
          <SanityImage
            source={image}
            className={styles.sectionImage}
            columns={{
              md: 6,
              sm: 12,
            }}
          />
        </div>
      </div>
    </div>
  )
}

TextAndPointsWithImageSection.displayName = 'TextAndPointsWithImageSection'

export default TextAndPointsWithImageSection
