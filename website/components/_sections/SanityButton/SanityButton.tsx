'use client'

import classnames from 'classnames'
import styles from './SanityButton.module.scss'
import SanityButton from '@/types/sanity/SanityButton'
import Button from '@/components/Button/Button'

type SanityButtonProps = SanityButton & {
  className?: string
  linkClassName?: string
}

const SanityButtonComponent = ({
  className,
  link,
  style,
  icon,
  iconPosition,
  labelSize,
  linkClassName,
}: SanityButtonProps) => {
  return (
    <Button
      link={link}
      style={style}
      icon={icon || undefined}
      iconPosition={iconPosition}
      labelSize={labelSize}
      className={classnames(styles.SanityButton, className)}
      linkClassName={linkClassName}
    />
  )
}

SanityButtonComponent.displayName = 'SanityButtonComponent'

export default SanityButtonComponent
