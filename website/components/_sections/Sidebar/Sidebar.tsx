'use client'

import classnames from 'classnames'
import styles from './Sidebar.module.scss'
import { useMemo } from 'react'
import SanityButtonComponent from '../SanityButton/SanityButton'
import ItemLabelList from '@/components/ItemLabelList/ItemLabelList'
import { formatCardItemObjectToCardProps, formatCardItemsObjectToItems } from '@/lib/utils/card'
import Card from '@/components/Card/Card'
import LineHeading from '@/components/LineHeading/LineHeading'
import ContainerOrLink from '@/components/ContainerOrLink/ContainerOrLink'
import SanityImage from '@/components/SanityImage/SanityImage'

type SidebarProps = SanitySidebar & {
  className?: string
}

const Sidebar = ({ className, items }: SidebarProps) => {
  const renderedItems = useMemo(() => {
    if (!items?.length) return []
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const itemsToRender: any = []
    items.forEach((item, index) => {
      switch (item._type) {
        case 'button':
          itemsToRender.push({
            component: (
              <SanityButtonComponent
                className={styles.button}
                key={index}
                {...item}
              />
            ),
          })
          break
        case 'cardItem':
          itemsToRender.push({
            component: (
              <Card
                className={styles.card}
                key={index}
                {...formatCardItemObjectToCardProps(item)}
              />
            ),
          })
          break
        case 'cardItems':
          formatCardItemsObjectToItems(item).forEach((cardItem, index) => {
            itemsToRender.push({
              component: (
                <Card
                  className={styles.card}
                  {...cardItem}
                  key={index}
                />
              ),
            })
          })
          break
        case 'heading':
          itemsToRender.push({
            component: (
              <LineHeading
                className={styles.lineHeading}
                title={item.title}
                disableLine={!item.hasLine}
                size={item.titleSize}
                {...item}
                key={index}
              />
            ),
          })
          break
        case 'textAndImage':
          itemsToRender.push({
            component: (
              <ContainerOrLink
                key={index}
                link={item.link}
                className={styles.textAndImage}
              >
                {item?.title && <h2 className={styles.textAndImage__title}>{item.title}</h2>}
                {item?.image && (
                  <div
                    className={styles.textAndImage__imageContainer}
                    style={
                      {
                        '--desktop-image-width': `${item.desktopImageWidth}px`,
                        '--mobile-image-width': `${item.mobileImageWidth}px`,
                        '--aspect-ratio': `${item.image.aspectRatio}`,
                      } as React.CSSProperties
                    }
                  >
                    <SanityImage
                      source={item.image}
                      className={styles.textAndImage__image}
                      columns={{
                        sm: 8,
                        md: 3,
                      }}
                    />
                  </div>
                )}
              </ContainerOrLink>
            ),
          })
          break
        case 'titleAndText':
          itemsToRender.push({
            label: item.title,
            value: item.text,
            link: item.link,
          })
          break
        default:
          break
      }
    })
    return itemsToRender
  }, [items])

  if (!renderedItems?.length) return null

  return (
    <div className={classnames(styles.Sidebar, className)}>
      <ItemLabelList items={renderedItems} />
    </div>
  )
}

Sidebar.displayName = 'Sidebar'

export default Sidebar
