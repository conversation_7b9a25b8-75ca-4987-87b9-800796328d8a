.Sidebar {
  width: 100%;
  text-align: left;
}

.button {
  display: inline-flex;
}

.lineHeading {
  margin-top: px(20);
}

.textAndImage__title {
  @include font-heading-eyebrow-md;
  margin-bottom: px(24);
}

.textAndImage__imageContainer {
  aspect-ratio: var(--aspect-ratio);
  width: 100%;
  max-width: var(--mobile-image-width);

  @include bp(tablet) {
    max-width: var(--desktop-image-width);
  }
}

.textAndImage__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
