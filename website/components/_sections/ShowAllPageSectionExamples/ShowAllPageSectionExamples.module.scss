/* stylelint-disable-next-line block-no-empty */
.ShowAllPageSectionExamples {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
  display: grid;
  grid-template-columns: 1fr;
  gap: px(24);

  @include bp(mobile) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include bp(tablet) {
    grid-template-columns: repeat(4, 1fr);
    gap: px(40);
  }
}

.item {
  display: flex;
  flex-direction: column;
}

.item__imageContainer {
  position: relative;
  aspect-ratio: var(--aspect-ratio-square);
  background-color: var(--gray-quiet);
}

.item__image {
  @include position-100(absolute);
  transform: scale(0.95);
  object-fit: contain;
}

.item__content {
  display: flex;
  flex-direction: column;
  margin-top: px(8);

  @include bp(mobile) {
    margin-top: px(16);
  }
}

.item__title {
  @include font-heading-sm;
}

.item__figmaLink {
  @include font-text-sm;
}
