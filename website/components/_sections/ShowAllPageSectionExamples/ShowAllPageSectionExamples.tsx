'use client'

import classnames from 'classnames'
import styles from './ShowAllPageSectionExamples.module.scss'
import Link from '@/components/Link/Link'
import SanityImage from '@/components/SanityImage/SanityImage'
import { DOC_TYPES } from '@/data'

const ShowAllPageSectionExamples = ({ className, items }: SanityShowAllPageSectionExamples) => {
  if (!items?.length) return null

  return (
    <div className={classnames(styles.ShowAllPageSectionExamples, className)}>
      <div className={styles.inner}>
        {items.map((item, i) => (
          <div
            className={styles.item}
            key={i}
          >
            <Link
              link={{
                linkType: 'internal',
                link: {
                  slug: item.slug.current,
                  _type: DOC_TYPES.PAGE_SECTION_EXAMPLE,
                },
              }}
            >
              <div className={styles.item__imageContainer}>
                {item.imageExample && (
                  <SanityImage
                    source={item.imageExample}
                    className={styles.item__image}
                    columns={{
                      md: 4,
                      sm: 12,
                    }}
                  />
                )}
              </div>
              <div className={styles.item__content}>
                <h3 className={styles.item__title}>{item.title}</h3>
              </div>
            </Link>
            {item.figmaLink && (
              <div className={styles.item__figmaLink}>
                <Link
                  link={{
                    linkType: 'external',
                    link: item.figmaLink,
                  }}
                >
                  <span>View in Figma</span>
                </Link>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

ShowAllPageSectionExamples.displayName = 'ShowAllPageSectionExamples'

export default ShowAllPageSectionExamples
