/* stylelint-disable-next-line block-no-empty */
.LogoSliderSection {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
}

.richTextContent {
  @include rich-text-base-styling;
  text-align: center;
  max-width: px(658);
  margin: 0 auto px(32);

  [data-h2] {
    @include font-heading-lg;
  }

  [data-p] {
    @include font-text-base;
  }
}

.logoCarousel {
  &::before,
  &::after {
    content: '';
    background: linear-gradient(90deg, rgba($white, 1) 0%, rgba($white, 0) 100%);
    display: block;
    position: absolute;
    top: 0;
    width: calc(var(--page-gutter) * 1.2);
    height: 100%;
    z-index: 3;
    pointer-events: none;
  }

  &::before {
    left: 0;
  }

  &::after {
    right: 0;
    transform: rotate(180deg);
  }
}
