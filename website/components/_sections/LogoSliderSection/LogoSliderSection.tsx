'use client'

import classnames from 'classnames'
import styles from './LogoSliderSection.module.scss'
import RichTextV2 from '@/components/RichTextV2/RichTextV2'
import LogoCarousel from '@/components/LogoCarousel/LogoCarousel'

const LogoSliderSection = ({ className, content, logoItems }: SanityLogoSliderSection) => {
  if (!logoItems?.length) {
    return null
  }

  return (
    <div className={classnames(styles.LogoSliderSection, className)}>
      <div className={styles.inner}>
        {!!content?.length && (
          <div className={styles.richTextContent}>
            <RichTextV2 content={content} />
          </div>
        )}
        <LogoCarousel
          className={styles.logoCarousel}
          items={logoItems}
        />
      </div>
    </div>
  )
}

LogoSliderSection.displayName = 'LogoSliderSection'

export default LogoSliderSection
