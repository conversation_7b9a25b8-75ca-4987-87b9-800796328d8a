/* stylelint-disable-next-line block-no-empty */
.SearchBarAndContent {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
  width: 100%;
}

.title {
  @include font-heading-lg;
  margin-bottom: px(24);
  text-align: center;

  @include bp(tablet) {
    margin-bottom: px(48);
  }
}

.searchBarForm {
  display: flex;
  flex-direction: column;
  gap: px(8);

  @include bp(laptop) {
    display: grid;
    column-gap: px(16);

    [data-search-bar-categories-length='1'] & {
      grid-template-columns: 0.75fr 0.25fr;
    }

    [data-search-bar-categories-length='2'] & {
      grid-template-columns: 0.5fr 0.25fr 0.25fr;
    }

    [data-search-bar-categories-length='3'] & {
      grid-template-columns: 0.25fr 0.25fr 0.25fr 0.25fr;
    }
  }
}

.content {
  @include bp(tablet) {
    margin-top: px(40);
  }

  .hasFilters & {
    @include bp(tablet) {
      display: grid;
      grid-template-columns: #{px(300)} 1fr;
      gap: px(40);
    }
  }
}

.filters {
  display: block;

  @include bp(tablet, true) {
    margin-top: px(8);
  }

  @include bp(tablet) {
    display: block;
  }
}

.mobileFiltersButton.specificity {
  @include bp(tablet) {
    display: none;
  }
}

.filtersInner {
  display: none;
  flex-direction: column;
  gap: px(24);
  padding-top: px(16);

  @include bp(tablet) {
    padding-top: 0;
    display: flex;
  }

  .mobileFiltersOpen & {
    @include bp(tablet, true) {
      display: flex;
    }
  }
}

.resultsList,
.noResults {
  margin-top: px(24);

  @include bp(tablet) {
    margin-top: 0;
  }
}

.resultsList {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  column-gap: px(24);
  row-gap: px(24);

  @include bp(laptop) {
    column-gap: px(40);
    row-gap: px(32);
  }

  .hasFilters & {
    grid-template-columns: repeat(2, minmax(0, 1fr));

    @include bp(laptop) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
}

.card {
  opacity: 1;
}

.pagination {
  width: 100%;
  margin-top: px(40);
}

.filterCheckboxListTitle {
  @include font-text-base;
  font-weight: 700;
  color: var(--navy-dark);
  margin-bottom: px(6);
}

.noResults {
  @include font-text-lg;
  text-align: center;
  padding: var(--gutter);
  background-color: var(--gray-quiet);
  display: block;
  height: fit-content;
}
