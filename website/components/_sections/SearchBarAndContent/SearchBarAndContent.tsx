'use client'

import classnames from 'classnames'
import styles from './SearchBarAndContent.module.scss'
import FormElement from '@/components/FormElement/FormElement'
import { useEffect, useMemo, useRef, useState, Suspense } from 'react'
import { SEARCH_PER_PAGE, SEARCH_QUERY_PARAM } from '@/data'
import Card from '@/components/Card/Card'
import { formatCardFieldsForComponent } from '@/lib/utils/card'
import Pagination from '@/components/Pagination/Pagination'
import axios from 'axios'
import gsap from 'gsap'
import { ScrollToPlugin } from 'gsap/ScrollToPlugin'
import { useRouter, usePathname, useSearchParams } from 'next/navigation'
import Button from '@/components/Button/Button'

gsap.registerPlugin(ScrollToPlugin)

const TEXT_ID = 'textField'

const getTotalPages = (totalCount: number) => {
  return Math.ceil(totalCount / SEARCH_PER_PAGE)
}

const ENDPOINT = '/api/searchBar'
const SCROLL_OFFSET = 70

const formatFiltersForQueryParams = (filters: SanitySearchBarFilterData) => {
  let value = JSON.stringify(Object.values(filters))
  value = value.replace(/[\s\\]/g, '')
  return value
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const sortArrayByObjectKey = <T extends Record<string, any>>(array: T[], key?: keyof T): T[] => {
  if (!key || !array?.length) return array

  return [...array].sort((a, b) => {
    const valueA = a[key]
    const valueB = b[key]

    if (typeof valueA === 'string' && typeof valueB === 'string') {
      return valueA.localeCompare(valueB)
    }

    return 0
  })
}

const SearchBarAndContent = ({
  title,
  searchFieldLabel,
  typesToQuery,
  searchBarCategories,
  filterCategories,
  totalCount,
  items,
  cardStyle,
  displayedCardFields,
  emptyInitially,
  disableUrlUpdates,
}: SanitySearchBarAndContent) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()
  const [currentPage, setCurrentPage] = useState(0)
  const [totalPages, setTotalPages] = useState(getTotalPages(totalCount))
  const [currentItems, setCurrentItems] = useState(items)
  const hasResults = useMemo(() => currentItems?.length > 0, [currentItems])
  const [isFetching, setIsFetching] = useState(false)
  const hasFetched = useRef(false)
  const $container = useRef<HTMLDivElement>(null)
  const hasFilters = useMemo(() => Object.keys(filterCategories || {}).length > 0, [filterCategories])
  const textDebounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null)
  const fetchedBasedOnInitialFilters = useRef(false)
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false)

  const initialFiltersInUrl = useMemo(() => {
    if (disableUrlUpdates) return {}

    const queryParam = searchParams.get(SEARCH_QUERY_PARAM)
    if (queryParam) {
      try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let param: any = decodeURIComponent(queryParam)
        param = JSON.parse(param) // Parse outer string
        param = JSON.parse(param) // Parse inner string

        const paramWithKeys: SanitySearchBarFilterData = {}
        if (param?.length > 0) {
          param.forEach((item: SanitySearchBarFilterItem) => {
            if (!item.slug) return
            paramWithKeys[item.slug] = item
          })
        }

        return paramWithKeys
      } catch (err) {
        console.error('Failed to parse search query param:', err)
        return {}
      }
    } else {
      return {}
    }
  }, [searchParams, disableUrlUpdates])
  const [categoryFilters, setCategoryFilters] = useState<SanitySearchBarFilterData>(initialFiltersInUrl)
  const [hideResults, setHideResults] = useState(emptyInitially || !!Object.keys(initialFiltersInUrl).length)
  const fetchRefs = useRef<{
    currentPage: number
    typesToQuery: string[]
    categoryFilters: SanitySearchBarFilterData
  }>({
    currentPage: 0,
    typesToQuery: [],
    categoryFilters: {},
  })
  const numberOfFiltersActive = useMemo(() => {
    if (!hasFilters) return 0

    let count = 0

    filterCategories.forEach(category => {
      if (categoryFilters[category.slug?.current]?.ids?.length) {
        count++
      }
    })

    return count
  }, [categoryFilters, hasFilters, filterCategories])

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const routerRefs = useRef<any>({
    pathname: pathname,
    searchParams: searchParams,
    router: router,
  })

  useEffect(() => {
    routerRefs.current.pathname = pathname
    routerRefs.current.searchParams = searchParams
    routerRefs.current.router = router
    fetchRefs.current.currentPage = currentPage
    fetchRefs.current.typesToQuery = typesToQuery
    fetchRefs.current.categoryFilters = categoryFilters
  }, [pathname, searchParams, router, currentPage, typesToQuery, categoryFilters])

  const updatePathQuery = (categoryFilters: SanitySearchBarFilterData) => {
    if (disableUrlUpdates) return
    if (Object.keys(categoryFilters).length > 0) {
      const { pathname, router } = routerRefs.current
      const formattedFilters = formatFiltersForQueryParams(categoryFilters)
      const query = JSON.stringify(formattedFilters)
      const params = new URLSearchParams('')
      params.set(SEARCH_QUERY_PARAM, query)

      router.push(`${pathname}?${params.toString()}`, { scroll: false })
    } else {
      router.push(pathname, { scroll: false })
    }
  }

  const scrollToContainer = () => {
    if (!$container.current) return

    gsap.to(window, {
      duration: 0.2,
      scrollTo: {
        y: $container.current,
        offsetY: SCROLL_OFFSET,
      },
      ease: 'Power3.easeOut',
    })
  }

  const fetchPosts = async () => {
    if (!typesToQuery) {
      console.error('No typesToQuery found')
      return
    }

    setIsFetching(true)

    const {
      currentPage: _currentPage,
      typesToQuery: _typesToQuery,
      categoryFilters: _categoryFilters,
    } = fetchRefs.current

    try {
      const response = await axios.post(ENDPOINT, {
        page: _currentPage,
        typesToQuery: _typesToQuery,
        categoryFilters: _categoryFilters,
      })

      if (response?.data?.totalCount) {
        setTotalPages(getTotalPages(response.data.totalCount))
      }

      if (response?.data?.items) {
        setCurrentItems(response.data.items)
      }
    } catch (error) {
      console.error(error)
    } finally {
      scrollToContainer()
      setIsFetching(false)
      setHideResults(false)
      updatePathQuery(_categoryFilters)
    }
  }

  const resetPagination = (page?: number) => {
    hasFetched.current = true
    setCurrentPage(page || 0)
  }

  const resetPaginationAndFetchPosts = (page?: number) => {
    hasFetched.current = true
    resetPagination(page)
    setMobileFiltersOpen(false)

    setTimeout(() => {
      fetchPosts()
    }, 20)
  }

  useEffect(() => {
    if (!fetchedBasedOnInitialFilters.current) {
      if (Object.keys(initialFiltersInUrl).length !== 0) {
        setTimeout(() => {
          resetPaginationAndFetchPosts()
          fetchedBasedOnInitialFilters.current = true
        }, 100)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialFiltersInUrl])

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleFormSubmit = (e: any) => {
    e.preventDefault()
  }

  const handleSelectOnChange = (value: string, parentCategory: SanitySearchBarCategory, childId?: string) => {
    if (parentCategory?.slug?.current) {
      setCategoryFilters((prev: SanitySearchBarFilterData) => {
        if (!value) {
          const copy = { ...prev }
          delete copy[parentCategory?.slug?.current]
          return copy
        }

        if (childId) {
          return {
            ...prev,
            [parentCategory?.slug?.current]: {
              id: parentCategory?._id,
              slug: parentCategory?.slug?.current,
              type: parentCategory?.filterType,
              ids: [childId],
            },
          }
        }

        return prev
      })
      resetPaginationAndFetchPosts()
    } else {
      console.error(`No parentSlug (${parentCategory?.slug?.current}) and/or childId (${childId}) found for category`)
    }
  }

  const handleSearchInputChange = (value?: string) => {
    if (value) {
      setCategoryFilters((prev: SanitySearchBarFilterData) => {
        return {
          ...prev,
          [TEXT_ID]: {
            id: TEXT_ID,
            slug: TEXT_ID,
            type: 'text',
            text: value,
          },
        }
      })
    } else {
      setCategoryFilters((prev: SanitySearchBarFilterData) => {
        const copy = { ...prev }
        delete copy[TEXT_ID]
        return copy
      })
    }

    resetPaginationAndFetchPosts()
  }

  return (
    <div
      className={classnames(
        styles.SearchBarAndContent,
        {
          [styles.hasResults]: hasResults,
        },
        {
          [styles.hasFilters]: hasFilters,
        },
        {
          [styles.mobileFiltersOpen]: mobileFiltersOpen,
        },
      )}
      data-search-bar-categories-length={searchBarCategories?.length}
      ref={$container}
    >
      <div className={styles.inner}>
        {title && <h2 className={styles.title}>{title}</h2>}
        <form
          onSubmit={handleFormSubmit}
          className={styles.searchBarForm}
        >
          <FormElement
            initialValue={categoryFilters[TEXT_ID]?.text ? categoryFilters[TEXT_ID].text : ''}
            element="input"
            type="text"
            name="search"
            placeholder={searchFieldLabel || 'Search'}
            buttonSide="left"
            buttonOnClick={value => {
              handleSearchInputChange(value.currentValue)
            }}
            buttonIcon="magnifyingGlass"
            className={styles.searchInput}
            onChange={value => {
              if (textDebounceTimeout.current) {
                clearTimeout(textDebounceTimeout.current)
              }

              textDebounceTimeout.current = setTimeout(() => {
                handleSearchInputChange(value)
              }, 400)
            }}
          />
          {searchBarCategories?.map((category, index) => {
            const initialValue = categoryFilters[category.slug?.current]?.ids?.[0] || ''
            return (
              <FormElement
                key={`${category.slug?.current}-${index}`}
                initialValue={initialValue}
                element="select"
                name={category.slug?.current}
                placeholder={category.title}
                items={sortArrayByObjectKey(category.children, 'title').map(child => ({
                  label: child.title,
                  value: child._id,
                }))}
                className={styles.searchBarSelect}
                onChange={value => {
                  const childId = category?.children?.find(c => c._id === value)?._id
                  handleSelectOnChange(value, category, childId)
                }}
              />
            )
          })}
        </form>
        {!hideResults && (
          <div className={styles.content}>
            {hasFilters && (
              <div className={styles.filters}>
                <Button
                  className={classnames(styles.mobileFiltersButton, styles.specificity)}
                  style="rounded"
                  icon="filter"
                  label={`Filters${numberOfFiltersActive !== 0 ? ` (${numberOfFiltersActive})` : ''}`}
                  iconPosition="right"
                  onClick={() => {
                    setMobileFiltersOpen(prev => !prev)
                  }}
                />
                <div className={styles.filtersInner}>
                  {filterCategories.map((category, index) => {
                    const filterType = category?.filterType
                    const filterItems = category.children
                    const key = `${category.slug?.current}-${index}`

                    if (!category.title || !filterType || !filterItems) return null

                    if (filterType === 'dropdown') {
                      return (
                        <FormElement
                          label={category.title}
                          initialValue={categoryFilters[category.slug?.current]?.ids?.[0] || ''}
                          key={key}
                          element="select"
                          name={category.slug?.current}
                          placeholder={category.title}
                          items={sortArrayByObjectKey(filterItems, 'title').map(child => ({
                            label: child.title,
                            value: child._id,
                          }))}
                          className={styles.filterSelect}
                          onChange={value => {
                            const childId = category?.children?.find(c => c._id === value)?._id
                            handleSelectOnChange(value, category, childId)
                          }}
                        />
                      )
                    } else if (filterType === 'checkbox') {
                      return (
                        <div
                          className={styles.filterCheckboxList}
                          key={key}
                        >
                          <h3 className={styles.filterCheckboxListTitle}>{category.title}</h3>
                          {sortArrayByObjectKey(filterItems, 'title').map(child => {
                            const objectInUrl = categoryFilters[category.slug?.current]
                            let initialValue = false
                            if (objectInUrl) {
                              initialValue = objectInUrl.ids?.includes(child._id) || false
                            }

                            return (
                              <FormElement
                                element="input"
                                type="checkbox"
                                initialValue={initialValue}
                                key={child.slug?.current}
                                label={child.title}
                                name={child.slug?.current}
                                className={styles.filterCheckbox}
                                onChange={value => {
                                  setCategoryFilters((prev: SanitySearchBarFilterData) => {
                                    const previousValue = prev[category.slug?.current]

                                    if (value) {
                                      let newValue: string[] = [child._id]
                                      if (previousValue) {
                                        if (previousValue?.ids?.includes(child._id)) {
                                          newValue = [...previousValue?.ids]
                                        } else {
                                          if (!!previousValue?.ids?.length) {
                                            newValue = [...previousValue?.ids, child._id]
                                          } else {
                                            newValue = [child._id]
                                          }
                                        }
                                      }

                                      return {
                                        ...prev,
                                        [category.slug?.current]: {
                                          id: category._id,
                                          slug: category.slug?.current,
                                          type: 'checkbox',
                                          ids: newValue,
                                        },
                                      }
                                    } else {
                                      let newValue: string[] = []
                                      if (!!previousValue?.ids?.length) {
                                        newValue = [...previousValue.ids.filter(id => id !== child._id)]
                                      }

                                      if (newValue?.length === 0) {
                                        const copy = { ...prev }
                                        delete copy[category.slug?.current]
                                        return copy
                                      }

                                      return {
                                        ...prev,
                                        [category.slug?.current]: {
                                          id: category._id,
                                          slug: category.slug?.current,
                                          type: 'checkbox',
                                          ids: newValue,
                                        },
                                      }
                                    }
                                  })
                                  resetPaginationAndFetchPosts()
                                }}
                              />
                            )
                          })}
                        </div>
                      )
                    } else if (filterType === 'radio') {
                      return (
                        <div
                          className={styles.filterCheckboxList}
                          key={key}
                        >
                          <FormElement
                            element="radioGroup"
                            initialValue={categoryFilters[category.slug?.current]?.ids?.[0] || ''}
                            key={category?.slug?.current}
                            label={category.title}
                            name={category.slug?.current}
                            placeholder="All"
                            items={sortArrayByObjectKey(filterItems, 'title').map(child => ({
                              label: child.title,
                              value: child._id,
                            }))}
                            onChange={value => {
                              setCategoryFilters((prev: SanitySearchBarFilterData) => {
                                if (value) {
                                  return {
                                    ...prev,
                                    [category.slug?.current]: {
                                      id: category._id,
                                      slug: category.slug?.current,
                                      type: 'radio',
                                      ids: [value],
                                    },
                                  }
                                } else {
                                  const copy = { ...prev }
                                  delete copy[category.slug?.current]
                                  return copy
                                }
                              })
                              resetPaginationAndFetchPosts()
                            }}
                          />
                        </div>
                      )
                    }

                    return null
                  })}
                </div>
              </div>
            )}
            {hasResults ? (
              <div className={styles.results}>
                <div className={styles.resultsList}>
                  {currentItems.map((item, index) => (
                    <Card
                      key={`${item.slug}-${index}`}
                      {...formatCardFieldsForComponent({
                        props: item,
                        allowedFields: displayedCardFields?.fieldsAllowed,
                        cardStyle: cardStyle,
                      })}
                      className={styles.card}
                    />
                  ))}
                </div>
                <Pagination
                  pageCount={totalPages}
                  currentPage={currentPage + 1}
                  isDisabled={isFetching}
                  onPageClick={page => {
                    resetPaginationAndFetchPosts(page - 1)
                  }}
                  onButtonClick={action => {
                    if (action === 'next' && currentPage < totalPages) {
                      resetPaginationAndFetchPosts(currentPage + 1)
                    } else if (action === 'prev' && currentPage === 0) {
                      resetPaginationAndFetchPosts(currentPage - 1)
                    }
                  }}
                  className={styles.pagination}
                />
              </div>
            ) : (
              <p className={styles.noResults}>No results found.</p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

SearchBarAndContent.displayName = 'SearchBarAndContent'

export default function SearchBarAndContentWithSuspense(props: SanitySearchBarAndContent) {
  // console.log({
  //   props,
  // })

  return (
    <Suspense fallback={<div />}>
      <SearchBarAndContent {...props} />
    </Suspense>
  )
}
