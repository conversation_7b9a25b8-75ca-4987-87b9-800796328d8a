'use client'

import classnames from 'classnames'
import styles from './HeaderWithSubnav.module.scss'
import { portableTextSerializer } from '@/components/RichTextV2/RichTextV2'
import RichTextV2 from '@/components/RichTextV2/RichTextV2'
import SanityImage from '@/components/SanityImage/SanityImage'
import { CSSProperties } from 'react'
import Subnavigation from '@/components/Subnavigation/Subnavigation'
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const TITLE_SERIALIZER: any = {
  ...portableTextSerializer,
}

// TODO: Add breadcrumb
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const HeaderWithSubnav = ({ className, hasBreadcrumb, title, links, description }: SanityHeaderWithSubnav) => {
  return (
    <div className={classnames(styles.HeaderWithSubnav, className)}>
      <div className={styles.inner}>
        <div className={styles.heading}>
          {typeof title === 'string' ? (
            <h1 className={styles.heading__title}>{title}</h1>
          ) : (
            <span className={styles.heading__richText}>
              <RichTextV2
                content={title}
                serializer={TITLE_SERIALIZER}
              />
            </span>
          )}
        </div>
        {description && <p className={styles.description}>{description}</p>}
        <Subnavigation
          className={styles.subnav}
          links={links}
        />
      </div>
    </div>
  )
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
TITLE_SERIALIZER.types.subnavTitleImage = ({ value }: { value: any }) => {
  return (
    <div
      className={styles.subnavTitleImage}
      style={
        {
          '--desktop-width': `${value.desktopWidth}px`,
          '--mobile-width': `${value.mobileWidth}px`,
          '--image-aspect-ratio': `${value.image.asset.aspectRatio}`,
        } as CSSProperties
      }
    >
      <h1 className={styles.subnavTitleImage__title}>
        {value?.image && (
          <SanityImage
            source={value.image}
            className={styles.subnavTitleImage__image}
            columns={3}
          />
        )}
        <span className={styles.subnavTitleImage__titleText}>{value.title}</span>
      </h1>
    </div>
  )
}

HeaderWithSubnav.displayName = 'HeaderWithSubnav'

export default HeaderWithSubnav
