/* stylelint-disable-next-line block-no-empty */
@mixin description-base-styling {
  @include font-heading-sm;
  color: var(--slate);
  font-style: italic;
  text-align: center;
}

.HeaderWithSubnav {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
}

.heading {
  text-align: center;
}

.description {
  @include description-base-styling;
  margin-top: 1em;
}

.heading__richText {
  @include rich-text-base-styling;

  [data-p] {
    @include description-base-styling;
  }
}

.subnav {
  margin-top: px(20);
}

.subnavTitleImage__title,
.heading__title {
  @include font-heading-xl;
}

img.subnavTitleImage__image {
  display: inline-block;
  width: var(--mobile-width);
  aspect-ratio: var(--image-aspect-ratio);
  margin-right: px(14);
  vertical-align: middle;

  @include bp(tablet) {
    width: var(--desktop-width);
  }
}

.subnavTitleImage__titleText {
  display: inline-block;
  transform: translateY(11%); // to align with the image
}
