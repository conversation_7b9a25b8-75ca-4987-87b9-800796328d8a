import classnames from 'classnames'
import styles from './AdSection.module.scss'
import Ad from '@/components/Ad/Ad'

const AdSection = ({ className, title, ad }: SanityAdSection) => {
  if (!ad) return null

  return (
    <div
      className={classnames(styles.AdSection, className)}
      data-ad-section-type={ad.type}
    >
      <div className={styles.inner}>
        <div className={styles.adContainer}>
          <Ad
            className={styles.ad}
            title={title}
            {...ad}
          />
        </div>
      </div>
    </div>
  )
}

AdSection.displayName = 'AdSection'

export default AdSection
