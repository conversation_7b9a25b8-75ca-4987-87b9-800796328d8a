'use client'

import React from 'react'
import classnames from 'classnames'
import styles from './SubscriptionPlansSection.module.scss'
import CardPlan from '@/components/CardPlan/CardPlan'

const SubscriptionPlansSection = ({ className, title, subtitle, items }: SanitySubscriptionPlansSection) => {
  if (!title || !subtitle || !items || items.length < 3) return null

  return (
    <div className={classnames(styles.SubscriptionPlansSection, className)}>
      <div className={styles.inner}>
        <div className={styles.header}>
          <h2 className={styles.title}>{title}</h2>
          <p className={styles.subtitle}>{subtitle}</p>
        </div>
        <div className={styles.plansContainer}>
          {items.map((item: CardPlanProps, index: number) => (
            <React.Fragment key={`${item.title}-${index}`}>
              <CardPlan
                className={styles.cardPlan}
                {...item}
              />
              {index < items.length - 1 && <div className={styles.hr} />}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  )
}

SubscriptionPlansSection.displayName = 'SubscriptionPlansSection'

export default SubscriptionPlansSection
