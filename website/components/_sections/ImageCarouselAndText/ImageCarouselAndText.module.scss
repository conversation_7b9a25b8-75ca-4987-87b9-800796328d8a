/* stylelint-disable-next-line block-no-empty */
.ImageCarouselAndText {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
  display: grid;
  grid-template-columns: minmax(0, 1fr);
  row-gap: px(24);
  align-items: center;

  @include bp(tablet) {
    grid-template-columns: minmax(0, 0.5fr) minmax(0, 0.5fr);
    column-gap: px(40);
  }

  @include bp(laptop) {
    column-gap: px(80);
  }
}

.carouselContent {
  @include bp(tablet, true) {
    order: 2;
  }
}

.content {
  @include bp(tablet, true) {
    order: 1;
  }
}

.title {
  @include font-heading-lg;
}

.authorAndDate {
  margin-top: px(4);
}

.description {
  @include font-text-lg;
  margin-top: px(16);
}

.description__text {
  margin-right: 0.5em;
}

.link {
  font-weight: 700;
  text-decoration: underline;
  display: inline-block;
  text-wrap: initial;
}
