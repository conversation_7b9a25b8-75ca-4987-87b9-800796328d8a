'use client'

import classnames from 'classnames'
import styles from './ImageCarouselAndText.module.scss'
import { SanityImageCarouselAndText } from '@/types/sections/ImageCarouselAndText'
import ImageCarousel from '@/components/ImageCarousel/ImageCarousel'
import AuthorDateDisplay from '../../AuthorDateDisplay/AuthorDateDisplay'
import { truncateText } from '@/lib/text'
import Link from '@/components/Link/Link'

const ImageCarouselAndText = ({
  className,
  useContentReference,
  referenceForContent,
  title,
  authors,
  description,
  images,
  link,
}: SanityImageCarouselAndText) => {
  const isContentReference = useContentReference === true && referenceForContent
  link =
    isContentReference && referenceForContent
      ? ({
          linkType: 'internal',
          label: 'Read More',
          link: {
            _type: referenceForContent?._type,
            slug: referenceForContent?.slug,
          },
        } as SanityLink)
      : link
  title = title || referenceForContent?.title
  description = description || referenceForContent?.description
  images = images.filter(image => image?.asset)

  if (!images?.length) {
    return null
  }

  return (
    <div className={classnames(styles.ImageCarouselAndText, className)}>
      <div className={styles.inner}>
        <div className={styles.carouselContent}>
          <ImageCarousel images={images} />
        </div>
        <div className={styles.content}>
          <h2 className={styles.title}>{title}</h2>
          {!!authors?.length && (
            <AuthorDateDisplay
              className={styles.authorAndDate}
              authors={authors}
            />
          )}
          {description && (
            <div className={styles.description}>
              <span className={styles.description__text}>{truncateText(description, 200)}</span>
              <Link
                className={styles.link}
                link={link}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

ImageCarouselAndText.displayName = 'ImageCarouselAndText'

export default ImageCarouselAndText
