'use client'
import classnames from 'classnames'
import styles from './TitleTextAndCtaSection.module.scss'
import TitleTextAndCta from '@/components/TitleTextAndCta/TitleTextAndCta'
import { SanityTitleTextAndCtaSection } from '@/types/sections/TitleTextAndCtaSection'

const TitleTextAndCtaSection = ({ className, content }: SanityTitleTextAndCtaSection) => {
  if (!content || !content.title) return null

  return (
    <div className={classnames(styles.TitleTextAndCtaSection, className)}>
      <div className={styles.inner}>
        <TitleTextAndCta
          title={content.title}
          description={content.description}
          cta={content.cta}
        />
      </div>
    </div>
  )
}

export default TitleTextAndCtaSection
