/* stylelint-disable-next-line block-no-empty */
.ContentList {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: px(28);

  @include bp(tablet) {
    margin-bottom: px(36);
  }
}

.title {
  @include font-heading-lg;

  [data-content-list-title-size='sm'] & {
    @include font-heading-eyebrow-lg;
  }
}

.content {
  display: flex;

  [data-content-list-list-type='vertical'] & {
    justify-content: center;
  }
}

.verticalList {
  display: flex;
  flex-direction: column;
  max-width: px(960);

  [data-content-list-container-size='small'] & {
    max-width: px(800);
  }
}

.verticalList__item {
  &:not(:last-child) {
    margin-bottom: px(28);

    @include bp(tablet) {
      margin-bottom: px(40);
    }
  }
}

.verticalList__card {
  width: 100%;
}

.stackedList {
  @include reset-ul;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: px(32);
  row-gap: px(48);

  @include bp(mobile) {
    grid-template-columns: repeat(4, 1fr);
  }

  @include bp(laptop) {
    grid-template-columns: repeat(var(--per-row), 1fr);
  }
}
