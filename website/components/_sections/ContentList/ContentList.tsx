'use client'

import classnames from 'classnames'
import styles from './ContentList.module.scss'
import { formatCardItemsObjectToItems } from '@/lib/utils/card'
import Card from '@/components/Card/Card'
import Button from '@/components/Button/Button'
import { CSSProperties } from 'react'
import CardCarousel from '@/components/CardCarousel/CardCarousel'

const ContentList = ({
  className,
  title,
  titleSize,
  listType,
  containerSize,
  perRow,
  items,
  cta,
}: SanityContentList) => {
  const cardItems = formatCardItemsObjectToItems(items)

  if (!cardItems.length) return null

  return (
    <div
      className={classnames(styles.ContentList, className)}
      data-content-list-list-type={listType}
      data-content-list-title-size={titleSize}
      data-content-list-container-size={containerSize}
      style={
        {
          '--per-row': parseInt(perRow || '5'),
        } as CSSProperties
      }
    >
      <div className={styles.inner}>
        <div className={styles.header}>
          {title && <h1 className={styles.title}>{title}</h1>}
          {cta && (
            <Button
              className={styles.cta}
              link={cta}
              bareColor="loud"
              style="bare"
              icon="arrowRight"
              iconPosition="right"
            />
          )}
        </div>
        <div className={styles.content}>
          {(listType === 'stacked' || listType === 'vertical') && (
            <ul className={listType === 'vertical' ? styles.verticalList : styles.stackedList}>
              {cardItems.map((card, i) => (
                <li
                  key={i}
                  className={listType === 'vertical' ? styles.verticalList__item : styles.stackedList__item}
                >
                  <Card
                    {...card}
                    className={listType === 'vertical' ? styles.verticalList__card : styles.stackedList__card}
                  />
                </li>
              ))}
            </ul>
          )}
          {listType === 'carousel' && <CardCarousel items={cardItems} />}
        </div>
      </div>
    </div>
  )
}

ContentList.displayName = 'ContentList'

export default ContentList
