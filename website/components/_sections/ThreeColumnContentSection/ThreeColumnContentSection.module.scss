.threeColumnContentSection {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
}

.container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  text-align: center;

  @include bp(tablet) {
    flex-direction: row;
  }
}

.content__item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-bottom: 1px solid rgba($navy-dark, 0.2);
  padding: px(40) 0;

  &:last-child {
    border-bottom: none;
  }

  @include bp(tablet) {
    border-bottom: none;
    border-right: 1px solid rgba($navy-dark, 0.2);

    &:last-child {
      border-right: none;
    }
    padding: 0 px(40);
  }
}

.eyebrow {
  @include font-heading-eyebrow-md;
  margin-bottom: px(16);
}

.eyebrow__line__items {
  @include reset-ul;
}

.eyebrow__line__item {
  @include font-heading-md;
  margin-bottom: px(16);

  &:last-child {
    margin-bottom: 0;
  }
}

.imageWrapper {
  aspect-ratio: var(--aspect-ratio);
  position: relative;
}

.image {
  @include position-100(absolute);
  object-fit: cover;
}
