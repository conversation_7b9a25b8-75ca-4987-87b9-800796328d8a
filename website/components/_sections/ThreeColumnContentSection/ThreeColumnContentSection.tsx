'use client'

import classnames from 'classnames'
import styles from './ThreeColumnContentSection.module.scss'
import SanityImage from '@/components/SanityImage/SanityImage'
import TitleTextAndCta from '@/components/TitleTextAndCta/TitleTextAndCta'
import { CSSProperties } from 'react'

const ThreeColumnContentSection = ({ className, items }: SanityThreeColumnContentSection) => {
  if (!items || items.length !== 3) return null

  return (
    <div className={classnames(styles.threeColumnContentSection, className)}>
      <div className={styles.inner}>
        <div className={styles.container}>
          {items.map((item: SanityThreeColumnContentSection, index: number) => (
            <div
              key={index}
              className={styles.content__item}
            >
              {item._type === 'imageAsset' && (
                <div
                  className={styles.imageWrapper}
                  style={
                    {
                      '--aspect-ratio': item?.asset?.aspectRatio,
                    } as CSSProperties
                  }
                >
                  <SanityImage
                    source={item}
                    className={styles.image}
                    columns={{
                      md: 4,
                      sm: 12,
                    }}
                  />
                </div>
              )}
              {item._type === 'titleTextAndCta' && <TitleTextAndCta {...item} />}
              {item._type === 'eyebrowWithLineItems' && (
                <div className={styles.eyebrow__with__line__items}>
                  <div className={styles.eyebrow}>{item.eyebrow}</div>
                  <ul className={styles.eyebrow__line__items}>
                    {item.items.map((lineItem: string, lineItemIndex: number) => (
                      <li
                        key={lineItemIndex}
                        className={styles.eyebrow__line__item}
                      >
                        {lineItem}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

ThreeColumnContentSection.displayName = 'ThreeColumnContentSection'

export default ThreeColumnContentSection
