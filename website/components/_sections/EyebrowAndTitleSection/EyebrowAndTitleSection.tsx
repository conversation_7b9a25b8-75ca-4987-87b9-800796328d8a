'use client'

import classnames from 'classnames'
import styles from './EyebrowAndTitleSection.module.scss'

const EyebrowAndTitleSection = ({ className, eyebrow, title }: SanityEyebrowAndTitleSection) => {
  if (!title) return null

  return (
    <div className={classnames(styles.EyebrowAndTitleSection, className)}>
      <div
        className={styles.inner}
        role="note"
        aria-label={title}
      >
        {eyebrow && (
          <div className={styles.headerRow}>
            <hr
              className={styles.divider}
              aria-hidden="true"
            />
            <span className={styles.eyebrow}>{eyebrow}</span>
            <hr
              className={styles.divider}
              aria-hidden="true"
            />
          </div>
        )}
        <div className={styles.title}>{title}</div>
      </div>
    </div>
  )
}

EyebrowAndTitleSection.displayName = 'EyebrowAndTitleSection'

export default EyebrowAndTitleSection
