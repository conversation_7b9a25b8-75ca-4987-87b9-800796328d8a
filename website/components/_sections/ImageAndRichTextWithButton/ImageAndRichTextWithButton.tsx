'use client'

import classnames from 'classnames'
import styles from './ImageAndRichTextWithButton.module.scss'
import SanityImage from '@/components/SanityImage/SanityImage'
import RichTextV2 from '@/components/RichTextV2/RichTextV2'
import Video from '@/components/Video/Video'
import { CSSProperties } from 'react'

const IMAGE_ASPECT_RATIO = 16 / 9

const ImageAndRichTextWithButton = ({ className, media, description }: SanityImageAndRichTextWithButton) => {
  const mediaItem = media && media.length > 0 ? media[0] : null

  if (!mediaItem) return null

  return (
    <div className={classnames(styles.ImageAndRichTextWithButton, className)}>
      <div className={styles.inner}>
        <div className={styles.mediaWrapper}>
          {mediaItem._type === 'imageAsset' && (
            <div
              className={styles.imageWrapper}
              style={
                {
                  '--image-aspect-ratio': IMAGE_ASPECT_RATIO,
                } as CSSProperties
              }
            >
              <SanityImage
                source={mediaItem}
                className={styles.image}
                columns={8}
                aspectRatio={IMAGE_ASPECT_RATIO}
              />
            </div>
          )}
          {mediaItem._type === 'video' && (
            <Video
              type={mediaItem.type}
              youtubeId={mediaItem.youtubeId}
              vimeoId={mediaItem.vimeoId}
              url={mediaItem.url}
              aspectRatio={mediaItem.aspectRatio}
              previewImage={mediaItem.previewImage}
            />
          )}
        </div>
        <div className={styles.content}>
          {description && description.length > 0 && (
            <div className={styles.description}>
              <RichTextV2 content={description} />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

ImageAndRichTextWithButton.displayName = 'ImageAndRichTextWithButton'

export default ImageAndRichTextWithButton
