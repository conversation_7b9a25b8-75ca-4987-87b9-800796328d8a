.ImageAndRichTextWithButton {
  padding: 0 var(--page-gutter);
}

.inner {
  max-width: var(--section-max-width);
  margin: 0 auto;
  @include grid-12;
  row-gap: px(24);

  @include bp(tablet) {
    row-gap: px(48);
  }
}

.mediaWrapper {
  grid-column: 1 / -1;

  @include bp(tablet) {
    grid-column: 2 / 12;
  }
}

.imageWrapper {
  aspect-ratio: var(--image-aspect-ratio);
  width: 100%;
}

.image {
  width: 100%;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  grid-column: 1 / -1;

  @include bp(tablet) {
    grid-column: 3 / 11;
  }
}

.description {
  @include rich-text-base-styling;
  @include font-text-lg;
  text-align: center;
}
