.ClassCalendar {
  padding: 0 var(--page-gutter);
}

.inner {
  margin: 0 auto;
  max-width: var(--section-max-width);
}

.header {
  display: flex;
  flex-direction: column-reverse;
  margin-bottom: px(24);
  border-top: 1px solid rgba($navy-dark, 0.2);
  padding-top: px(16);

  @include bp(tablet) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.filter {
  @include font-text-base;
  color: var(--navy-dark);
}

.title {
  @include font-heading-lg;
  color: var(--navy-dark);
  margin-bottom: px(16);
  text-align: center;

  @include bp(tablet) {
    margin-bottom: 0;
  }
}

.viewToggle {
  display: flex;
  gap: px(24);
}

.viewToggle__button {
  color: rgba($navy-dark, 0.5);
}

.viewToggle__button_active {
  color: var(--navy-dark);
}

.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba($navy-dark, 0.2);
  border-bottom: 1px solid rgba($navy-dark, 0.2);
  padding: px(16) 0;
}

.navigation__button {
  color: var(--slate);
}

.navigation__month {
  @include font-heading-eyebrow-lg;
  color: var(--navy-dark);
}

.calendar {
  width: 100%;
}

.calendar__weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.calendar__weekday {
  @include font-heading-eyebrow-md;
  color: var(--black);
  border-right: 1px solid rgba($navy-dark, 0.2);
  padding: px(16) px(8);
  text-align: center;

  &:last-child {
    border-right: none;
  }
}

.calendar__grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-auto-rows: minmax(px(180), auto);
}

.calendar__day {
  position: relative;
  border-right: 1px solid rgba($navy-dark, 0.2);
  border-top: 1px solid rgba($navy-dark, 0.2);
  min-height: px(182);

  &:first-child {
    border-left: none;
  }

  &:nth-child(7n) {
    border-right: none;
  }
}

.calendar__day_otherMonth {
  background-color: var(--gray-quiet);

  .calendar__dayNumber {
    background-color: transparent;
  }
}

.calendar__day_hasEvents {
  background-color: var(--white);
}

.calendar__dayNumber {
  @include font-text-sm;
  @include flex-center;
  z-index: 1;
  color: var(--black);
  font-weight: 700;
  position: absolute;
  top: px(0);
  right: px(0);
  width: px(32);
  height: px(36);
  background-color: var(--white);
}

// TODO: Final styling for multiple events
.calendar__events {
  display: flex;
  flex-direction: column;
}

.calendar__event {
  padding: px(8);
}

.list {
  display: flex;
  flex-direction: column;
}

.list__item {
  display: flex;
  align-items: center;
  padding: px(16);

  &:not(:last-child) {
    border-bottom: 1px solid rgba($navy-dark, 0.1);
  }
}

.list__empty {
  @include font-text-base;
  color: var(--slate);
  text-align: center;
  padding: px(32) 0;
}
