'use client'

import { useState, useMemo, useEffect } from 'react'
import classnames from 'classnames'
import styles from './ClassCalendar.module.scss'
import CalendarCard from '@/components/CalendarCard/CalendarCard'
import FormElementSelect from '@/components/FormElement/FormElement'
import Button from '@/components/Button/Button'
import useBreakpoint from '@/hooks/use-breakpoint'

const ClassCalendar = ({ className, title, classes }: SanityClassCalendar) => {
  const { isMobile } = useBreakpoint()

  const [viewMode, setViewMode] = useState<'calendar' | 'list'>(() => (isMobile ? 'list' : 'calendar'))
  const [filter, setFilter] = useState<string>('all')

  useEffect(() => {
    setViewMode(isMobile ? 'list' : 'calendar')
  }, [isMobile])

  const [currentMonth, setCurrentMonth] = useState<Date>(new Date())

  const firstDayOfMonth = useMemo(() => {
    const date = new Date(currentMonth)
    date.setDate(1)
    return date
  }, [currentMonth])

  const lastDayOfMonth = useMemo(() => {
    const date = new Date(currentMonth)
    date.setMonth(date.getMonth() + 1)
    date.setDate(0)
    return date
  }, [currentMonth])

  // Get days from previous month to fill the first week
  const daysFromPrevMonth = useMemo(() => {
    const firstDay = firstDayOfMonth.getDay()
    const prevMonthLastDay = new Date(firstDayOfMonth)
    prevMonthLastDay.setDate(0)

    const days = []
    for (let i = firstDay - 1; i >= 0; i--) {
      const day = new Date(prevMonthLastDay)
      day.setDate(prevMonthLastDay.getDate() - i)
      days.push(day)
    }
    return days
  }, [firstDayOfMonth])

  // Get days from current month
  const daysInMonth = useMemo(() => {
    const days = []
    for (let i = 1; i <= lastDayOfMonth.getDate(); i++) {
      const day = new Date(firstDayOfMonth)
      day.setDate(i)
      days.push(day)
    }
    return days
  }, [firstDayOfMonth, lastDayOfMonth])

  // Get days from next month to fill the last week
  const daysFromNextMonth = useMemo(() => {
    const lastDay = lastDayOfMonth.getDay()
    const nextMonthFirstDay = new Date(lastDayOfMonth)
    nextMonthFirstDay.setDate(lastDayOfMonth.getDate() + 1)

    const days = []
    for (let i = 0; i < 6 - lastDay; i++) {
      const day = new Date(nextMonthFirstDay)
      day.setDate(nextMonthFirstDay.getDate() + i)
      days.push(day)
    }
    return days
  }, [lastDayOfMonth])

  const allDays = useMemo(() => {
    return [...daysFromPrevMonth, ...daysInMonth, ...daysFromNextMonth]
  }, [daysFromPrevMonth, daysInMonth, daysFromNextMonth])

  const filteredClasses = useMemo(() => {
    if (!classes) return []

    if (filter === 'all') {
      return classes
    }

    const filtered = classes.filter(classItem => {
      return classItem.skillLevel === filter
    })

    return filtered
  }, [classes, filter])

  // TODO: Handle multiple classes on the same day
  // Group classes by date
  const classesByDate = useMemo(() => {
    const grouped: Record<string, SanityClassCard[]> = {}

    if (filteredClasses) {
      filteredClasses.forEach(classItem => {
        const date = new Date(classItem.date)
        const dateKey = date.toISOString().split('T')[0]

        if (!grouped[dateKey]) {
          grouped[dateKey] = []
        }

        grouped[dateKey].push(classItem)
      })
    }

    return grouped
  }, [filteredClasses])

  const currentMonthClasses = useMemo(() => {
    if (!filteredClasses) return []

    return filteredClasses.filter(classItem => {
      const date = new Date(classItem.date)
      return date.getMonth() === currentMonth.getMonth() && date.getFullYear() === currentMonth.getFullYear()
    })
  }, [filteredClasses, currentMonth])

  const goToPrevMonth = () => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev)
      newDate.setMonth(prev.getMonth() - 1)
      return newDate
    })
  }

  const goToNextMonth = () => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev)
      newDate.setMonth(prev.getMonth() + 1)
      return newDate
    })
  }

  if (!classes || classes.length === 0) return null

  return (
    <div className={classnames(styles.ClassCalendar, className)}>
      <div className={styles.inner}>
        <div className={styles.header}>
          <FormElementSelect
            className={styles.filter}
            element="select"
            name="filter"
            items={[
              { label: 'All Events', value: 'all' },
              { label: 'Beginner Classes', value: 'beginner' },
              { label: 'Intermediate Classes', value: 'intermediate' },
              { label: 'Advanced Classes', value: 'advanced' },
            ]}
            currentValue={filter as string}
            onChange={value => {
              setFilter(value)
            }}
          />

          <h2 className={styles.title}>{title}</h2>
          {!isMobile && (
            <div className={styles.viewToggle}>
              <Button
                label="Calendar"
                icon="calendar"
                style="bare"
                onClick={() => setViewMode('calendar')}
                className={classnames(styles.viewToggle__button, {
                  [styles.viewToggle__button_active]: viewMode === 'calendar',
                })}
              />
              <Button
                label="List"
                icon="bullets"
                style="bare"
                iconPosition="left"
                onClick={() => setViewMode('list')}
                className={classnames(styles.viewToggle__button, {
                  [styles.viewToggle__button_active]: viewMode === 'list',
                })}
              />
            </div>
          )}
        </div>

        <NavigationBar
          currentMonth={currentMonth}
          goToPrevMonth={goToPrevMonth}
          goToNextMonth={goToNextMonth}
        />

        {viewMode === 'calendar' ? (
          <div className={styles.calendar}>
            <div className={styles.calendar__weekdays}>
              <div className={styles.calendar__weekday}>SUN</div>
              <div className={styles.calendar__weekday}>MON</div>
              <div className={styles.calendar__weekday}>TUE</div>
              <div className={styles.calendar__weekday}>WED</div>
              <div className={styles.calendar__weekday}>THU</div>
              <div className={styles.calendar__weekday}>FRI</div>
              <div className={styles.calendar__weekday}>SAT</div>
            </div>
            <div className={styles.calendar__grid}>
              {allDays.map((day, index) => {
                const dateKey = day.toISOString().split('T')[0]
                const dayClasses = classesByDate[dateKey] || []
                const isCurrentMonth = day.getMonth() === currentMonth.getMonth()

                return (
                  <div
                    key={index}
                    className={classnames(styles.calendar__day, {
                      [styles.calendar__day_otherMonth]: !isCurrentMonth,
                      [styles.calendar__day_hasEvents]: dayClasses.length > 0,
                    })}
                  >
                    <div className={styles.calendar__dayNumber}>{day.getDate()}</div>
                    <div className={styles.calendar__events}>
                      {dayClasses.map((classItem, i) => (
                        <div
                          key={i}
                          className={styles.calendar__event}
                        >
                          {classItem.image && (
                            <CalendarCard
                              image={classItem.image}
                              eyebrow={classItem.instructor}
                              title={classItem.title}
                              date={classItem.date}
                              price={classItem.price}
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        ) : (
          <div className={styles.list}>
            {currentMonthClasses.length > 0 ? (
              currentMonthClasses.map((classItem, index) => (
                <div
                  key={index}
                  className={styles.list__item}
                >
                  {classItem.image && (
                    <CalendarCard
                      image={classItem.image}
                      eyebrow={classItem.instructor}
                      title={classItem.title}
                      date={classItem.date}
                      price={classItem.price}
                      orientation="landscape"
                    />
                  )}
                </div>
              ))
            ) : (
              <div className={styles.list__empty}>
                {filter !== 'all'
                  ? `No ${filter} classes scheduled for this month`
                  : 'No classes scheduled for this month'}
              </div>
            )}
          </div>
        )}

        <NavigationBar
          position="bottom"
          currentMonth={currentMonth}
          goToPrevMonth={goToPrevMonth}
          goToNextMonth={goToNextMonth}
        />
      </div>
    </div>
  )
}

const NavigationBar = ({
  position = 'top',
  currentMonth,
  goToPrevMonth,
  goToNextMonth,
}: {
  position?: 'top' | 'bottom'
  currentMonth: Date
  goToPrevMonth: () => void
  goToNextMonth: () => void
}) => {
  return (
    <div
      data-class-calendar-navigation-position={position}
      className={styles.navigation}
    >
      <Button
        label="PREV"
        icon="arrowLeft"
        style="bare"
        onClick={goToPrevMonth}
        className={styles.navigation__button}
      />
      <h3 className={styles.navigation__month}>
        {currentMonth.toLocaleString('default', { month: 'long', year: 'numeric' }).toUpperCase()}
      </h3>
      <Button
        label="NEXT"
        icon="arrowRight"
        style="bare"
        iconPosition="right"
        onClick={goToNextMonth}
        className={styles.navigation__button}
      />
    </div>
  )
}

ClassCalendar.displayName = 'ClassCalendar'

export default ClassCalendar
