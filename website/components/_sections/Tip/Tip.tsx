'use client'

import classnames from 'classnames'
import styles from './Tip.module.scss'
import TipComponent from '@/components/Tip/Tip'

const TipSection = ({ className, content }: SanityTip) => {
  if (!content) return null

  return (
    <div className={classnames(styles.TipSection, className)}>
      <div className={styles.inner}>
        <TipComponent {...content} />
      </div>
    </div>
  )
}

TipSection.displayName = 'TipSection'

export default TipSection
