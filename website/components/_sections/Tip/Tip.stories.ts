import type { Meta, StoryObj } from '@storybook/react'
import TipSection from './Tip'
import RICH_TEXT_STUB_DATA from '@/components/RichTextV2/RichTextV2.stub'

const meta = {
  title: 'Sections/TipSection',
  component: TipSection,
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof TipSection>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    _type: 'tip',
    _id: 'tip',
    content: {
      title: 'Tip',
      description:
        'Don\'t use coconut water that contains added sweetener. A small amount of natural sugar is normal, but check the ingredients listed on the label for added sugar.',
      alignment: 'left',
      backgroundColor: 'white',
      textSize: 'md',
    },
  },
}

export const YellowBackground: Story = {
  args: {
    _type: 'tip',
    _id: 'tip',
    content: {
      title: 'Tip with Yellow Background',
      description:
        'Don\'t use coconut water that contains added sweetener. A small amount of natural sugar is normal, but check the ingredients listed on the label for added sugar.',
      alignment: 'left',
      backgroundColor: 'yellow',
      textSize: 'md',
    },
  },
}

export const LargeText: Story = {
  args: {
    _type: 'tip',
    _id: 'tip',
    content: {
      title: 'Tip with Large Text',
      description:
        'Don\'t use coconut water that contains added sweetener. A small amount of natural sugar is normal, but check the ingredients listed on the label for added sugar.',
      alignment: 'left',
      backgroundColor: 'white',
      textSize: 'lg',
    },
  },
}

export const RichText: Story = {
  args: {
    _type: 'tip',
    _id: 'tip',
    content: {
      title: 'Tip with Rich Text',
      description: RICH_TEXT_STUB_DATA.EXAMPLE_1_P_WITH_LINK,
      alignment: 'center',
      backgroundColor: 'yellow',
      textSize: 'lg',
    },
  },
}
