'use client'

import classnames from 'classnames'
import styles from './AuthorDateDisplay.module.scss'
import { formatDate } from '@/lib/utils/date'
import { useMemo } from 'react'

const AuthorDateDisplay = ({ className, authors, date }: AuthorDateDisplayProps) => {
  const text = useMemo(() => {
    let _text = 'By'
    authors.forEach((author, index) => {
      if (typeof author === 'string') {
        _text += ` ${author}`
      } else {
        _text += ` ${author.name}`
      }
      if (index < authors.length - 1) {
        _text += '&'
      }
    })

    return _text
  }, [authors])

  return (
    <p className={classnames(styles.AuthorDateDisplay, className)}>
      <span
        className={styles.text}
        data-authors-text
      >
        {text}
      </span>
      {date && (
        <span
          data-date
          className={styles.date}
        >
          {formatDate(date)}
        </span>
      )}
    </p>
  )
}

AuthorDateDisplay.displayName = 'AuthorDateDisplay'

export default AuthorDateDisplay
