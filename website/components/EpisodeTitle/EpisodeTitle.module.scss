.EpisodeTitle {
  display: flex;
  align-items: center;
  gap: px(8);
}

.icon {
  @include flex-center;
  color: var(--loud-warm);

  svg {
    width: px(16);
  }
}

.content {
  display: flex;
  align-items: center;
  gap: px(4);
  flex-wrap: wrap;
}

.episode {
  color: var(--loud-warm);

  [data-episode-title-size='sm'] & {
    @include font-episode-sm;
  }

  [data-episode-title-size='md'] & {
    @include font-episode;
  }
}

.date {
  color: var(--slate);

  [data-episode-title-size='sm'] & {
    @include font-date-sm;
  }

  [data-episode-title-size='md'] & {
    @include font-date-md;
  }
}
