'use client'

import classnames from 'classnames'
import styles from './EpisodeTitle.module.scss'
import Icon from '@/components/Icon/Icon'
import { formatDate } from '@/lib/utils/date'

const EpisodeTitle = ({ className, season, episode, size = 'md', date, hasIcon = true }: EpisodeTitleProps) => {
  const formattedDate = date ? formatDate(date, { month: 'short' }) : null

  return (
    <div
      className={classnames(styles.EpisodeTitle, className)}
      data-episode-title-size={size}
    >
      {hasIcon && (
        <div className={styles.icon}>
          <Icon name="playCircle" />
        </div>
      )}
      <div className={styles.content}>
        {(season || episode) && (
          <span className={styles.episode}>
            {season && `Season ${season}`}
            {season && episode && ', '}
            {episode && `Ep. ${episode}`}
          </span>
        )}
        {formattedDate && <span className={styles.date}>{formattedDate}</span>}
      </div>
    </div>
  )
}

EpisodeTitle.displayName = 'EpisodeTitle'

export default EpisodeTitle
