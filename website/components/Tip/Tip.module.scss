.Tip {
  padding: px(32);
  margin: 0 auto;
  width: 100%;

  &[data-tip-alignment='left'] {
    text-align: left;
  }

  &[data-tip-alignment='center'] {
    text-align: center;
  }

  &[data-tip-background-color='white'] {
    background: var(--white);
    border: 1px solid rgba($navy-dark, 0.2);
    box-shadow: 0 px(2) px(10) 0 rgba($black, 0.1);
  }

  &[data-tip-background-color='yellow'] {
    background: var(--highlight-light);
  }
}

.headerRow {
  @include flex-center;
  margin-bottom: px(32);
  gap: px(16);
}

.title {
  @include font-heading-eyebrow-lg;
  color: var(--navy-dark);
}

.divider {
  flex: 1 1 0%;
  border: none;
  border-top: 1px solid rgba($navy-dark, 0.2);
  height: 1px;
  background: none;
}

.description {
  @include rich-text-base-styling;

  [data-p] {
    @include font-text-lg;
    color: var(--navy-dark);
  }
}
