import type { Meta, StoryObj } from '@storybook/react'
import Tip from './Tip'
import RICH_TEXT_STUB_DATA from '../RichTextV2/RichTextV2.stub'

const meta = {
  title: 'Components/Tip',
  component: Tip,
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof Tip>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    title: 'Tip',
    description:
      'Don\'t use coconut water that contains added sweetener. A small amount of natural sugar is normal, but check the ingredients listed on the label for added sugar. Be sure to use dried wide-flake coconut, not dried coconut in small, fine shreds. Also make sure it is unsweetened.',
    alignment: 'left',
  },
}

export const Centered: Story = {
  args: {
    title: 'Tip',
    description:
      'Don\'t use coconut water that contains added sweetener. A small amount of natural sugar is normal, but check the ingredients listed on the label for added sugar. Be sure to use dried wide-flake coconut, not dried coconut in small, fine shreds. Also make sure it is unsweetened.',
    alignment: 'center',
  },
}

export const RichText: Story = {
  args: {
    title: 'Tip',
    description: RICH_TEXT_STUB_DATA.EXAMPLE_1_P_WITH_LINK,
    alignment: 'left',
  },
}

export const RichTextCentered: Story = {
  args: {
    title: 'Tip',
    description: RICH_TEXT_STUB_DATA.EXAMPLE_1_P_WITH_LINK,
    alignment: 'center',
  },
}

export const YellowBackground: Story = {
  args: {
    title: 'Tip with Yellow Background',
    description:
      'Don\'t use coconut water that contains added sweetener. A small amount of natural sugar is normal, but check the ingredients listed on the label for added sugar.',
    alignment: 'left',
    backgroundColor: 'yellow',
  },
}

export const LargeText: Story = {
  args: {
    title: 'Tip with Large Text',
    description:
      'Don\'t use coconut water that contains added sweetener. A small amount of natural sugar is normal, but check the ingredients listed on the label for added sugar.',
    alignment: 'left',
    textSize: 'lg',
  },
}

export const YellowBackgroundLargeText: Story = {
  args: {
    title: 'Tip with Yellow Background and Large Text',
    description:
      'Don\'t use coconut water that contains added sweetener. A small amount of natural sugar is normal, but check the ingredients listed on the label for added sugar.',
    alignment: 'center',
    backgroundColor: 'yellow',
    textSize: 'lg',
  },
}
