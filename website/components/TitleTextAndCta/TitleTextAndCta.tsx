import classnames from 'classnames'
import styles from './TitleTextAndCta.module.scss'
import Button from '../Button/Button'

const TitleTextAndCta = ({ title, description, cta, className }: TitleTextAndCtaProps) => {
  if (!title) return null

  return (
    <div className={classnames(styles.container, className)}>
      <div className={styles.title}>{title}</div>
      {description && <p className={styles.description}>{description}</p>}
      {cta && (
        <Button
          link={cta}
          style="primary"
          icon="caretRight"
          iconPosition="right"
        />
      )}
    </div>
  )
}

export default TitleTextAndCta
