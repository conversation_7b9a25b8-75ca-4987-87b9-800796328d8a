import type { Meta, StoryObj } from '@storybook/react'
import TitleTextAndCta from './TitleTextAndCta'

const meta: Meta<typeof TitleTextAndCta> = {
  title: 'Components/TitleTextAndCta',
  component: TitleTextAndCta,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
}

export default meta
type Story = StoryObj<typeof TitleTextAndCta>

export const Default: Story = {
  args: {
    title: 'Not a subscriber?',
    description: 'Get six issues of Milk Street Magazine delivered to your door.',
    cta: {
      label: 'Subscribe',
      linkType: 'internal',
      link: {
        _type: 'page',
        slug: 'about',
      },
    },
  },
}

export const WithoutDescription: Story = {
  args: {
    title: 'Not a subscriber?',
    cta: {
      label: 'Subscribe',
      linkType: 'internal',
      link: {
        _type: 'page',
        slug: 'about',
      },
    },
  },
}

export const WithoutCta: Story = {
  args: {
    title: 'Not a subscriber?',
    description: 'Get six issues of Milk Street Magazine delivered to your door.',
  },
}

export const TitleOnly: Story = {
  args: {
    title: 'Not a subscriber?',
  },
}

export const WithExternalLink: Story = {
  args: {
    title: 'Not a subscriber?',
    description: 'Get six issues of Milk Street Magazine delivered to your door.',
    cta: {
      label: 'Subscribe',
      linkType: 'external',
      link: 'https://www.177milkstreet.com',
    },
  },
}
