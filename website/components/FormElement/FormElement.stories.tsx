import type { Meta, StoryObj } from '@storybook/react'
import FormElement from './FormElement'
import { useRef } from 'react'
import { FormElementImperativeHandle } from '@/types/components/FormElement'
import { EMAIL_REGEX } from '@/data'

// eslint-disable-next-line
const TestValidComponent = (args: any) => {
  const $ref = useRef<FormElementImperativeHandle | null>(null)

  return (
    <div>
      <button
        onClick={() => {
          if ($ref) $ref.current?.test()
        }}
      >
        Test if valid
      </button>
      <FormElement
        ref={$ref}
        {...args}
      />
    </div>
  )
}

const meta = {
  title: 'Components/FormElement',
  component: FormElement,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    element: {
      options: ['input', 'select', 'textarea'],
      control: { type: 'radio' },
    },
    type: {
      options: ['text', 'email', 'password', 'checkbox'],
      control: { type: 'radio' },
    },
    theme: {
      options: ['white', 'gray'],
      control: { type: 'radio' },
    },
    buttonSide: {
      options: ['left', 'right'],
      control: { type: 'radio' },
    },
  },
} satisfies Meta<typeof FormElement>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    element: 'input',
    label: 'Label',
    name: 'name',
    placeholder: 'Placeholder',
    type: 'text',
  },
}

export const Checkbox: Story = {
  args: {
    element: 'input',
    label: 'Label',
    name: 'name',
    type: 'checkbox',
  },
}

export const Select: Story = {
  args: {
    element: 'select',
    label: 'Label',
    name: 'name',
    placeholder: 'Placeholder',
    items: [
      {
        label: 'Item 1',
        value: 'item1',
      },
      {
        label: 'Item 2',
        value: 'item2',
      },
    ],
  },
}

export const TextArea: Story = {
  args: {
    element: 'textarea',
    label: 'Label',
    name: 'name',
    placeholder: 'Placeholder',
  },
}

export const RoundedCorners: Story = {
  args: {
    element: 'input',
    label: 'Label',
    name: 'name',
    placeholder: 'Placeholder',
    type: 'text',
    isRounded: true,
  },
}

export const WithInitialValue: Story = {
  args: {
    element: 'input',
    label: 'Label',
    name: 'name',
    placeholder: 'Placeholder',
    type: 'text',
    initialValue: 'Test',
  },
}

export const WithButton: Story = {
  args: {
    element: 'input',
    label: 'With Button',
    name: 'name',
    placeholder: 'Placeholder',
    type: 'text',
    buttonIcon: 'arrowRight',
    buttonOnClick: ($ref: FormElementImperativeHandle) => {
      // eslint-disable-next-line
      console.log($ref)
    },
  },
}

export const ChangeButtonSide: Story = {
  args: {
    element: 'input',
    label: 'Changed Button Side',
    name: 'name',
    placeholder: 'Placeholder',
    buttonSide: 'left',
    type: 'text',
    buttonIcon: 'magnifyingGlass',
    buttonOnClick: ($ref: FormElementImperativeHandle) => {
      // eslint-disable-next-line
      console.log($ref)
    },
  },
}

export const WhiteTheme: Story = {
  args: {
    element: 'input',
    label: 'Theme',
    name: 'name',
    placeholder: 'Placeholder',
    type: 'text',
    theme: 'white',
  },
}

export const IsInvalid: Story = {
  args: {
    element: 'input',
    label: 'Label (Required)',
    name: 'name',
    placeholder: 'Placeholder',
    type: 'text',
    required: true,
  },
  render: TestValidComponent,
}

export const TestCustomExpression: Story = {
  args: {
    element: 'textarea',
    label: 'Email',
    name: 'name',
    placeholder: 'Enter Email',
    required: true,
    regexp: EMAIL_REGEX,
  },
  render: TestValidComponent,
}
