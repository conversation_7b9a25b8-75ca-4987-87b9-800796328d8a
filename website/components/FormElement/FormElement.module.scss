@mixin form-default-theme {
  outline-width: 1px;
  outline-style: solid;
  color: var(--navy-dark);
}

@mixin form-grey-theme {
  outline-color: var(--gray-quiet);
  background-color: var(--gray-quiet);

  &:focus {
    outline-width: 2px;
    outline-color: rgba($navy-dark, 0.5);
  }
}

@mixin form-white-theme {
  outline-color: rgba($navy-dark, 0.2);
  background-color: var(--white);

  &:focus {
    outline-width: 2px;
    outline-color: var(--slate);
  }
}

.container {
  position: relative;

  --element-height: #{px(48)};
  --element-base-padding: #{px(12)};
  --checkbox-size: #{px(24)};
}

.labelContainer {
  display: block;
  margin-bottom: px(6);
}

.label,
.radioLabel {
  @include font-text-base;
  font-weight: 700;
  color: var(--navy-dark);
  display: inline-block;
  cursor: pointer;

  .isCheckbox & {
    padding-left: px(28);
    margin-bottom: 0;
  }
}

.required {
  display: inline-block;
  margin-left: px(4);
  color: var(--loud-warm);
}

.elementWrapper {
  .container:not(.isCheckbox) & {
    position: relative;
  }
}

.input,
.select,
.textarea {
  @include reset-button;
  @include font-text-base;
  @include form-default-theme;
  cursor: initial;
  box-sizing: border-box;
  width: 100%;
  display: block;

  .isRounded & {
    border-radius: px(100);
  }

  &::placeholder,
  &[data-select-value=''] {
    color: var(--slate);
    font-style: italic;
  }

  .hasButton[data-form-button-side='left'] & {
    padding-left: calc(var(--element-height) + px(2));
  }

  .hasButton[data-form-button-side='right'] & {
    padding-right: calc(var(--element-height) + px(20));
  }

  [data-form-theme='gray'] & {
    @include form-grey-theme;
  }

  [data-form-theme='white'] & {
    @include form-white-theme;
  }

  .isInvalid & {
    outline-color: var(--loud-warm);
  }
}

.input,
.select {
  height: var(--element-height);
  padding: 0 var(--element-base-padding);
}

.checkbox {
  @include flex-center;
  @include form-default-theme;
  @include form-white-theme;
  width: var(--checkbox-size);
  height: var(--checkbox-size);
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;

  .isFocused & {
    outline-color: rgba($navy-dark, 0.8);
  }
}

.checkbox__icon {
  width: 100%;
}

.textarea {
  min-height: px(120);
  padding: var(--element-base-padding);
}

.input[type='checkbox'] {
  width: 1px;
  height: 1px;
  position: absolute;
  opacity: 0.001;
  top: 0;
  left: 0;
}

.selectContainer {
  position: relative;
  display: block;
}

.selectIcon {
  position: absolute;
  right: px(12);
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  width: px(24);
}

.button {
  @include reset-button;
  @include flex-center;
  position: absolute;
  top: 50%;
  right: px(12);
  transform: translateY(-50%);
  height: var(--element-height);
  width: var(--element-height);

  [data-form-button-side='left'] & {
    right: auto;
    left: 0;
  }
}

.button__icon {
  width: px(24);
}

.radioGroup {
  display: flex;
  flex-direction: column;
  gap: px(12);
}

.radio {
  display: flex;
  cursor: pointer;
}

.radioInput {
  @include reset-button;
  margin: 0;
}

.radioCircle {
  @include flex-center;
  position: relative;
  min-width: var(--checkbox-size);
  min-height: var(--checkbox-size);
  max-width: var(--checkbox-size);
  max-height: var(--checkbox-size);
  border-radius: 50%;
  border: 1px solid var(--navy-dark);
  margin-right: px(5);
}

.radioCircleInner {
  @include position-100(absolute);
  transform: scale(0.7);
  background-color: var(--navy-light);
  display: none;
  border-radius: 50%;

  .isActive & {
    display: block;
  }
}
