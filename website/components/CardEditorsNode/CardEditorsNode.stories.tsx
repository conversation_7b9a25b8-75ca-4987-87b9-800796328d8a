import type { Meta, StoryObj } from '@storybook/react'
import CardEditorsNode from './CardEditorsNode'
import { SANITY_IMAGE_STUB_SOURCE } from '@/components/SanityImage/SanityImage.stub'
import { STUB_LINK } from '@/components/Link/Link.stub'

const Container = ({ children }: { children: React.ReactNode }) => <div style={{ maxWidth: '640px' }}>{children}</div>

const meta = {
  title: 'Cards/CardEditorsNode',
  component: CardEditorsNode,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: 'radio',
      options: ['sm', 'md'],
      description: 'Size of the card',
    },
    image: {
      description: 'Sanity image object',
    },
    title: {
      control: 'text',
      description: 'Title of the card',
    },
    eyebrow: {
      control: 'text',
      description: 'Eyebrow text (optional)',
    },
    description: {
      control: 'text',
      description: 'Description text (optional)',
    },
  },
  decorators: [
    Story => (
      <Container>
        <Story />
      </Container>
    ),
  ],
} satisfies Meta<typeof CardEditorsNode>

export default meta

type Story = StoryObj<typeof meta>

const BASIC_PROPS = {
  image: SANITY_IMAGE_STUB_SOURCE.PERSON,
  title: 'How to Cook with Lackluster Tomatoes',
  eyebrow: 'CHRISTOPHER KIMBALL',
  description: 'You also can roast them, bake them, simmer them or stir-fry them to drive off...',
}

export const Medium: Story = {
  args: {
    ...BASIC_PROPS,
    size: 'md',
  },
}

export const Small: Story = {
  args: {
    ...BASIC_PROPS,
    size: 'sm',
  },
}

export const WithoutEyebrow: Story = {
  args: {
    ...BASIC_PROPS,
    eyebrow: undefined,
    size: 'md',
  },
}

export const WithoutDescription: Story = {
  args: {
    ...BASIC_PROPS,
    description: undefined,
    size: 'md',
  },
}

export const ShortDescription: Story = {
  args: {
    ...BASIC_PROPS,
    description: 'A short description.',
    size: 'md',
  },
}

export const LongDescription: Story = {
  args: {
    ...BASIC_PROPS,
    description:
      'This is a much longer description that will definitely exceed the character threshold and show the Read More link. You also can roast them, bake them, simmer them or stir-fry them to drive off moisture and concentrate their flavor.',
    size: 'md',
  },
}

export const WithLink: Story = {
  args: {
    ...BASIC_PROPS,
    size: 'md',
    link: STUB_LINK.TEST_WITH_LABEL as SanityLink,
  },
}

export const SmallWithLink: Story = {
  args: {
    ...BASIC_PROPS,
    size: 'sm',
    link: STUB_LINK.TEST_WITH_LABEL as SanityLink,
  },
}
