import type { Meta, StoryObj } from '@storybook/react'
import StarsRating from './StarsRating'

const meta: Meta<typeof StarsRating> = {
  title: 'Components/StarsRating',
  component: StarsRating,
  tags: ['autodocs'],
  argTypes: {
    percent: { control: { type: 'range', min: 0, max: 1, step: 0.01 } },
    isSelectable: { control: 'boolean' },
    className: { control: 'text' },
    count: { control: 'number' },
    size: {
      control: { type: 'radio' },
      options: ['sm', 'md', 'lg'],
    },
  },
}

export default meta
type Story = StoryObj<typeof StarsRating>

export const Default: Story = {
  args: {
    percent: 0.8,
    size: 'sm',
    isSelectable: false,
  },
}

export const Medium: Story = {
  args: {
    percent: 0.6,
    size: 'md',
    isSelectable: false,
  },
}

export const Large: Story = {
  args: {
    percent: 0.4,
    size: 'lg',
    isSelectable: false,
  },
}

export const WithCount: Story = {
  args: {
    percent: 0.7,
    size: 'md',
    count: 42,
    isSelectable: false,
  },
}

export const Selectable: Story = {
  args: {
    percent: 0.5,
    size: 'md',
    isSelectable: true,
  },
}
