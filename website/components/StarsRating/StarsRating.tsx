'use client'

import { useState } from 'react'
import classnames from 'classnames'
import styles from './StarsRating.module.scss'
import Icon from '../Icon/Icon'

const STAR_COUNT = 5

const StarsRating = ({ percent, isSelectable = false, count, size = 'sm', className }: StarsRatingProps) => {
  const [hovered, setHovered] = useState<number | null>(null)
  const [selected, setSelected] = useState<number | null>(null)

  const displayPercent =
    isSelectable && hovered !== null
      ? hovered / STAR_COUNT
      : isSelectable && selected !== null
        ? selected / STAR_COUNT
        : percent

  const ariaValue = Math.round(displayPercent * STAR_COUNT * 10) / 10

  const handleMouseMove = (i: number) => isSelectable && setHovered(i + 1)
  const handleMouseLeave = () => isSelectable && setHovered(null)
  const handleClick = (i: number) => isSelectable && setSelected(i + 1)

  return (
    <div
      className={classnames(styles.StarsRating, className, {
        [styles.selectable]: isSelectable,
      })}
      data-stars-rating-size={size}
      aria-label={`Rated ${ariaValue} out of ${STAR_COUNT} stars`}
      role={isSelectable ? 'slider' : 'img'}
      aria-valuenow={isSelectable ? selected || Math.round(percent * STAR_COUNT) : undefined}
      aria-valuemax={isSelectable ? STAR_COUNT : undefined}
      aria-valuemin={isSelectable ? 0 : undefined}
      tabIndex={isSelectable ? 0 : -1}
      onMouseLeave={handleMouseLeave}
    >
      <div className={styles.starsWrapper}>
        <span className={styles.starsBg}>
          {Array.from({ length: STAR_COUNT }).map((_, i) => (
            <div
              key={`empty-${i}`}
              onMouseMove={isSelectable ? () => handleMouseMove(i) : undefined}
              onClick={isSelectable ? () => handleClick(i) : undefined}
              style={{ cursor: isSelectable ? 'pointer' : 'default' }}
            >
              <Icon
                name="star"
                className={classnames(styles.star, styles.empty)}
              />
            </div>
          ))}
        </span>
        <span
          className={styles.starsFg}
          style={{
            clipPath: `inset(0 ${(1 - displayPercent) * 100}% 0 0)`,
          }}
        >
          {Array.from({ length: STAR_COUNT }).map((_, i) => (
            <div key={`filled-${i}`}>
              <Icon
                name="star"
                className={classnames(styles.star, styles.filled)}
              />
            </div>
          ))}
        </span>
      </div>
      {count && (
        <div
          className={styles.count}
          aria-label={`based on ${count} reviews`}
        >
          {count}
        </div>
      )}
    </div>
  )
}

StarsRating.displayName = 'StarsRating'

export default StarsRating
