/* stylelint-disable-next-line block-no-empty */
.StarsRating {
  position: relative;
  display: flex;
  color: var(--navy-dark);
  align-items: center;
}

.starsWrapper {
  position: relative;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-areas: 'stars stars stars stars stars';

  [data-stars-rating-size='sm'] & {
    width: px(68);
  }

  [data-stars-rating-size='md'] & {
    width: px(128);
  }

  [data-stars-rating-size='lg'] & {
    width: px(232);
  }
}

.starsBg,
.starsFg {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-area: stars;
}

.starsBg {
  z-index: 1;
  pointer-events: auto;
}

.starsFg {
  z-index: 2;
  pointer-events: none;
  overflow: hidden;
}

.star {
  transition: color $transition-short;
  @include flex-center;

  &.filled {
    color: var(--navy-dark);
  }

  &.empty {
    color: rgb($black, 0.2);
  }
}

.count {
  @include font-stars-rating-sm;
  color: var(--navy-dark);
  align-self: center;
  margin-left: px(8);

  [data-stars-rating-size='md'] & {
    @include font-stars-rating-md;
    margin-left: px(12);
  }

  [data-stars-rating-size='lg'] & {
    @include font-stars-rating-lg;
    margin-left: px(16);
  }
}

.selectable {
  cursor: pointer;

  .starsBg span:focus ~ span .star {
    opacity: 0.5;
  }
}
