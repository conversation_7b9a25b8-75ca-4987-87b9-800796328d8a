import type { Meta, StoryObj } from '@storybook/react'
import LogoCarousel from './LogoCarousel'
import { SANITY_IMAGE_STUB_SOURCE } from '../SanityImage/SanityImage.stub'
import { SanityImage } from '@/types/sanity/SanityImage'

const ITEMS = [
  {
    image: SANITY_IMAGE_STUB_SOURCE.LOGO as SanityImage,
    desktopWidth: 170,
    mobileWidth: 200,
  },
  {
    image: SANITY_IMAGE_STUB_SOURCE.LOGO as SanityImage,
    desktopWidth: 80,
  },
  {
    image: SANITY_IMAGE_STUB_SOURCE.LOGO as SanityImage,
    mobileWidth: 60,
    desktopWidth: 30,
  },
  {
    image: SANITY_IMAGE_STUB_SOURCE.LOGO as SanityImage,
    desktopWidth: 200,
  },
  {
    image: SANITY_IMAGE_STUB_SOURCE.LOGO as SanityImage,
    desktopWidth: 200,
    mobileWidth: 60,
  },
  {
    image: SANITY_IMAGE_STUB_SOURCE.LOGO as SanityImage,
    desktopWidth: 60,
  },
]

const meta = {
  title: 'Carousels/LogoCarousel',
  component: LogoCarousel,
  parameters: {
    layout: 'fullscreen',
  },
} satisfies Meta<typeof LogoCarousel>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    items: ITEMS,
  },
}
