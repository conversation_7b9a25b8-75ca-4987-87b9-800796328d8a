.LogoCarousel {
  position: relative;
  overflow: hidden;
  width: calc(100% + calc(var(--page-gutter) * 2));
  margin-left: calc(var(--page-gutter) * -1);
}

.inner {
  margin: 0 auto;
  width: 100%;
  padding: 0 var(--page-gutter);
}

.carousel {
  position: relative;
  overflow: visible;
}

.carouselInner {
  width: 100%;
  align-items: center;
}

.carouselItem {
  width: fit-content !important;
}

.carouselItemInner {
  position: relative;
  aspect-ratio: var(--aspect-ratio);
  width: var(--mobile-width);

  @include bp(tablet) {
    width: var(--desktop-width);
  }
}

.image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
