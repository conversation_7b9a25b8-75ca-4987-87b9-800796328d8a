'use client'

import classnames from 'classnames'
import styles from './LogoCarousel.module.scss'
import { LogoCarouselProps } from '@/types/components/LogoCarouselProps'
import Swiper from 'swiper'
import { SwiperOptions } from 'swiper/types'
import { CSSProperties, useEffect, useRef } from 'react'
import useWindowResize from '@/hooks/use-window-resize'
import 'swiper/css'
import useBreakpoint from '@/hooks/use-breakpoint'
import SanityImage from '../SanityImage/SanityImage'
import { Autoplay } from 'swiper/modules'

// eslint-disable-next-line react-hooks/rules-of-hooks
Swiper.use([Autoplay])

const PADDING = {
  desktop: 80,
  mobile: 28,
}

const LogoCarousel = ({ className, items }: LogoCarouselProps) => {
  const carouselInstance = useRef<Swiper | null>(null)
  const $carousel = useRef<HTMLDivElement | null>(null)
  const resizeKey = useWindowResize()
  const { isMobile } = useBreakpoint()
  const $swiperWrapper = useRef<HTMLUListElement | null>(null)
  const $itemInners = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    if (!$carousel.current || !$swiperWrapper.current) return

    if (carouselInstance.current) {
      carouselInstance.current.destroy()
    }

    const padding = isMobile ? PADDING.mobile : PADDING.desktop
    let innerWidth = 0
    $itemInners.current.forEach((item, i) => {
      if (item) {
        innerWidth += item.offsetWidth
        if (i !== $itemInners.current.length - 1) {
          innerWidth += padding
        }
      }
    })

    const contentIsSmaller = innerWidth < $swiperWrapper.current.offsetWidth

    const settings: SwiperOptions = {
      slidesPerView: 'auto',
      loop: false,
      spaceBetween: isMobile ? 28 : 80,
      autoplay: {
        delay: 2000,
        disableOnInteraction: true,
      },
      centeredSlides: contentIsSmaller ? true : false,
    }

    carouselInstance.current = new Swiper($carousel.current, settings)

    return () => {
      if (carouselInstance.current) {
        carouselInstance.current.destroy()
      }
    }
  }, [resizeKey, isMobile])

  if (!items?.length) return null

  return (
    <div className={classnames(styles.LogoCarousel, className)}>
      <div className={styles.inner}>
        <div
          className={styles.carousel}
          ref={$carousel}
        >
          <ul
            className={classnames(styles.carouselInner, 'swiper-wrapper')}
            ref={$swiperWrapper}
          >
            {items.map((item, i) => (
              <li
                className={classnames(styles.carouselItem, 'swiper-slide')}
                key={i}
              >
                <div
                  className={styles.carouselItemInner}
                  ref={ref => {
                    $itemInners.current[i] = ref
                  }}
                  style={
                    {
                      '--desktop-width': `${item.desktopWidth || 100}px`,
                      '--mobile-width': `${item.mobileWidth || 100}px`,
                      '--aspect-ratio': `${item.image.asset.width}/${item.image.asset.height}`,
                    } as CSSProperties
                  }
                >
                  <SanityImage
                    source={item.image}
                    className={styles.image}
                    columns={2}
                  />
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  )
}

LogoCarousel.displayName = 'LogoCarousel'

export default LogoCarousel
