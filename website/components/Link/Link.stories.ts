import type { <PERSON>a, StoryObj } from '@storybook/react'
import Link from './Link'
import { DOC_TYPES, HOME_SLUG } from '@/data'

const meta = {
  title: 'Components/Link',
  component: Link,
  parameters: {
    layout: 'centered',
  },
  args: {
    link: {
      label: 'Internal Link??',
      linkType: 'internal',
      link: {
        slug: HOME_SLUG,
        _type: DOC_TYPES.PAGE,
      },
    },
  },
} satisfies Meta<typeof Link>

export default meta

type Story = StoryObj<typeof meta>

export const InternalLink: Story = {
  args: {
    link: {
      label: 'Internal Link',
      linkType: 'internal',
      link: {
        slug: HOME_SLUG,
        _type: DOC_TYPES.PAGE,
      },
    },
  },
}

export const ExternalLink: Story = {
  args: {
    link: {
      label: 'External Link',
      linkType: 'external',
      link: 'https://google.com',
    },
  },
}
