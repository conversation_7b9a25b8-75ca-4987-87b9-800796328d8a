'use client'

import { forwardRef, useEffect } from 'react'
import NextLink from 'next/link'
import classnames from 'classnames'
import styles from './Link.module.scss'
import useCurrentPage from '@/hooks/use-current-page'
import { getUrlFromPageData } from '@/lib/utils'

const Link = forwardRef<HTMLAnchorElement, LinkProps>(
  (
    {
      className,
      children,
      link,
      onMouseEnter,
      onMouseLeave,
      linkOnly,
      ariaLabel,
      onClick,
      onFocus,
      disableOpenNewTab = false,
      activeClass,
      tabIndexHidden,
      queryParamString,
      getIsSamePage,
    },
    ref,
  ) => {
    const { linkType, label, link: url, hash } = link
    const { currentPath } = useCurrentPage()
    const urlObject = url as SanityLinkInternal
    const slug = typeof url === 'object' ? url?.slug : ''
    const path = `${getUrlFromPageData(urlObject?._type, slug)}`

    useEffect(() => {
      if (getIsSamePage) {
        getIsSamePage(path === currentPath)
      }
    }, [path, currentPath, getIsSamePage])

    if (linkType === 'disabled') {
      return null
    }

    if (typeof url !== 'string' && linkType === 'external') {
      return null
    }

    if (typeof url !== 'object' && linkType === 'internal') {
      return null
    }

    if (linkType === 'external' || linkType === 'file') {
      return (
        <a
          ref={ref}
          aria-label={ariaLabel}
          data-link={true}
          {...(ariaLabel && !label && !children && { name: ariaLabel })}
          {...(tabIndexHidden && { tabIndex: -1 })}
          href={typeof url === 'string' ? url : ''}
          className={classnames(className)}
          target={disableOpenNewTab ? '_self' : '_blank'}
          rel="noreferrer"
          onFocus={e => {
            if (onFocus) onFocus(e)
          }}
          onMouseEnter={() => {
            if (onMouseEnter) onMouseEnter()
          }}
          onMouseLeave={() => {
            if (onMouseLeave) onMouseLeave()
          }}
          onClick={e => {
            if (onClick) onClick(e)
          }}
        >
          {label && !children && !linkOnly && <span>{label}</span>}
          {children && children}
        </a>
      )
    } else if (linkType === 'internal' || linkType === 'videoPopout') {
      const goesToOtherPage = currentPath !== path
      const hasHashOnSamePage = !goesToOtherPage && hash
      const isPopoutVideo = linkType === 'videoPopout' && link.videoId && link.videoPopoutType

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const props: any = {
        'aria-label': ariaLabel,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onFocus: (e: any) => {
          if (onFocus) onFocus(e)
        },
        ref: ref,
        className: classnames(
          className,
          { [styles.hashLink]: hasHashOnSamePage },
          {
            [styles.inactive]: !hasHashOnSamePage && currentPath === path,
          },
          {
            [activeClass || '']: activeClass && !hasHashOnSamePage && currentPath === path,
          },
        ),
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onClick: (e: any) => {
          if (onClick) onClick(e)

          if (hash) {
            if (hasHashOnSamePage) {
              e.preventDefault()
            }

            /*
            TODO: Scroll-to functionality without lenis

            const goToElement = () => {
              const id = document.getElementById(hash)

              if (id && scroll) {
                scroll.scrollTo(id, {
                  duration: 0.3,
                })
              }
            }

            setTimeout(
              () => {
                goToElement()
              },
              hasHashOnSamePage ? 0 : 500,
            )
              */
          }
        },
        onMouseEnter: () => {
          if (onMouseEnter) onMouseEnter()
        },
        onMouseLeave: () => {
          if (onMouseLeave) onMouseLeave()
        },
      }

      if (hasHashOnSamePage || isPopoutVideo) {
        return (
          <span {...props}>
            {label && !children && <span>{label}</span>}
            {children && children}
          </span>
        )
      }

      // props.scroll = false
      props.href = `${path}${queryParamString ? queryParamString : ''}${hash ? `#${hash}` : ''}`
      props['data-link'] = true

      if (tabIndexHidden) {
        props.tabIndex = -1
      }

      return (
        <NextLink {...props}>
          {label && !children && <span>{label}</span>}
          {children && children}
        </NextLink>
      )
    }
  },
)

Link.displayName = 'Link'

export default Link
