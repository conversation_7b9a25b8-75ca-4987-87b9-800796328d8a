@mixin pagination-disabled {
  opacity: 0.6;
  pointer-events: none;
}

.pagination {
  display: flex;
  align-items: baseline;
  gap: px(16);

  &.hasLines {
    border-top: 1px solid rgba($slate, 0.2);
    border-bottom: 1px solid rgba($slate, 0.2);
    padding: px(40) 0;
  }
}

.pageNumbers {
  flex: 1;
  @include flex-center;
  gap: px(8);
  color: var(--slate);

  .isDisabled & {
    @include pagination-disabled;
  }
}

.pageButton {
  @include reset-button;
  @include font-text-lg;
  @include flex-center;
  font-weight: 700;
  min-width: px(32);

  &.active {
    color: var(--navy-dark);
  }
}

.navButton {
  @include reset-button;
  @include font-heading-eyebrow-lg;
  color: var(--slate);

  .isDisabled & {
    @include pagination-disabled;
  }
}

.hide {
  opacity: 0;
  pointer-events: none;
}

.ellipsis {
  @include font-text-lg;
}
