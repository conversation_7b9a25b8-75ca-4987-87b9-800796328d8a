import type { Meta, StoryObj } from '@storybook/react'
import { useState } from 'react'
import Pagination from './Pagination'
import styles from './Pagination.module.scss'

const meta = {
  title: 'Components/Pagination',
  component: Pagination,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    pageCount: {
      control: { type: 'number', min: 1, max: 100 },
      description: 'Total number of pages',
    },
    currentPage: {
      control: { type: 'number', min: 1 },
      description: 'Current active page',
    },
    initialPage: {
      control: { type: 'number', min: 1 },
      description: 'Initial page to be selected (optional)',
    },
    onPageClick: { action: 'page clicked' },
    onButtonClick: { action: 'button clicked' },
  },
} satisfies Meta<typeof Pagination>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    pageCount: 10,
    currentPage: 1,
  },
}

const InteractivePaginationComponent = ({ pageCount }: { pageCount: number }) => {
  const [currentPage, setCurrentPage] = useState(1)

  return (
    <div className={styles.storybook__container}>
      <h3>Current Page: {currentPage}</h3>
      <Pagination
        pageCount={pageCount}
        currentPage={currentPage}
        onPageClick={page => setCurrentPage(page)}
        onButtonClick={action => {
          if (action === 'next' && currentPage < pageCount) {
            setCurrentPage(currentPage + 1)
          } else if (action === 'prev' && currentPage > 1) {
            setCurrentPage(currentPage - 1)
          }
        }}
      />
    </div>
  )
}

export const Interactive: Story = {
  args: {
    pageCount: 20,
    currentPage: 1,
  },
  render: args => <InteractivePaginationComponent pageCount={args.pageCount} />,
}

export const ManyPages: Story = {
  args: {
    pageCount: 50,
    currentPage: 25,
  },
}

export const FewPages: Story = {
  args: {
    pageCount: 3,
    currentPage: 2,
  },
}

export const FirstPage: Story = {
  args: {
    pageCount: 20,
    currentPage: 1,
  },
}

export const LastPage: Story = {
  args: {
    pageCount: 20,
    currentPage: 20,
  },
}

export const FullWidth: Story = {
  args: {
    pageCount: 10,
    currentPage: 1,
  },
  parameters: {
    layout: 'fullscreen',
  },
  render: args => (
    <div>
      <Pagination {...args} />
    </div>
  ),
}
