import React, { useState, useEffect } from 'react'
import styles from './Pagination.module.scss'
import classNames from 'classnames'

const Pagination = ({
  className,
  pageCount,
  currentPage,
  initialPage,
  isDisabled = false,
  hasLines = true,
  onPageClick,
  onButtonClick,
}: PaginationProps) => {
  const [activePage, setActivePage] = useState(initialPage || currentPage)

  useEffect(() => {
    setActivePage(currentPage)
  }, [currentPage])

  const handlePageClick = (page: number) => {
    setActivePage(page)
    onPageClick?.(page)
  }

  const handlePrevClick = () => {
    if (activePage > 1) {
      setActivePage(activePage - 1)
      onPageClick?.(activePage - 1)
      onButtonClick?.('prev')
    }
  }

  const handleNextClick = () => {
    if (activePage < pageCount) {
      setActivePage(activePage + 1)
      onPageClick?.(activePage + 1)
      onButtonClick?.('next')
    }
  }

  const renderPageNumbers = () => {
    const pages = []

    // Always show first page
    pages.push(
      <PageButton
        key={1}
        page={1}
        isActive={activePage === 1}
        onClick={handlePageClick}
        ariaLabel={'Page 1'}
        ariaCurrent={activePage === 1 ? 'page' : undefined}
        isDisabled={isDisabled}
      />,
    )

    let startPage = Math.max(2, activePage - 1)
    let endPage = Math.min(pageCount - 1, activePage + 1)

    // Near Start
    if (activePage <= 2) {
      startPage = 2
      endPage = Math.min(pageCount - 1, 4)
    }
    // Near End
    else if (activePage >= pageCount - 1) {
      startPage = Math.max(2, pageCount - 3)
      endPage = pageCount - 1
    }

    // Ellipsis at start
    if (startPage > 2) {
      pages.push(
        <span
          key="ellipsis-start"
          className={styles.ellipsis}
        >
          ...
        </span>,
      )
    }

    // Middle page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <PageButton
          key={i}
          page={i}
          isActive={activePage === i}
          onClick={handlePageClick}
          ariaLabel={`Page ${i}`}
          ariaCurrent={activePage === i ? 'page' : undefined}
          isDisabled={isDisabled}
        />,
      )
    }

    // Ellipsis at end
    if (endPage < pageCount - 1) {
      pages.push(
        <span
          key="ellipsis-end"
          className={styles.ellipsis}
        >
          ...
        </span>,
      )
    }

    // Always show last page if there is more than one page
    if (pageCount > 1) {
      pages.push(
        <PageButton
          key={pageCount}
          page={pageCount}
          isActive={activePage === pageCount}
          onClick={handlePageClick}
          ariaLabel={`Page ${pageCount}`}
          ariaCurrent={activePage === pageCount ? 'page' : undefined}
          isDisabled={isDisabled}
        />,
      )
    }

    return pages
  }

  // Don't render if there's only one page
  if (pageCount <= 1) {
    return null
  }

  return (
    <nav
      className={classNames(
        styles.pagination,
        className,
        {
          [styles.hasLines]: hasLines,
        },
        {
          [styles.isDisabled]: isDisabled,
        },
      )}
      aria-label="Pagination"
    >
      <button
        className={classNames(styles.navButton, styles.prevButton, {
          [styles.hide]: activePage === 1,
        })}
        onClick={handlePrevClick}
        aria-label="Previous page"
        tabIndex={activePage === 1 ? -1 : 0}
        aria-disabled={activePage === 1 || isDisabled}
        disabled={activePage === 1 || isDisabled}
      >
        PREV
      </button>
      <div className={styles.pageNumbers}>{renderPageNumbers()}</div>
      <button
        className={classNames(styles.navButton, styles.nextButton, {
          [styles.hide]: activePage === pageCount,
        })}
        onClick={handleNextClick}
        aria-label="Next page"
        tabIndex={activePage === pageCount ? -1 : 0}
        aria-disabled={activePage === pageCount || isDisabled}
        disabled={activePage === pageCount || isDisabled}
      >
        NEXT
      </button>
    </nav>
  )
}

interface PageButtonProps {
  page: number
  isActive: boolean
  onClick: (page: number) => void
  className?: string
  ariaLabel?: string
  ariaCurrent?: 'page' | undefined
  isDisabled?: boolean
}

const PageButton = ({ page, isActive, onClick, className, ariaLabel, ariaCurrent, isDisabled }: PageButtonProps) => {
  return (
    <button
      type="button"
      onClick={() => onClick(page)}
      className={classNames(styles.pageButton, { [styles.active]: isActive }, className)}
      aria-label={ariaLabel}
      aria-current={ariaCurrent}
      disabled={isDisabled}
    >
      {page}
    </button>
  )
}

export default Pagination

Pagination.displayName = 'Pagination'
