import imageUrlBuilder from '@sanity/image-url'

/*

TODO: make breakpoints universal variable

*/

import { ImageBuilderOptions, SanityImage } from '@/types/sanity/SanityImage'

const _breakpoints = /** @type {const} */ [
  {
    // from 0 - 767 we apply the base/small breakpoint
    name: 'sm',
    min: 0,
  },
  {
    // Tablet - starts at 768
    name: 'md',
    min: 768,
  },
  {
    // Desktop - starts at 1240
    name: 'lg',
    min: 1240,
  },
  {
    // Desktop XL - starts at 1921
    name: 'xl',
    min: 1921,
  },
]

_breakpoints.sort((a, b) => (a.min === null ? 1 : b === null ? -1 : a.min - b.min))

const _BREAKPOINTS: {
  [key: string]: number
} = {}

_breakpoints.forEach(breakpoint => {
  _BREAKPOINTS[breakpoint.name] = breakpoint.min === 0 ? 1 : breakpoint.min
})

export const BREAKPOINTS = _BREAKPOINTS

export const breakpoints = _breakpoints

export const IMAGE_SRC_SET_WIDTHS = [
  // Default imageSizes: 256, 384, 640, 750, 828, 1080, 1200, 1920, 2048, 3840,
  256, 750, 1200, 1920, 2600,
]

export const columnToVw = (column: number | string = 12) => {
  if (typeof column === 'string') return column
  const isFullWidth = column === 12
  // The max grid width is 1440px, we want to ensure that the image never exceeds this width
  // This not being used on full width images
  const maxWidth = 1440 * (column / 12)
  if (isFullWidth) {
    return `${Math.ceil((column / 12) * 100)}vw`
  } else {
    return `min(${Math.ceil((column / 12) * 100)}vw, ${maxWidth}px)`
  }
}

// This is used specifically to remove native functionality of
// generating image srcSet within _next/ directory which is
// then served from Vercel, and to instead request all images
// from the Sanity CDN directly
export const imageLoader = ({ src, width }: { src: string; width: number }) => {
  const url = new URL(src)
  const originalHeight = parseInt(url.searchParams.get('h') || '0')
  const originalWidth = parseInt(url.searchParams.get('w') || '0')
  url.searchParams.set('auto', 'format')
  url.searchParams.set('fit', 'max')
  url.searchParams.set('w', width.toString())

  if (originalHeight && originalWidth) {
    url.searchParams.set('h', `${Math.round((width * originalHeight) / originalWidth)}`)
  }

  return url.href
}

export const getImageUrl = (
  image: SanityImage,
  { width, height, quality = 80, blur, isCover, format }: ImageBuilderOptions,
) => {
  const builder = imageUrlBuilder({
    projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || '',
    dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || '',
  })
  const urlFor = builder.image(image)
  let srcObject = urlFor.width(width).height(height).quality(quality)
  if (format) {
    srcObject = srcObject.format(format)
  } else {
    srcObject = srcObject.auto('format')
  }
  if (isCover && image?.crop) {
    srcObject = srcObject
      .fit('crop')
      .crop('focalpoint')
      .focalPoint(image.hotspot?.x || 0.5, image.hotspot?.y || 0.5)
  }
  if (blur) {
    srcObject = srcObject.blur(blur)
  }
  if (image?.crop) {
    const cropWidthPercent = 1 - image.crop.left - image.crop.right
    const croppedWidth = Math.round(image.asset.width * cropWidthPercent)
    const cropHeightPercent = 1 - image.crop.top - image.crop.bottom
    const croppedHeight = Math.round(image.asset.height * cropHeightPercent)

    srcObject = srcObject.rect(
      Math.round(image.crop?.left * image.asset.width),
      Math.round(image.crop?.top * image.asset.height),
      croppedWidth,
      croppedHeight,
    )
  }

  const url = srcObject.url()

  return url
}
