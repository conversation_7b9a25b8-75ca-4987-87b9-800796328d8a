'use client'

import NextImage from 'next/image'
import React, { CSSProperties, ForwardedRef, forwardRef, useMemo } from 'react'
import gsap from 'gsap'
import styles from './SanityImage.module.scss'
import { ImageProps } from '@/types/sanity/SanityImage'
import { BREAKPOINTS, columnToVw, getImageUrl, IMAGE_SRC_SET_WIDTHS, imageLoader } from './SanityImage.helper'

const Image = (props: ImageProps, ref: ForwardedRef<HTMLImageElement>) => {
  const {
    className,
    source,
    columns = 12,
    onReady,
    dpr,
    blur,
    preload,
    animated = true,
    quality = 80,
    width,
    height,
    aspectRatio,
    isCover = false,
  } = props

  const sourceProps = useMemo(() => {
    const props = {
      width: width || source?.asset?.width,
      height: height || source?.asset?.height,
      dpr,
      blur,
      quality,
      isCover,
    }

    if (typeof aspectRatio === 'number') {
      props.height = Math.round(props.width / aspectRatio)
    }

    return props
  }, [source, dpr, blur, quality, width, height, aspectRatio, isCover])

  const sizes = useMemo(() => {
    if (typeof columns === 'number') {
      return `${columnToVw(columns)}`
    } else if (typeof columns === 'string') {
      return columns
    } else {
      const imageSizeArray = Object.keys(BREAKPOINTS)
        .reverse()
        .map(breakpointKey => {
          if (columns[breakpointKey as keyof typeof columns]) {
            const value = columnToVw(columns[breakpointKey as keyof typeof columns])
            return `(min-width: ${BREAKPOINTS[breakpointKey]}px) ${value}`
          }
        })
        .filter(item => item)
        .join(', ')

      return imageSizeArray
    }
  }, [columns])

  const croppedImageSources = useMemo(() => {
    if (typeof aspectRatio === 'number' || !aspectRatio) return null

    const imageSourcesByBreakpoint = Object.keys(BREAKPOINTS)
      .reverse()
      .map(breakpointKey => {
        if (aspectRatio[breakpointKey as keyof typeof aspectRatio]) {
          const value = aspectRatio[breakpointKey as keyof typeof aspectRatio]

          if (!value) return null

          const vw = columnToVw(columns[breakpointKey as keyof typeof columns])

          const srcSet = IMAGE_SRC_SET_WIDTHS.map(width => {
            return `${getImageUrl(source, {
              width,
              height: Math.round(width / value),
              isCover,
            })} ${width}w`
          }).join(', ')

          return (
            <source
              srcSet={srcSet}
              media={`(min-width: ${BREAKPOINTS[breakpointKey]}px)`}
              sizes={vw}
              key={breakpointKey}
            />
          )
        }
      })
      .filter(item => item)

    return imageSourcesByBreakpoint
  }, [aspectRatio, source, columns, isCover])

  const imageElement = (
    <NextImage
      loader={imageLoader}
      onLoad={e => {
        const target = e.target as HTMLImageElement
        if (onReady) onReady(target)
        if (target && animated) {
          gsap.to(target, {
            opacity: 1,
            duration: 0.4,
          })
        }
      }}
      ref={ref}
      priority={preload}
      className={`
              ${className} 
              ${styles.image} 
              ${animated && styles.animated} 
              ${isCover && styles.isCover} 
            `}
      alt={source?.alt || ''}
      src={getImageUrl(source, sourceProps)}
      style={
        {
          '--object-position': source?.hotspot ? `${source?.hotspot.x * 100}% ${source?.hotspot.y * 100}%` : 'center',
        } as CSSProperties
      }
      sizes={sizes}
      width={sourceProps.width || source?.asset?.width}
      height={sourceProps.height || source?.asset?.height}
    />
  )

  if (!source) return null

  if (croppedImageSources && imageElement) {
    return (
      <picture>
        {croppedImageSources}
        {imageElement}
      </picture>
    )
  }

  return imageElement
}

export default forwardRef(Image)
