import type { Meta, StoryObj } from '@storybook/react'
import ItemLabelList from './ItemLabelList'
import StarsRating from '@/components/StarsRating/StarsRating'
import Button from '@/components/Button/Button'

const meta = {
  title: 'Components/ItemLabelList',
  component: ItemLabelList,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  decorators: [
    Story => {
      return (
        <div style={{ width: '400px', background: '#fff', padding: '1rem' }}>
          <Story />
        </div>
      )
    },
  ],
} satisfies Meta<typeof ItemLabelList>

export default meta

type Story = StoryObj<typeof meta>

export const Ingredients: Story = {
  args: {
    title: 'Ingredients',
    buttonLabel: 'Copy',
    buttonIcon: 'copy',
    buttonCallback: () => {},
    items: [
      { label: 'Chicken thighs', value: '2 lbs' },
      { label: 'Kosher salt', value: '1 tbsp' },
      { label: 'Ground black pepper', value: '1 tsp' },
      { label: 'Vegetable oil', value: '2 tbsp' },
      { label: 'Yellow onion', value: '1' },
      { label: 'Red bell pepper', value: '1' },
      { label: 'White rice', value: '2 cups' },
      { label: 'Chicken broth', value: '3 cups' },
    ],
  },
}

export const Makes: Story = {
  args: {
    title: 'Make',
    items: [{ label: '', value: '2 9-inch loaves' }],
  },
}

export const TotalTime: Story = {
  args: {
    title: 'Total Time',
    items: [{ label: '', value: '10 hours' }],
  },
}

export const ActiveTime: Story = {
  args: {
    title: 'Active Time Plus Cooling',
    items: [{ label: '', value: '2½ hours' }],
  },
}

export const Rating: Story = {
  args: {
    title: 'Rating',
    items: [
      {
        component: (
          <StarsRating
            percent={0.8}
            count={102}
            size="md"
          />
        ),
      },
    ],
  },
}

export const ProgramOverview: Story = {
  args: {
    title: 'Program Overview',
    size: 'sm',
    items: [
      { label: 'Season 9 Premiere', value: 'September 2024' },
      { label: 'Number of Episodes', value: '23' },
      { label: 'Episode Length', value: '30 minutes' },
      { label: 'Website', value: '177milkstreet.com/TV' },
    ],
  },
}

export const WaysToListen: Story = {
  args: {
    title: 'Ways to Listen',
    items: [
      {
        component: (
          <Button
            label="Apple Podcasts"
            icon="podcasts"
            style="bare"
            labelSize="md"
          />
        ),
      },
      {
        component: (
          <Button
            label="TuneIn"
            icon="tuneIn"
            style="bare"
            labelSize="md"
          />
        ),
      },
      {
        component: (
          <Button
            label="Spotify"
            icon="spotify"
            style="bare"
            labelSize="md"
          />
        ),
      },
      {
        component: (
          <Button
            label="Amazon Music"
            icon="amazonMusic"
            style="bare"
            labelSize="md"
          />
        ),
      },
    ],
  },
}

export const OtherWaysToWatch: Story = {
  args: {
    title: 'Other Ways to Watch',
    items: [
      {
        component: (
          <Button
            label="YouTube"
            icon="youtube"
            style="bare"
            labelSize="md"
          />
        ),
      },
      {
        component: (
          <Button
            label="PBS Passport"
            icon="document"
            style="bare"
            labelSize="md"
          />
        ),
      },
      {
        component: (
          <Button
            label="Prime Video"
            icon="play"
            style="bare"
            labelSize="md"
          />
        ),
      },
      {
        component: (
          <Button
            label="Roku TV"
            icon="playCircle"
            style="bare"
            labelSize="md"
          />
        ),
      },
    ],
  },
}

export const MultipleExamples: Story = {
  args: {
    title: 'Multiple Examples',
    items: [{ label: 'Example', value: 'Value' }],
  },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <ItemLabelList
        title="Ingredients"
        buttonLabel="Copy"
        buttonIcon="copy"
        buttonCallback={() => {}}
        items={[
          { label: 'Chicken thighs', value: '2 lbs' },
          { label: 'Kosher salt', value: '1 tbsp' },
          { label: 'Ground black pepper', value: '1 tsp' },
        ]}
      />
      <ItemLabelList
        title="Ways to Listen"
        items={[
          {
            component: (
              <Button
                label="Apple Podcasts"
                icon="podcasts"
                style="bare"
                labelSize="md"
              />
            ),
          },
          {
            component: (
              <Button
                label="Spotify"
                icon="spotify"
                style="bare"
                labelSize="md"
              />
            ),
          },
        ]}
      />
      <ItemLabelList
        title="Program Overview"
        size="sm"
        items={[
          { label: 'Host', value: 'Christopher Kimball' },
          { label: 'Length', value: '59:00' },
          { label: 'Frequency', value: 'Weekly' },
        ]}
      />
    </div>
  ),
}
