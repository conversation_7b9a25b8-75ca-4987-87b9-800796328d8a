'use client'

import classnames from 'classnames'
import styles from './ItemLabelList.module.scss'
import LineHeading from '@/components/LineHeading/LineHeading'
import { isValidElement } from 'react'
import ContainerOrLink from '../ContainerOrLink/ContainerOrLink'

const ItemLabelList = ({ title, items, className, ...lineHeadingProps }: ItemLabelListProps) => {
  if (!items || items.length === 0) {
    return null
  }

  return (
    <div className={classnames(styles.ItemLabelList, className)}>
      {title && (
        <LineHeading
          title={title}
          {...lineHeadingProps}
        />
      )}

      <ul className={styles.list}>
        {items.map((item, index: number) => {
          // Custom component item
          if ('component' in item && isValidElement(item.component)) {
            return (
              <li
                key={index}
                className={styles.item}
              >
                {item.component}
              </li>
            )
          }

          // Label/Value item
          if ('label' in item && 'value' in item) {
            return (
              <li
                key={index}
                className={styles.item}
              >
                <ContainerOrLink
                  link={item.link}
                  className={styles.labelValueContainer}
                >
                  <div className={styles.label}>{item.label}</div>
                  <div className={styles.value}>{item.value}</div>
                </ContainerOrLink>
              </li>
            )
          }

          return null
        })}
      </ul>
    </div>
  )
}

ItemLabelList.displayName = 'ItemLabelList'

export default ItemLabelList
