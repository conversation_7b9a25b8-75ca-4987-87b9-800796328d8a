import type { Meta, StoryObj } from '@storybook/react'
import But<PERSON> from './Button'
import { STUB_LINK } from '../Link/Link.stub'
import { ICONS } from '../Icon/Icon'

const meta = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    style: {
      options: ['primary', 'secondary', 'block', 'rounded', 'roundedFilled', 'bare', 'iconOnly', 'iconOnlyFilled'],
      control: { type: 'select' },
    },
    iconPosition: {
      options: ['left', 'right'],
      control: { type: 'select' },
    },
    labelSize: {
      options: ['sm', 'md', 'lg'],
      control: { type: 'select' },
    },
    bareColor: {
      options: ['default', 'loud', 'slate'],
      control: { type: 'select' },
    },
    bareFont: {
      options: ['default', 'eyebrow'],
      control: { type: 'select' },
    },
    iconSize: {
      options: ['sm', 'md'],
      control: { type: 'select' },
    },
    icon: {
      options: Object.keys(ICONS),
      control: { type: 'select' },
    },
  },
} satisfies Meta<typeof Button>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    label: 'Primary Button',
    style: 'primary',
  },
}

export const Secondary: Story = {
  args: {
    label: 'Secondary Button',
    style: 'secondary',
  },
}

export const Block: Story = {
  args: {
    label: 'Block Button',
    style: 'block',
  },
}

export const Rounded: Story = {
  args: {
    label: 'Rounded Button',
    style: 'rounded',
  },
}

export const RoundedFilled: Story = {
  args: {
    label: 'Rounded Filled Button',
    style: 'roundedFilled',
  },
}

export const IconOnly: Story = {
  args: {
    style: 'iconOnly',
    icon: 'download',
  },
}

export const IconOnlyLarge: Story = {
  args: {
    style: 'iconOnly',
    icon: 'audioBack',
    iconOnlySize: 'lg',
  },
}

export const IconFilled: Story = {
  args: {
    style: 'iconOnlyFilled',
    icon: 'download',
  },
}

export const Bare: Story = {
  args: {
    label: 'Bare Button',
    style: 'bare',
  },
}

export const BareSmall: Story = {
  args: {
    label: 'Bare Button Small',
    style: 'bare',
    labelSize: 'sm',
  },
}

export const BareLarge: Story = {
  args: {
    label: 'Bare Button Large',
    style: 'bare',
    labelSize: 'lg',
  },
}

export const BareWithOtherFont: Story = {
  args: {
    label: 'Bare with Other Font Button',
    style: 'bare',
    bareFont: 'eyebrow',
    icon: 'arrowRight',
    iconPosition: 'right',
  },
}

export const BareWithOtherColor: Story = {
  args: {
    label: 'Bare with Other Color',
    style: 'bare',
    bareColor: 'loud',
    icon: 'arrowRight',
    iconPosition: 'right',
  },
}

export const BareWithSlateColor: Story = {
  args: {
    label: 'Bare with Slate Color',
    style: 'bare',
    bareColor: 'slate',
    icon: 'arrowRight',
    iconPosition: 'right',
  },
}

export const WithIcon: Story = {
  args: {
    label: 'Rounded Button With Icon',
    style: 'rounded',
    icon: 'bookmark',
  },
}

export const WithIconOnRightSide: Story = {
  args: {
    label: 'Icon On Right Side',
    style: 'primary',
    icon: 'bookmark',
    iconPosition: 'right',
  },
}

export const WithLink: Story = {
  args: {
    label: 'Linked Button',
    style: 'primary',
    icon: 'caretRight',
    iconPosition: 'right',
    link: STUB_LINK.TEST_WITH_LABEL as SanityLink,
  },
}

export const SmallIcon: Story = {
  args: {
    label: 'Small Icon',
    style: 'bare',
    icon: 'arrowRight',
    iconPosition: 'right',
    iconSize: 'sm',
  },
}

export const Disabled: Story = {
  args: {
    label: 'Disabled',
    disabled: true,
  },
}
