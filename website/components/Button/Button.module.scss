.Button {
  @include reset-button;
  transition:
    background-color $transition-short,
    border-color $transition-short,
    color $transition-short;
  @include flex-center;
  display: flex-inline;
  padding: 0 px(16);

  &:disabled {
    opacity: 0.7;
    pointer-events: none;
  }

  &[data-button-style='primary'],
  &[data-button-style='secondary'],
  &[data-button-style='block'] {
    height: px(45);
  }

  &[data-button-style='rounded'],
  &[data-button-style='roundedFilled'] {
    height: px(40);
    border-radius: 100px;
  }

  // Has icon + padding
  &.hasIcon {
    gap: px(5);
  }

  &.hasIcon[data-button-icon-position='left']:not([data-button-style='bare']) {
    padding-left: px(12);
  }

  &.hasIcon[data-button-icon-position='right']:not([data-button-style='bare']) {
    padding-right: px(12);
  }

  // Primary
  &[data-button-style='primary'] {
    background-color: var(--loud-warm);
    color: var(--white);

    @include hover {
      background-color: var(--loud-warm-darker);
    }
  }

  // Secondary
  &[data-button-style='secondary'] {
    background-color: var(--white);
    color: var(--navy-dark);
    border: 1px solid rgba($navy-dark, 0.2);

    @include hover {
      border-color: var(--navy-dark);
    }
  }

  // Block
  &[data-button-style='block'] {
    color: var(--navy-dark);

    @include hover {
      color: var(--slate);
    }
  }

  // Rounded
  &[data-button-style='rounded'] {
    color: var(--navy-dark);
    border: 1px solid rgba($navy-dark, 0.2);
    background-color: var(--white);

    @include hover {
      border-color: var(--navy-dark);
    }

    &:active {
      background-color: transparent;
    }
  }

  // Rounded Filled
  &[data-button-style='roundedFilled'] {
    color: var(--white);
    border: 1px solid var(--loud-warm);
    background-color: var(--loud-warm);

    @include hover {
      border-color: var(--loud-warm);
      background-color: var(--loud-warm-darker);
    }
  }

  // Icon only
  &[data-button-style='iconOnly'],
  &[data-button-style='iconOnlyFilled'] {
    @include flex-center;
    @include box(px(40));
    display: flex-inline;
    border-radius: 100px;

    // stylelint-disable-next-line declaration-no-important
    padding: 0 !important;

    &[data-button-icon-only-size='lg'] {
      @include box(px(48));
    }
  }

  &[data-button-style='iconOnly'] {
    background-color: var(--white);
    color: var(--navy-dark);
    border: 1px solid var(--white);

    @include hover {
      border-color: rgba($navy-dark, 0.2);
    }
  }

  &[data-button-style='iconOnlyFilled'] {
    background-color: var(--loud-warm);
    color: var(--white);
    border: 1px solid var(--loud-warm);

    @include hover {
      border-color: var(--loud-warm-darker);
      background-color: var(--loud-warm-darker);
    }
  }

  // Bare
  &[data-button-style='bare'][data-button-bare-color='loud'] {
    color: var(--loud-warm);

    @include hover {
      color: var(--loud-warm-darker);
    }
  }

  &[data-button-style='bare'][data-button-bare-color='slate'] {
    color: var(--slate);

    @include hover {
      color: var(--navy-dark);
    }
  }

  &[data-button-style='bare'] {
    padding: 0;

    @include hover {
      color: rgba($navy-dark, 0.5);
    }
  }
}

.label {
  display: block;
  color: inherit;

  // stylelint-disable-next-line declaration-no-important
  line-height: 1 !important;

  [data-button-style='primary'] &,
  [data-button-style='secondary'] &,
  [data-button-style='block'] &,
  [data-button-style='rounded'] &,
  [data-button-style='roundedFilled'] & {
    @include font-text-base;
    font-weight: 700;
  }

  [data-button-style='bare'][data-button-label-size='sm'] & {
    @include font-text-sm;
    font-weight: 700;
  }

  [data-button-style='bare'][data-button-label-size='md'] & {
    @include font-text-base;
    font-weight: 700;
  }

  [data-button-style='bare'][data-button-label-size='lg'] & {
    @include font-text-lg;
    font-weight: 700;
  }

  [data-button-style='bare'][data-button-bare-font='eyebrow'][data-button-label-size='sm'] & {
    @include font-heading-eyebrow-sm;
  }

  [data-button-style='bare'][data-button-bare-font='eyebrow'][data-button-label-size='md'] & {
    @include font-heading-eyebrow-md;
  }

  [data-button-style='bare'][data-button-bare-font='eyebrow'][data-button-label-size='lg'] & {
    @include font-heading-eyebrow-lg;
  }

  // Position Ordering
  [data-button-icon-position='left'] & {
    order: 2;
  }

  [data-button-icon-position='right'] & {
    order: 1;
  }
}

.icon {
  color: inherit;
  width: px(24);
  transition: opacity $transition-short;

  [data-button-icon-size='sm'] & {
    width: px(16);
  }

  // Position Ordering
  [data-button-icon-position='left'] & {
    order: 1;
  }

  [data-button-icon-position='right'] & {
    order: 2;
  }
}
