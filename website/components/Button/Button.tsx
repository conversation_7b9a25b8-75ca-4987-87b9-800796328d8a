import classnames from 'classnames'
import styles from './Button.module.scss'
import React, { useImperativeHandle, useMemo, useRef, useState } from 'react'
import Link from '@/components/Link/Link'
import useBreakpoint from '@/hooks/use-breakpoint'
import Icon from '../Icon/Icon'

const Button = React.forwardRef<ButtonImperativeHandle, ButtonProps>(
  (
    {
      className,
      label,
      children,
      onFocus,
      onBlur,
      onMouseEnter,
      onMouseLeave,
      isHoverState,
      onClick,
      element,
      link,
      linkClassName,
      disableHoverAnimation = false,
      ariaLabel,
      disableOpenNewTab,
      disabled,
      style = 'primary',
      htmlFor,
      icon,
      iconPosition = 'left',
      labelSize = 'md',
      bareColor = 'default',
      bareFont = 'default',
      iconSize = 'md',
      iconOnlySize = 'md',
    },
    ref,
  ) => {
    label = label || link?.label
    const [isHover, setIsHover] = useState(false)
    const containerRef = useRef<HTMLButtonElement | HTMLLabelElement | null>(null)
    const Element = link ? 'span' : element || 'button'
    const { isMobile } = useBreakpoint()
    style = useMemo(() => {
      if (!link && !label && icon && style !== 'iconOnlyFilled') {
        return 'iconOnly'
      }
      return style
    }, [icon, link, label, style])

    useImperativeHandle(ref, () => ({
      getElement: () => {
        return containerRef.current
      },
      setIsHover: isHover => {
        setIsHover(isHover)
      },
    }))

    const handleOnMouseEnter = () => {
      if (onMouseEnter) onMouseEnter()
      if (!isMobile && !disableHoverAnimation) {
        setIsHover(true)
      }
    }

    const handleOnMouseLeave = () => {
      if (onMouseLeave) onMouseLeave()
      if (!isMobile && !disableHoverAnimation) {
        setIsHover(false)
      }
    }

    const handleOnClick = () => {
      if (onClick) onClick()
    }

    const handleOnFocus = () => {
      if (onFocus) onFocus()
    }

    const handleOnBlur = () => {
      if (onBlur) onBlur()
    }

    const content = (
      <Element
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={containerRef as any}
        className={classnames(
          styles.Button,
          className,
          { [styles.hover]: isHover || isHoverState },
          { [styles.hasIcon]: icon },
        )}
        onMouseEnter={handleOnMouseEnter}
        onMouseLeave={handleOnMouseLeave}
        onClick={handleOnClick}
        aria-label={ariaLabel}
        onFocus={handleOnFocus}
        onBlur={handleOnBlur}
        disabled={disabled}
        data-button
        htmlFor={htmlFor}
        data-button-style={style}
        data-button-icon-position={iconPosition}
        data-button-label-size={labelSize}
        data-button-bare-color={bareColor}
        data-button-bare-font={bareFont}
        data-button-icon-size={iconSize}
        data-button-icon-only-size={iconOnlySize}
      >
        {label && !children && (
          <>
            <span
              className={styles.label}
              data-button-label
            >
              {label}
            </span>
          </>
        )}
        {children && children}
        {icon && (
          <Icon
            name={icon}
            className={styles.icon}
          />
        )}
      </Element>
    )

    if (link) {
      return (
        <Link
          className={classnames(linkClassName, styles.link)}
          link={link}
          disableOpenNewTab={disableOpenNewTab}
        >
          {content}
        </Link>
      )
    }

    return content
  },
)

Button.displayName = 'Button'

export default Button
