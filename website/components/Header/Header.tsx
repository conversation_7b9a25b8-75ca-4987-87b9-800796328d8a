'use client'

import SearchBar from '@/components/SearchBar/SearchBar'
// import { HeaderProps, NavItem } from '@/types'
import Link from '@/components/Link/Link'
import { DOC_TYPES, HOME_SLUG } from '@/data'
import styles from './Header.module.scss'
import classnames from 'classnames'
import Icon from '@/components/Icon/Icon'
import Button from '@/components/Button/Button'
import { useState, useEffect, useRef } from 'react'
import { HeaderNavItem, NavMenuItem, NavSubMenuGroup } from '@/types/navigation'
import SanityImage from '@/components/SanityImage/SanityImage'

const DEFAULT_INTERNAL_LINK = {
  linkType: 'internal' as const,
  link: {
    slug: HOME_SLUG,
    _type: DOC_TYPES.PAGE,
  },
}

export default function Header({ siteSettings, headerNav, headerData }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [activeMobileSubmenu, setActiveMobileSubmenu] = useState<string | null>(null)
  const [mobileSubmenuItems, setMobileSubmenuItems] = useState<
    {
      title?: string
      titleLink?: SanityLink
      links: {
        _type: string
        title?: string
        linkData: SanityLink | null
      }[]
    }[]
  >([])
  const navigationRef = useRef<HTMLDivElement>(null)

  // Position a specific dropdown to stay within viewport
  const positionDropdown = (navItem: Element) => {
    const submenu = navItem.querySelector(`.${styles.submenu}`) as HTMLElement
    if (!submenu) return

    // Reset positioning to get natural dimensions
    submenu.style.left = ''
    submenu.style.transform = ''
    submenu.style.right = ''

    const navItemRect = navItem.getBoundingClientRect()
    const submenuRect = submenu.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const margin = 20 // Minimum margin from viewport edges

    // Calculate ideal center position
    const navItemCenter = navItemRect.left + navItemRect.width / 2
    const submenuHalfWidth = submenuRect.width / 2

    // Check if centered position would go off edges
    const leftEdge = navItemCenter - submenuHalfWidth
    const rightEdge = navItemCenter + submenuHalfWidth

    if (leftEdge < margin) {
      // Would go off left edge - align to left with margin
      submenu.style.left = `${margin - navItemRect.left}px`
      submenu.style.transform = 'none'
    } else if (rightEdge > viewportWidth - margin) {
      // Would go off right edge - align to right with margin
      submenu.style.left = 'auto'
      submenu.style.right = `${margin - (viewportWidth - navItemRect.right)}px`
      submenu.style.transform = 'none'
    } else {
      // Can be centered safely
      submenu.style.left = '50%'
      submenu.style.transform = 'translateX(-50%)'
    }
  }

  // Position dropdowns on hover
  useEffect(() => {
    if (!navigationRef.current) return

    const navItems = navigationRef.current.querySelectorAll(`.${styles.nav__item}`)
    const handlers = new Map()

    navItems.forEach(navItem => {
      const handleMouseEnter = () => {
        // Small delay to ensure dropdown is visible before positioning
        setTimeout(() => positionDropdown(navItem), 10)
      }

      handlers.set(navItem, handleMouseEnter)
      navItem.addEventListener('mouseenter', handleMouseEnter)
    })

    return () => {
      navItems.forEach(navItem => {
        const handler = handlers.get(navItem)
        if (handler) {
          navItem.removeEventListener('mouseenter', handler)
        }
      })
    }
  }, [headerNav])

  // Handle body scroll locking when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.setAttribute('data-disable-scroll', 'true')
    } else {
      document.body.setAttribute('data-disable-scroll', 'false')
    }

    return () => {
      document.body.setAttribute('data-disable-scroll', 'false')
    }
  }, [isMobileMenuOpen])

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(prev => !prev)
    if (isSearchOpen) setIsSearchOpen(false)
    // Reset submenu state when closing the menu
    if (isMobileMenuOpen) {
      setActiveMobileSubmenu(null)
      // Add a small delay to ensure any click events are processed before closing
      setTimeout(() => {
        setActiveMobileSubmenu(null)
      }, 50)
    }
  }

  const toggleSearch = () => {
    setIsSearchOpen(prev => !prev)
    if (isMobileMenuOpen) setIsMobileMenuOpen(false)
  }

  const openMobileSubmenu = (item: HeaderNavItem) => {
    setActiveMobileSubmenu(item.title)

    if (item.subMenuGroups && item.subMenuGroups.length > 0) {
      const mobileGroups: {
        links: {
          _type: string
          title?: string

          linkData: SanityLink | null
        }[]
      }[] = []

      item.subMenuGroups.forEach((group: NavSubMenuGroup) => {
        const hasItems = 'items' in group && Array.isArray(group.items)

        if (hasItems) {
          mobileGroups.push({
            links: group.items.map((item: NavMenuItem) => ({
              _type: item._type,
              title: item.title,
              linkData:
                item._type === 'link'
                  ? item.linkData
                  : item.titleLink && item.titleLink.linkType !== 'disabled'
                    ? item.titleLink
                    : null,
            })),
          })
        }
      })

      // Filter out any empty groups
      const groupedItems = mobileGroups.filter(group => !!group && !!group.links && group.links.length > 0)
      setMobileSubmenuItems(groupedItems)
    } else {
      setMobileSubmenuItems([])
    }
  }

  const closeMobileSubmenu = () => {
    setActiveMobileSubmenu(null)
    setMobileSubmenuItems([])
  }

  const toggleHoverDisabled = (e: React.MouseEvent) => {
    const navItem = e.currentTarget.closest(`.${styles.nav__item}`)
    if (!navItem) return
    navItem.classList.toggle(styles['hover-disabled'])
    setTimeout(() => {
      navItem.classList.remove(styles['hover-disabled'])
    }, 500)
  }

  if (!siteSettings || !headerNav || !headerData) return null

  return (
    <header className={styles.header}>
      <div className={styles.inner}>
        {/* Top row: Logo, Search Bar, Login/Signup */}
        <div className={styles.desktopHeader}>
          {/* Logo */}
          <Link
            link={DEFAULT_INTERNAL_LINK}
            className={styles.logo}
          >
            <Icon
              name="wordmark"
              className={styles.logo__img}
            />
          </Link>

          {/* SearchBar */}
          <div className={styles.searchBar}>
            <SearchBar />
          </div>

          {/* Login/Sign Up */}
          <div className={styles.authButtons}>
            {headerData?.loginLink && (
              <Button
                link={headerData.loginLink}
                className={styles.login}
                label="Login"
                style="bare"
              />
            )}
            {headerData?.signupLink && (
              <Button
                link={headerData.signupLink}
                className={styles.signup}
                label="Sign Up"
                style="primary"
              />
            )}
          </div>
        </div>

        {/* Bottom row: Navigation */}
        <div
          className={styles.navigation}
          ref={navigationRef}
        >
          <nav className={styles.nav}>
            {headerNav?.map((item, index) => {
              const hasLink = Boolean(item.linkData)
              return (
                <div
                  key={`nav-item-${index}`}
                  className={styles.nav__item}
                >
                  {/* Main Navigation Item */}
                  {hasLink ? (
                    <Link
                      link={item.linkData}
                      className={styles.nav__link}
                      activeClass={styles.active}
                    >
                      {item.title}
                    </Link>
                  ) : (
                    <button
                      className={classnames(styles.nav__button, {
                        [styles.highlighted]: item.isHighlighted,
                      })}
                    >
                      {item.title}
                    </button>
                  )}

                  {/* Sub Menu */}
                  {item.subMenuGroups && item.subMenuGroups.length > 0 && (
                    <div className={styles.submenu}>
                      {item.subMenuGroups.map((group: NavSubMenuGroup, groupIndex: number) => {
                        const hasItems = 'items' in group && Array.isArray(group.items)

                        return (
                          <div
                            key={`submenu-group-${groupIndex}`}
                            className={styles.submenu__group}
                          >
                            {hasItems && (
                              <ul className={styles.submenu__list}>
                                {group.items.map((item: NavMenuItem, itemIndex: number) => {
                                  // Check if the header follows a link (for spacing between groups)
                                  const prevItem = itemIndex > 0 ? group.items[itemIndex - 1] : null
                                  const isHeaderAfterLink =
                                    item._type === 'header' && prevItem && prevItem._type === 'link'

                                  if (item._type === 'header') {
                                    return (
                                      <li
                                        key={`submenu-header-${itemIndex}`}
                                        className={isHeaderAfterLink ? styles.submenu__header_afterLink : ''}
                                      >
                                        {item.titleLink && item.titleLink.linkType !== 'disabled' ? (
                                          <Link
                                            link={item.titleLink}
                                            className={styles.submenu__title}
                                            onClick={toggleHoverDisabled}
                                          >
                                            {item.title}
                                          </Link>
                                        ) : (
                                          <div className={styles.submenu__title}>{item.title}</div>
                                        )}
                                      </li>
                                    )
                                  } else if (item._type === 'link') {
                                    return (
                                      <li
                                        key={`submenu-link-${itemIndex}`}
                                        className={styles.submenu__item}
                                      >
                                        {item.linkData && (
                                          <Link
                                            link={item.linkData}
                                            className={styles.submenu__link}
                                            activeClass={styles.active}
                                            onClick={toggleHoverDisabled}
                                          />
                                        )}
                                      </li>
                                    )
                                  }
                                  return null
                                })}
                              </ul>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  )}
                </div>
              )
            })}
          </nav>

          {/* Bow Tie Divider */}
          <div className={styles.bowtieContainer}>
            <div className={styles.bowtie}>
              <SanityImage
                source={headerData?.bowtieImage}
                width={76}
                height={36}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Header*/}
      <div
        className={styles.mobileHeader}
        data-menu-open={isMobileMenuOpen}
        data-search-open={isSearchOpen}
      >
        {/* Logo - Mobile */}
        <Link
          link={DEFAULT_INTERNAL_LINK}
          className={styles.mobileLogo}
        >
          <Icon
            name="wordmark"
            className={styles.mobileLogo__img}
          />
        </Link>

        {/* Mobile Navigation Icons */}
        <div className={styles.mobileNav}>
          {/* User Profile */}
          <button
            className={styles.mobileNav__button}
            aria-label="User profile"
          >
            <Icon
              name="profile"
              className={styles.mobileNav__icon}
            />
          </button>

          {/* Search */}
          <button
            className={styles.mobileNav__button}
            aria-label="Search"
            onClick={toggleSearch}
          >
            <Icon
              name="magnifyingGlass"
              className={styles.mobileNav__icon}
            />
          </button>

          {/* Hamburger Menu */}
          <button
            className={classnames(styles.mobileNav__openButton, styles.mobileNav__button)}
            aria-label="Open menu"
            onClick={toggleMobileMenu}
          >
            <Icon
              name="hamburger"
              className={styles.mobileNav__icon}
            />
          </button>

          {/* Close Button (hidden by default) */}
          <button
            className={classnames(styles.mobileNav__button, styles.mobileNav__closeButton)}
            aria-label="Close menu"
            onClick={toggleMobileMenu}
          >
            <Icon
              name="close"
              className={styles.mobileNav__icon}
            />
          </button>
        </div>
      </div>

      {/* Mobile Search Overlay */}
      {isSearchOpen && (
        <div className={styles.mobileSearchOverlay}>
          {/* Header with close search button */}
          <div className={styles.mobileSearchOverlay__header}>
            <button
              className={styles.mobileSearchOverlay__close}
              aria-label="Close search"
              onClick={toggleSearch}
            >
              <Icon
                name="close"
                className={styles.mobileSearchOverlay__closeIcon}
              />
            </button>
          </div>

          {/* Mobile Search form */}
          <div className={styles.mobileSearchOverlay__searchContainer}>
            <SearchBar placeholder="Search recipes, articles, etc." />
          </div>
        </div>
      )}

      {/**************************************************************************/}
      {/*************************** Mobile Menu Overlay ***************************/}
      {/**************************************************************************/}
      {isMobileMenuOpen && (
        <div className={styles.mobileMenuOverlay}>
          {activeMobileSubmenu ? (
            <div className={styles.mobileSubmenu}>
              <div className={styles.mobileSubmenu__header}>
                <button
                  className={styles.mobileSubmenu__backButton}
                  onClick={closeMobileSubmenu}
                  aria-label="Back to main menu"
                >
                  <Icon
                    name="caretLeft"
                    className={styles.mobileSubmenu__backIcon}
                  />
                </button>
              </div>

              <div className={styles.mobileSubmenu__content}>
                <div className={styles.mobileSubmenu__groups}>
                  {mobileSubmenuItems.map((group, groupIndex) => (
                    <div
                      key={`submenu-group-${groupIndex}`}
                      className={styles.mobileSubmenu__group}
                    >
                      {group.title && (
                        <div className={styles.mobileSubmenu__headerText}>
                          {group.titleLink && group.titleLink.linkType !== 'disabled' ? (
                            <Link
                              link={group.titleLink}
                              onClick={toggleMobileMenu}
                            >
                              {group.title}
                            </Link>
                          ) : (
                            <>{group.title}</>
                          )}
                        </div>
                      )}
                      <ul className={styles.mobileSubmenu__list}>
                        {group.links &&
                          group.links.map((item, itemIndex) => {
                            if (item._type === 'header') {
                              return (
                                <li
                                  key={`submenu-header-${groupIndex}-${itemIndex}`}
                                  className={styles.mobileSubmenu__item}
                                >
                                  {item.linkData ? (
                                    <Link
                                      link={item.linkData}
                                      className={styles.mobileSubmenu__headerText}
                                      onClick={toggleMobileMenu}
                                    >
                                      {item.title}
                                    </Link>
                                  ) : (
                                    <div className={styles.mobileSubmenu__headerText}>{item.title}</div>
                                  )}
                                </li>
                              )
                            }

                            return (
                              <li
                                key={`submenu-link-${groupIndex}-${itemIndex}`}
                                className={styles.mobileSubmenu__item}
                              >
                                <Link
                                  link={item.linkData as SanityLink}
                                  className={styles.mobileSubmenu__link}
                                  onClick={toggleMobileMenu}
                                />
                              </li>
                            )
                          })}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            // Main Menu View
            <div className={styles.mobileMenu}>
              <nav className={styles.mobileMenu__nav}>
                <ul className={styles.mobileMenu__list}>
                  {headerNav?.map((item, index) => {
                    const hasSubmenu = item.subMenuGroups && item.subMenuGroups.length > 0

                    return (
                      <li
                        key={`mobile-nav-item-${index}`}
                        className={styles.mobileMenu__item}
                      >
                        {hasSubmenu ? (
                          <button
                            className={styles.mobileMenu__button}
                            onClick={() => openMobileSubmenu(item)}
                          >
                            {item.title}
                            <Icon
                              name="caretRight"
                              className={styles.mobileMenu__buttonIcon}
                            />
                          </button>
                        ) : (
                          <Link
                            link={item.linkData}
                            className={styles.mobileMenu__link}
                            activeClass={styles.active}
                            onClick={toggleMobileMenu}
                          >
                            {item.title}
                          </Link>
                        )}
                      </li>
                    )
                  })}
                </ul>
                <div className={styles.mobileMenu__footer}>
                  <div className={styles.mobileMenu__buttons}>
                    {headerData?.loginLink && (
                      <Button
                        link={headerData.loginLink}
                        linkClassName={styles.mobileMenu__loginButton}
                        onClick={toggleMobileMenu}
                        label="Login"
                        style="secondary"
                      />
                    )}
                    {headerData?.signupLink && (
                      <Button
                        link={headerData.signupLink}
                        linkClassName={styles.mobileMenu__signupButton}
                        onClick={toggleMobileMenu}
                        label="Sign Up"
                        style="primary"
                      />
                    )}
                  </div>
                </div>
              </nav>
            </div>
          )}
        </div>
      )}
    </header>
  )
}

Header.displayName = 'Header'
