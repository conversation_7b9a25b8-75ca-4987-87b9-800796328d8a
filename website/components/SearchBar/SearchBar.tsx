'use client'

import { useState, useRef } from 'react'
import styles from './SearchBar.module.scss'
import FormElement from '@/components/FormElement/FormElement'
import { FormElementImperativeHandle } from '@/types/components/FormElement'

interface SearchBarProps {
  placeholder?: string
}

export default function SearchBar({
  placeholder = 'Search recipes, television shows, magazine articles, etc.',
}: SearchBarProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const formRef = useRef<FormElementImperativeHandle>(null)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // This is a placeholder - search logic will be implemented later
    // eslint-disable-next-line
    console.log('Search for:', searchTerm)
  }

  const handleChange = (value: string) => {
    setSearchTerm(value)
  }

  return (
    <form
      onSubmit={handleSearch}
      className={styles.searchBar}
    >
      <FormElement
        ref={formRef}
        element="input"
        type="text"
        name="search"
        placeholder={placeholder}
        className={styles.formElement}
        initialValue={searchTerm}
        onChange={handleChange}
        isRounded={true}
        theme="gray"
        buttonIcon="magnifyingGlass"
        buttonOnClick={() => {}}
      />
    </form>
  )
}
