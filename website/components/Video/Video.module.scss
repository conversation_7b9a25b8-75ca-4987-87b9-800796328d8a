/* stylelint-disable-next-line block-no-empty */
.Video {
  width: 100%;
  position: relative;
  aspect-ratio: var(--aspect-ratio);
}

.iframe,
.videoElement {
  @include position-100(absolute);
  border: none;
}

.previewContainer {
  @include position-100(absolute);
  z-index: 2;
  object-fit: cover;
}

.previewImage {
  @include position-100(absolute);
  z-index: 2;
  object-fit: cover;
}

.playButton {
  position: absolute;
  z-index: 3;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
