'use client'

import classnames from 'classnames'
import styles from './Video.module.scss'
import { CSSProperties, useMemo, useState } from 'react'
import SanityImage from '../SanityImage/SanityImage'
import Button from '../Button/Button'

const Video = ({ className, vimeoId, youtubeId, url, aspectRatio = '16:9', type, previewImage }: VideoProps) => {
  aspectRatio = aspectRatio || '16:9'
  const [clickedPlay, setClickedPlay] = useState(false)
  const requiresPlayClick = useMemo(() => (previewImage ? true : false), [previewImage])
  const formattedVimeoUrl = useMemo(() => {
    const baseUrl = `https://player.vimeo.com/video/${vimeoId}`
    if (requiresPlayClick) {
      return `${baseUrl}?autoplay=1`
    }
    return baseUrl
  }, [requiresPlayClick, vimeoId])

  const formattedYoutubeUrl = useMemo(() => {
    const baseUrl = `https://www.youtube.com/embed/${youtubeId}`
    if (requiresPlayClick) {
      return `${baseUrl}?autoplay=1`
    }
    return baseUrl
  }, [requiresPlayClick, youtubeId])

  const aspectRatioWithSlash = useMemo(() => {
    return aspectRatio?.split(':').join('/')
  }, [aspectRatio])

  const aspectRatioAsNumber = useMemo(() => {
    const eachValue = aspectRatio?.split(':').map(value => parseInt(value))
    return eachValue[0] / eachValue[1]
  }, [aspectRatio])

  if (!type) return null

  return (
    <div
      className={classnames(styles.Video, className)}
      style={
        {
          '--aspect-ratio': aspectRatioWithSlash,
        } as CSSProperties
      }
    >
      {((requiresPlayClick && clickedPlay) || !requiresPlayClick) && (
        <>
          {type === 'youtube' && youtubeId && (
            <iframe
              src={formattedYoutubeUrl}
              frameBorder={0}
              allowFullScreen
              className={styles.iframe}
              allow="autoplay"
            ></iframe>
          )}
          {type === 'vimeo' && vimeoId && (
            <iframe
              src={formattedVimeoUrl}
              frameBorder={0}
              allowFullScreen
              className={styles.iframe}
              allow="autoplay"
            ></iframe>
          )}
          {type === 'url' && url && (
            <video
              src={url}
              className={styles.videoElement}
              controls
              autoPlay={requiresPlayClick}
            />
          )}
        </>
      )}

      {previewImage && !clickedPlay && (
        <div
          className={styles.previewContainer}
          onClick={() => setClickedPlay(true)}
        >
          <SanityImage
            source={previewImage}
            className={styles.previewImage}
            aspectRatio={aspectRatioAsNumber}
            columns={6}
          />
          <Button
            icon="play"
            iconOnlySize="lg"
            className={styles.playButton}
          />
        </div>
      )}
    </div>
  )
}

Video.displayName = 'Video'

export default Video
