import type { Meta, StoryObj } from '@storybook/react'
import Video from './Video'
import { STUB_VIDEO_DATA } from './Video.stub'
import { SANITY_IMAGE_STUB_SOURCE } from '../SanityImage/SanityImage.stub'

const meta = {
  title: 'Components/Video',
  component: Video,
  parameters: {
    layout: 'fullscreen',
  },
} satisfies Meta<typeof Video>

export default meta

type Story = StoryObj<typeof meta>

export const Youtube: Story = {
  args: {
    type: 'youtube',
    youtubeId: STUB_VIDEO_DATA.YOUTUBE,
  },
}

export const Vimeo: Story = {
  args: {
    type: 'vimeo',
    vimeoId: STUB_VIDEO_DATA.VIMEO,
  },
}

export const URL: Story = {
  args: {
    type: 'url',
    url: STUB_VIDEO_DATA.URL,
  },
}

export const WithPreviewImage: Story = {
  args: {
    type: 'url',
    url: STUB_VIDEO_DATA.URL,
    previewImage: SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
  },
}
