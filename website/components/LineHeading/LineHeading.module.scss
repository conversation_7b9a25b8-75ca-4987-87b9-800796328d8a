/* stylelint-disable block-no-empty */
.LineHeading {
  border-bottom: 2px solid var(--slate);
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: px(8);
  padding-bottom: px(8);

  &[data-line-heading-size='sm'] {
    border-bottom: 1px solid rgba($navy-dark, 0.2);
  }

  &[data-disable-line='true'] {
    border-bottom: none;
  }
}

.title {
  @include font-heading-eyebrow-lg;
  color: var(--navy-dark);

  [data-line-heading-size='sm'] & {
    @include font-heading-eyebrow-md;
  }

  [data-line-heading-size='md'] & {
    @include font-heading-eyebrow-lg;
  }
}
