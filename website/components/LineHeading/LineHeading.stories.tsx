import type { Meta, StoryObj } from '@storybook/react'
import LineHeading from './LineHeading'

const meta = {
  title: 'Components/LineHeading',
  component: LineHeading,
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof LineHeading>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    title: 'Ingredients',
  },
}

export const WithButton: Story = {
  args: {
    title: 'Ingredients',
    buttonLabel: 'Copy',
    buttonIcon: 'copy',
    buttonCallback: () => alert('Copied!'),
  },
}

export const SmallVariant: Story = {
  args: {
    title: 'Program Overview',
    size: 'sm',
  },
}

export const IngredientsWithContent: Story = {
  render: args => (
    <div style={{ width: 420, background: '#fff', padding: '1rem' }}>
      <LineHeading {...args} />
      <ul>
        <li>2 cups flour</li>
        <li>1 tsp salt</li>
        <li>1/2 cup water</li>
      </ul>
    </div>
  ),
  args: {
    title: 'Ingredients',
    buttonLabel: 'Copy',
    buttonIcon: 'copy',
  },
}

export const ProgramOverviewWithContent: Story = {
  render: args => (
    <div style={{ width: 420, background: '#fff', padding: '1rem' }}>
      <LineHeading {...args} />
      <div>
        <div>
          <strong>Season Premiere:</strong> September 2024
        </div>
        <div>
          <strong>Number of Episodes:</strong> 23
        </div>
        <div>
          <strong>Episode Length:</strong> 30 minutes
        </div>
        <div>
          <strong>Website:</strong> 177milkstreet.com/TV
        </div>
      </div>
    </div>
  ),
  args: {
    title: 'Program Overview',
    size: 'sm',
  },
}

export const MultipleLineHeadings: Story = {
  args: {
    title: 'anystring',
  },
  render: _ => (
    <div
      style={{ width: 420, background: '#fff', padding: '1rem', display: 'flex', flexDirection: 'column', gap: '1rem' }}
    >
      <LineHeading
        title="Ingredients"
        buttonLabel="Copy"
        buttonIcon="copy"
      />
      <div>
        <LineHeading
          title="For the Dough"
          size="sm"
        />
        <div
          style={{
            display: 'flex',
            gap: '1rem',
          }}
        >
          <span style={{ fontWeight: 700 }}>2</span>
          <span>large eggs plus 2 large egg yolks, room temperature</span>
        </div>
      </div>
      <div>
        <LineHeading
          title="For the Filling"
          size="sm"
        />
        {[1, 2, 3, 4].map(i => (
          <div
            key={i}
            style={{
              display: 'flex',
              gap: '1rem',
            }}
          >
            <span style={{ fontWeight: 700 }}>2</span>
            <span>ingredient item</span>
          </div>
        ))}
      </div>
    </div>
  ),
}
