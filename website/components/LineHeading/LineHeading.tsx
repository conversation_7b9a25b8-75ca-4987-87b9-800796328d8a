'use client'

import classnames from 'classnames'
import styles from './LineHeading.module.scss'
import { LineHeadingProps } from '@/types/components/LineHeadingProps'
import Button from '../Button/Button'

const LineHeading = ({
  title,
  buttonLabel,
  buttonIcon,
  disableLine = false,
  buttonCallback,
  size = 'md',
  className,
}: LineHeadingProps) => {
  return (
    <div
      data-line-heading-size={size}
      className={classnames(styles.LineHeading, className)}
      data-disable-line={disableLine}
    >
      {title && <span className={styles.title}>{title}</span>}
      {Boolean(buttonLabel && buttonCallback) && (
        <Button
          className={styles.button}
          label={buttonLabel}
          icon={buttonIcon}
          style="bare"
          ariaLabel={buttonLabel}
          onClick={buttonCallback}
          labelSize={size === 'sm' ? 'sm' : 'md'}
        />
      )}
    </div>
  )
}

LineHeading.displayName = 'LineHeading'

export default LineHeading
