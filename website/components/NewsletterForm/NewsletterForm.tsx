'use client'

import { FooterNewsletter } from '@/types/index.d'
import styles from './NewsletterForm.module.scss'

interface NewsletterFormProps {
  newsletter: FooterNewsletter
}

export default function NewsletterForm({ newsletter }: NewsletterFormProps) {
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const form = e.currentTarget
    const email = (form.elements.namedItem('email') as HTMLInputElement).value

    // TODO: Implement newsletter signup logic
    // eslint-disable-next-line
    console.log('Newsletter signup:', email)
  }

  return (
    <form
      onSubmit={handleSubmit}
      className={styles.NewsletterForm}
    >
      <div className={styles.inner}>
        <input
          type="email"
          name="email"
          placeholder={newsletter?.placeholderText || 'Email...'}
          className={styles.input}
        />
        <button
          type="submit"
          className={styles.button}
          aria-label="Subscribe to newsletter"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={styles.svg}
          >
            <path
              d="M9 5l7 7-7 7"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>
      <p className={styles.description}>
        {newsletter?.description ||
          'You will receive special offers from Milk Street. You can unsubscribe from our emails at any time.'}
      </p>
    </form>
  )
}
