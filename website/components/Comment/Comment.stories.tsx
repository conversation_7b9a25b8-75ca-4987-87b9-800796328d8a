import type { Meta, StoryObj } from '@storybook/react'
import Comment from './Comment'

const meta = {
  title: 'Components/Comment',
  component: Comment,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    author: { control: 'text' },
    date: { control: 'text' },
    title: { control: 'text' },
    description: { control: 'text' },
    ratingPercent: {
      control: { type: 'range', min: 0, max: 1, step: 0.1 },
      description: 'Value between 0-1 representing the rating percentage',
    },
  },
  decorators: [
    Story => (
      <div style={{ maxWidth: '800px', width: '100%' }}>
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof Comment>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    author: '<PERSON>',
    date: '2023-03-26T12:00:00Z',
    title: 'Wonderful, wonderful!',
    description:
      'Yes, I can see why Milk Street hailed this as one of their favorite recipes. I followed the recipe to a T (zucchini version) and just loved it. The flavors were balanced and developed, and it all came together relatively quickly. Lime and chilis at the end (plus I added cilantro) is a must!',
    ratingPercent: 1,
  },
}

export const WithoutRating: Story = {
  args: {
    author: '<PERSON> K.',
    date: '2023-03-26T12:00:00Z',
    title: 'Wonderful, wonderful!',
    description:
      'Yes, I can see why Milk Street hailed this as one of their favorite recipes. I followed the recipe to a T (zucchini version) and just loved it. The flavors were balanced and developed, and it all came together relatively quickly. Lime and chilis at the end (plus I added cilantro) is a must!',
  },
}

export const WithoutDate: Story = {
  args: {
    author: 'Margaret K.',
    title: 'Wonderful, wonderful!',
    description:
      'Yes, I can see why Milk Street hailed this as one of their favorite recipes. I followed the recipe to a T (zucchini version) and just loved it. The flavors were balanced and developed, and it all came together relatively quickly. Lime and chilis at the end (plus I added cilantro) is a must!',
    ratingPercent: 0.8,
  },
}

export const MultipleComments: Story = {
  args: {
    author: 'Multiple Authors',
    title: 'Multiple Comments Example',
    description: 'This story shows multiple comments rendered together',
  },
  render: () => (
    <>
      <Comment
        author="Margaret K."
        date="2023-03-26T12:00:00Z"
        title="Wonderful, wonderful!"
        description="Yes, I can see why Milk Street hailed this as one of their favorite recipes. I followed the recipe to a T (zucchini version) and just loved it. The flavors were balanced and developed, and it all came together relatively quickly. Lime and chilis at the end (plus I added cilantro) is a must!"
        ratingPercent={1}
      />
      <Comment
        author="John D."
        date="2023-02-15T12:00:00Z"
        title="Great recipe!"
        description="This was delicious and easy to make. Will definitely make it again."
        ratingPercent={0.8}
      />
      <Comment
        author="Sarah M."
        date="2023-01-10T12:00:00Z"
        title="Loved it!"
        description="My family enjoyed this recipe. I made a few modifications to suit our taste preferences."
        ratingPercent={0.6}
      />
    </>
  ),
}
