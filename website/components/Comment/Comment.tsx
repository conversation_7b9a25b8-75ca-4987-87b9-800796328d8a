'use client'

import classnames from 'classnames'
import styles from './Comment.module.scss'
import StarsRating from '@/components/StarsRating/StarsRating'
import { formatDate } from '@/lib/utils/date'

const Comment = ({ author, date, title, description, ratingPercent, className }: CommentProps) => {
  if (!title || !author || !description) return null

  return (
    <article className={classnames(styles.Comment, className)}>
      {ratingPercent && (
        <div className={styles.rating}>
          <StarsRating
            percent={ratingPercent}
            size="md"
          />
        </div>
      )}
      <div className={styles.header}>
        <h3 className={styles.author}>{author}</h3>
        {date && <span className={styles.date}>{formatDate(date)}</span>}
      </div>
      <p className={styles.title}>{title}</p>
      <p className={styles.description}>{description}</p>
    </article>
  )
}

Comment.displayName = 'Comment'

export default Comment
