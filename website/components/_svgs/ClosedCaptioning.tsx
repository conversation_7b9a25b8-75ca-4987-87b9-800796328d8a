import classNames from 'classnames'

const ClosedCaptioningSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 32 33"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1659_37895)">
        <path
          d="M25.3333 5.771H6.66667C5.18667 5.771 4 6.971 4 8.43766V24.4377C4 25.9043 5.18667 27.1043 6.66667 27.1043H25.3333C26.8 27.1043 28 25.9043 28 24.4377V8.43766C28 6.971 26.8 5.771 25.3333 5.771ZM14.6667 15.1043H12.6667V14.4377H10V18.4377H12.6667V17.771H14.6667V19.1043C14.6667 19.8377 14.0667 20.4377 13.3333 20.4377H9.33333C8.6 20.4377 8 19.8377 8 19.1043V13.771C8 13.0377 8.6 12.4377 9.33333 12.4377H13.3333C14.0667 12.4377 14.6667 13.0377 14.6667 13.771V15.1043ZM24 15.1043H22V14.4377H19.3333V18.4377H22V17.771H24V19.1043C24 19.8377 23.4 20.4377 22.6667 20.4377H18.6667C17.9333 20.4377 17.3333 19.8377 17.3333 19.1043V13.771C17.3333 13.0377 17.9333 12.4377 18.6667 12.4377H22.6667C23.4 12.4377 24 13.0377 24 13.771V15.1043Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1659_37895">
          <rect
            width="32"
            height="32"
            fill="white"
            transform="translate(0 0.4375)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

ClosedCaptioningSvg.displayName = 'ClosedCaptioningSvg'

export default ClosedCaptioningSvg
