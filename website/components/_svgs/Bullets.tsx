import classNames from 'classnames'

const BulletsSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.5 4.498H23.5"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.5 12.498H23.5"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.5 20.498H23.5"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.5 2.498H1.5C0.947715 2.498 0.5 2.94571 0.5 3.498V5.498C0.5 6.05028 0.947715 6.498 1.5 6.498H3.5C4.05228 6.498 4.5 6.05028 4.5 5.498V3.498C4.5 2.94571 4.05228 2.498 3.5 2.498Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.5 10.498H1.5C0.947715 10.498 0.5 10.9457 0.5 11.498V13.498C0.5 14.0503 0.947715 14.498 1.5 14.498H3.5C4.05228 14.498 4.5 14.0503 4.5 13.498V11.498C4.5 10.9457 4.05228 10.498 3.5 10.498Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.5 18.498H1.5C0.947715 18.498 0.5 18.9457 0.5 19.498V21.498C0.5 22.0503 0.947715 22.498 1.5 22.498H3.5C4.05228 22.498 4.5 22.0503 4.5 21.498V19.498C4.5 18.9457 4.05228 18.498 3.5 18.498Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

BulletsSvg.displayName = 'BulletsSvg'

export default BulletsSvg
