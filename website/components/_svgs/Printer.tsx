import classNames from 'classnames'

const PrinterSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.1253 7.12499H18.375V3.74999C18.375 3.65054 18.3355 3.55515 18.2652 3.48483C18.1948 3.4145 18.0995 3.37499 18 3.37499H6C5.90055 3.37499 5.80516 3.4145 5.73484 3.48483C5.66451 3.55515 5.625 3.65054 5.625 3.74999V7.12499H3.87469C2.77219 7.12499 1.875 7.96874 1.875 8.99999V16.5C1.875 16.5995 1.91451 16.6948 1.98484 16.7652C2.05516 16.8355 2.15055 16.875 2.25 16.875H5.625V20.25C5.625 20.3495 5.66451 20.4448 5.73484 20.5152C5.80516 20.5855 5.90055 20.625 6 20.625H18C18.0995 20.625 18.1948 20.5855 18.2652 20.5152C18.3355 20.4448 18.375 20.3495 18.375 20.25V16.875H21.75C21.8495 16.875 21.9448 16.8355 22.0152 16.7652C22.0855 16.6948 22.125 16.5995 22.125 16.5V8.99999C22.125 7.96874 21.2278 7.12499 20.1253 7.12499ZM6.375 4.12499H17.625V7.12499H6.375V4.12499ZM17.625 19.875H6.375V14.625H17.625V19.875ZM21.375 16.125H18.375V14.25C18.375 14.1505 18.3355 14.0552 18.2652 13.9848C18.1948 13.9145 18.0995 13.875 18 13.875H6C5.90055 13.875 5.80516 13.9145 5.73484 13.9848C5.66451 14.0552 5.625 14.1505 5.625 14.25V16.125H2.625V8.99999C2.625 8.37937 3.1875 7.87499 3.87469 7.87499H20.1253C20.8125 7.87499 21.375 8.37937 21.375 8.99999V16.125ZM18.375 10.875C18.375 11.0233 18.331 11.1683 18.2486 11.2917C18.1662 11.415 18.0491 11.5111 17.912 11.5679C17.775 11.6247 17.6242 11.6395 17.4787 11.6106C17.3332 11.5816 17.1996 11.5102 17.0947 11.4053C16.9898 11.3004 16.9184 11.1668 16.8894 11.0213C16.8605 10.8758 16.8753 10.725 16.9321 10.588C16.9889 10.4509 17.085 10.3338 17.2083 10.2514C17.3317 10.169 17.4767 10.125 17.625 10.125C17.8239 10.125 18.0147 10.204 18.1553 10.3447C18.296 10.4853 18.375 10.6761 18.375 10.875Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.66413 3.41412C5.75321 3.32504 5.87402 3.27499 6 3.27499H18C18.126 3.27499 18.2468 3.32504 18.3359 3.41412C18.425 3.5032 18.475 3.62402 18.475 3.74999V7.02499H20.1253C21.277 7.02499 22.225 7.90763 22.225 8.99999V16.5C22.225 16.626 22.175 16.7468 22.0859 16.8359C21.9968 16.925 21.876 16.975 21.75 16.975H18.475V20.25C18.475 20.376 18.425 20.4968 18.3359 20.5859C18.2468 20.6749 18.126 20.725 18 20.725H6C5.87402 20.725 5.75321 20.6749 5.66413 20.5859C5.57505 20.4968 5.525 20.376 5.525 20.25V16.975H2.25C2.12402 16.975 2.00321 16.925 1.91413 16.8359C1.82505 16.7468 1.775 16.626 1.775 16.5V8.99999C1.775 7.90763 2.72303 7.02499 3.87469 7.02499H5.525V3.74999C5.525 3.62402 5.57505 3.5032 5.66413 3.41412ZM6 3.47499C5.92707 3.47499 5.85712 3.50397 5.80555 3.55554C5.75397 3.60711 5.725 3.67706 5.725 3.74999V7.22499H3.87469C2.82134 7.22499 1.975 8.02986 1.975 8.99999V16.5C1.975 16.5729 2.00397 16.6429 2.05555 16.6944C2.10712 16.746 2.17707 16.775 2.25 16.775H5.725V20.25C5.725 20.3229 5.75397 20.3929 5.80555 20.4444C5.85712 20.496 5.92707 20.525 6 20.525H18C18.0729 20.525 18.1429 20.496 18.1945 20.4444C18.246 20.3929 18.275 20.3229 18.275 20.25V16.775H21.75C21.8229 16.775 21.8929 16.746 21.9445 16.6944C21.996 16.6429 22.025 16.5729 22.025 16.5V8.99999C22.025 8.02986 21.1787 7.22499 20.1253 7.22499H18.275V3.74999C18.275 3.67706 18.246 3.60711 18.1945 3.55554C18.1429 3.50397 18.0729 3.47499 18 3.47499H6ZM6.275 4.02499H17.725V7.22499H6.275V4.02499ZM6.475 4.22499V7.02499H17.525V4.22499H6.475ZM3.87469 7.97499C3.23246 7.97499 2.725 8.44433 2.725 8.99999V16.025H5.525V14.25C5.525 14.124 5.57505 14.0032 5.66413 13.9141C5.75321 13.825 5.87402 13.775 6 13.775H18C18.126 13.775 18.2468 13.825 18.3359 13.9141C18.425 14.0032 18.475 14.124 18.475 14.25V16.025H21.275V8.99999C21.275 8.44433 20.7675 7.97499 20.1253 7.97499H3.87469ZM2.525 8.99999C2.525 8.3144 3.14254 7.77499 3.87469 7.77499H20.1253C20.8575 7.77499 21.475 8.3144 21.475 8.99999V16.225H18.275V14.25C18.275 14.1771 18.246 14.1071 18.1945 14.0555C18.1429 14.004 18.0729 13.975 18 13.975H6C5.92707 13.975 5.85712 14.004 5.80555 14.0555C5.75397 14.1071 5.725 14.1771 5.725 14.25V16.225H2.525V8.99999ZM17.625 10.225C17.4964 10.225 17.3708 10.2631 17.2639 10.3345C17.157 10.406 17.0737 10.5075 17.0245 10.6262C16.9753 10.745 16.9624 10.8757 16.9875 11.0018C17.0126 11.1279 17.0745 11.2437 17.1654 11.3346C17.2563 11.4255 17.3721 11.4874 17.4982 11.5125C17.6243 11.5376 17.755 11.5247 17.8737 11.4755C17.9925 11.4263 18.094 11.343 18.1655 11.2361C18.2369 11.1292 18.275 11.0036 18.275 10.875C18.275 10.7026 18.2065 10.5373 18.0846 10.4154C17.9627 10.2935 17.7974 10.225 17.625 10.225ZM17.1528 10.1682C17.2925 10.0748 17.4569 10.025 17.625 10.025C17.8504 10.025 18.0666 10.1145 18.226 10.274C18.3854 10.4334 18.475 10.6496 18.475 10.875C18.475 11.0431 18.4252 11.2074 18.3318 11.3472C18.2384 11.487 18.1056 11.596 17.9503 11.6603C17.795 11.7246 17.6241 11.7415 17.4592 11.7087C17.2943 11.6759 17.1428 11.5949 17.024 11.476C16.9051 11.3572 16.8241 11.2057 16.7913 11.0408C16.7585 10.8759 16.7754 10.705 16.8397 10.5497C16.904 10.3944 17.013 10.2616 17.1528 10.1682ZM6.275 14.525H17.725V19.975H6.275V14.525ZM6.475 14.725V19.775H17.525V14.725H6.475Z"
        fill="currentColor"
      />
    </svg>
  )
}

PrinterSvg.displayName = 'PrinterSvg'

export default PrinterSvg
