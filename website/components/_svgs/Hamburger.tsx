import classNames from 'classnames'

const HamburgerSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <line
        y1="6.5"
        x2="24"
        y2="6.5"
        stroke="currentColor"
      />
      <line
        y1="11.5"
        x2="24"
        y2="11.5"
        stroke="currentColor"
      />
      <line
        y1="16.5"
        x2="24"
        y2="16.5"
        stroke="currentColor"
      />
    </svg>
  )
}

HamburgerSvg.displayName = 'HamburgerSvg'

export default HamburgerSvg
