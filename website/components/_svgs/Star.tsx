import classNames from 'classnames'

const StarSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.376 4.70508L7.56152 4.30225L6 0.799805L4.43848 4.30225L0.624023 4.70508L3.47314 7.27295L2.67773 11.0244L6 9.1084L9.32227 11.0244L8.52686 7.27295L11.376 4.70508Z"
        fill="currentColor"
      />
    </svg>
  )
}

StarSvg.displayName = 'StarSvg'

export default StarSvg
