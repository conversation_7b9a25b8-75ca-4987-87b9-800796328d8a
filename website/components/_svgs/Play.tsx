import classNames from 'classnames'

const PlaySvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 36 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1659_37882)">
        <path
          d="M12 7.9375V28.9375L28.5 18.4375L12 7.9375Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1659_37882">
          <rect
            width="36"
            height="36"
            fill="white"
            transform="translate(0 0.4375)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

PlaySvg.displayName = 'PlaySvg'

export default PlaySvg
