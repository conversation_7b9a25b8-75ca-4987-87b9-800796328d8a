import classNames from 'classnames'

const TuneInSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1175_56395)">
        <path
          d="M7.66 10.3983V11.1403C7.66 11.2453 7.55 11.2453 7.55 11.2453H6.703C6.703 11.2453 6.593 11.2453 6.593 11.3553V15.3853C6.593 15.4953 6.488 15.4953 6.488 15.4953H5.633C5.527 15.4953 5.527 15.3853 5.527 15.3853V11.3553C5.527 11.3553 5.527 11.2453 5.418 11.2453H4.574C4.469 11.2453 4.469 11.1403 4.469 11.1403V10.3983C4.469 10.2923 4.574 10.2923 4.574 10.2923H7.66V10.3983ZM23.118 2.87825H12.301C11.621 2.87825 11.465 3.03825 11.465 3.69425V6.10825C11.465 6.60125 11.465 6.60125 10.973 6.60125H0.813C0.137 6.60025 0 6.73725 0 7.42525V12.8353C0 14.5893 0 16.3433 0.023 18.1013C0.023 19.0233 0.125 19.1213 1.063 19.1213H9.89C10.554 19.1213 11.21 19.1313 11.874 19.1113C12.354 19.1053 12.543 18.9093 12.543 18.4293V15.8693C12.543 15.4013 12.543 15.4013 13.012 15.4013H23.207C23.84 15.4013 24 15.2493 24 14.6213V3.73625C24 3.03625 23.836 2.87625 23.117 2.87625L23.118 2.87825ZM11.478 17.5033C11.478 18.0033 11.465 18.0033 10.953 18.0033C7.805 18.0033 4.66 18.0033 1.508 18.0113C1.188 18.0113 1.078 17.9333 1.078 17.5933C1.094 14.4333 1.086 11.2693 1.078 8.11325C1.07 7.77325 1.164 7.66725 1.52 7.66725C4.707 7.67925 7.883 7.67525 11.07 7.67525C11.187 7.67525 11.3 7.69025 11.47 7.69825C11.47 7.87825 11.47 8.01825 11.48 8.14025C11.477 11.2533 11.478 14.3823 11.478 17.5033ZM19.168 11.6593C19.168 11.7613 19.064 11.7613 19.064 11.7613H16.494C16.388 11.7613 16.388 11.6593 16.388 11.6593V10.9393C16.388 10.8393 16.493 10.8393 16.493 10.8393H17.11C17.11 10.8393 17.212 10.8393 17.212 10.7373V7.65925C17.212 7.65925 17.212 7.55825 17.11 7.55825H16.595C16.493 7.55825 16.493 7.45625 16.493 7.45625V6.63625C16.493 6.53025 16.595 6.53025 16.595 6.53025H18.962C19.064 6.53025 19.064 6.63625 19.064 6.63625V7.35125C19.064 7.45625 18.962 7.45625 18.962 7.45625H18.446C18.446 7.45625 18.345 7.45625 18.345 7.55825V10.6323C18.345 10.6323 18.345 10.7373 18.445 10.7373H19.063C19.169 10.7373 19.169 10.8393 19.169 10.8393L19.168 11.6593Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1175_56395">
          <rect
            width="24"
            height="24"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

TuneInSvg.displayName = 'TuneInSvg'

export default TuneInSvg
