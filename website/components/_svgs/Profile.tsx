import classNames from 'classnames'

const ProfileSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.6123 12.7679C15.8071 13.1186 16.9069 13.7646 17.8033 14.661C19.0915 15.9492 19.8627 17.6578 19.9833 19.4643H5.01667C5.1373 17.6578 5.90849 15.9492 7.1967 14.661C8.09304 13.7646 9.19289 13.1186 10.3877 12.7679C10.0057 12.5832 9.6517 12.3499 9.33352 12.0761C8.27853 12.4996 7.30902 13.1344 6.48959 13.9539C4.89553 15.5479 4 17.7099 4 19.9643C4 20.0969 4.05268 20.2241 4.14645 20.3178C4.24021 20.4116 4.36739 20.4643 4.5 20.4643H20.5C20.6326 20.4643 20.7598 20.4116 20.8536 20.3178C20.9473 20.2241 21 20.0969 21 19.9643C21 17.7099 20.1045 15.5479 18.5104 13.9539C17.691 13.1344 16.7214 12.4996 15.6664 12.0761C15.3483 12.3499 14.9942 12.5831 14.6123 12.7679Z"
        fill="currentColor"
      />
      <path
        d="M12.5 13.25C15.1825 13.25 17.3571 11.0754 17.3571 8.39286C17.3571 5.71033 15.1825 3.53572 12.5 3.53572C9.81744 3.53572 7.64282 5.71033 7.64282 8.39286C7.64282 11.0754 9.81744 13.25 12.5 13.25Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

ProfileSvg.displayName = 'ProfileSvg'

export default ProfileSvg
