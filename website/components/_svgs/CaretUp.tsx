import classNames from 'classnames'

const CaretUpSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.6879 15L11.8505 9.53217C11.8301 9.50919 11.805 9.49079 11.777 9.47818C11.749 9.46558 11.7186 9.45907 11.6879 9.45907C11.6571 9.45907 11.6268 9.46558 11.5987 9.47818C11.5707 9.49079 11.5457 9.50919 11.5253 9.53217L6.68787 15"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

CaretUpSvg.displayName = 'CaretUpSvg'

export default CaretUpSvg
