import classNames from 'classnames'

const MagnifyingGlassSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.56522 14.8183C12.6388 14.8183 15.1304 12.3267 15.1304 9.25308C15.1304 6.1795 12.6388 3.68787 9.56522 3.68787C6.49163 3.68787 4 6.1795 4 9.25308C4 12.3267 6.49163 14.8183 9.56522 14.8183Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.4998 13.1877L20 19.6879"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

MagnifyingGlassSvg.displayName = 'MagnifyingGlassSvg'

export default MagnifyingGlassSvg
