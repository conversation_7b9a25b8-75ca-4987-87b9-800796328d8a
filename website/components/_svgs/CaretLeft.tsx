import classNames from 'classnames'

const CaretLeftSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.5409 16.6879L9.07311 11.8505C9.05012 11.8301 9.03172 11.805 9.01912 11.777C9.00652 11.749 9 11.7186 9 11.6879C9 11.6571 9.00652 11.6268 9.01912 11.5987C9.03172 11.5707 9.05012 11.5457 9.07311 11.5253L14.5409 6.68787"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

CaretLeftSvg.displayName = 'CaretLeftSvg'

export default CaretLeftSvg
