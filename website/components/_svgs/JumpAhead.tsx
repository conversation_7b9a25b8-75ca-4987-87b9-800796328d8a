import classNames from 'classnames'

const JumpAheadSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 36 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1659_37885)">
        <path
          d="M18 7.9375V1.9375L25.5 9.4375L18 16.9375V10.9375C13.035 10.9375 9 14.9725 9 19.9375C9 24.9025 13.035 28.9375 18 28.9375C22.965 28.9375 27 24.9025 27 19.9375H30C30 26.5675 24.63 31.9375 18 31.9375C11.37 31.9375 6 26.5675 6 19.9375C6 13.3075 11.37 7.9375 18 7.9375Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1659_37885">
          <rect
            width="36"
            height="36"
            fill="white"
            transform="matrix(-1 0 0 1 36 0.4375)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

JumpAheadSvg.displayName = 'JumpAheadSvg'

export default JumpAheadSvg
