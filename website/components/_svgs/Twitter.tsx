import classNames from 'classnames'

const TwitterSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 150 150"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_263_128)">
        <path
          d="M46.2891 39.6899L96.0891 104.92H103.709L54.4891 39.6899H46.2891Z"
          fill="currentColor"
        />
        <path
          d="M75 0C60.1664 0 45.666 4.39866 33.3323 12.6398C20.9986 20.8809 11.3856 32.5943 5.70899 46.2987C0.0324159 60.0032 -1.45273 75.0832 1.44117 89.6318C4.33506 104.18 11.4781 117.544 21.967 128.033C32.456 138.522 45.8197 145.665 60.3683 148.559C74.9169 151.453 89.9968 149.968 103.701 144.291C117.406 138.614 129.119 129.001 137.36 116.668C145.601 104.334 150 89.8336 150 75C150 55.1088 142.098 36.0322 128.033 21.967C113.968 7.90175 94.8912 0 75 0ZM92.38 113.12L70.5 84.8L45.7 113.12H31.82L64.05 76.4L30.0501 31.88H58.57L78.1 57.88L100.95 31.88H114.62L84.55 66.45L120.09 113.12H92.38Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_263_128">
          <rect
            width="150"
            height="150"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

TwitterSvg.displayName = 'TwitterSvg'

export default TwitterSvg
