import classNames from 'classnames'

const BookmarkFilledSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.265 17.71L12 17.5445L11.735 17.7101L6.1325 21.2116C6.09466 21.2353 6.05119 21.2484 6.0066 21.2495C5.96199 21.2507 5.91789 21.2399 5.87886 21.2183C5.83983 21.1967 5.8073 21.165 5.78464 21.1266C5.76198 21.0882 5.75002 21.0444 5.75 20.9998L5.75 4.5C5.75 4.23478 5.85536 3.98043 6.04289 3.79289C6.23043 3.60536 6.48478 3.5 6.75 3.5H17.25C17.5152 3.5 17.7696 3.60536 17.9571 3.79289L18.3098 3.44019L17.9571 3.79289C18.1446 3.98043 18.25 4.23478 18.25 4.5V20.9994C18.2499 21.0439 18.238 21.0877 18.2153 21.1261C18.1927 21.1644 18.1602 21.1961 18.1212 21.2177C18.0822 21.2393 18.0382 21.2501 17.9936 21.249C17.9492 21.2478 17.9059 21.2349 17.8682 21.2115C17.8681 21.2114 17.8679 21.2113 17.8678 21.2112L12.265 17.71Z"
        fill="currentColor"
        stroke="currentColor"
      />
    </svg>
  )
}

BookmarkFilledSvg.displayName = 'BookmarkFilledSvg'

export default BookmarkFilledSvg
