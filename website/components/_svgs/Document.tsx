import classNames from 'classnames'

const DocumentSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1175_56528)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.80001 4C4.80001 3.55817 5.15819 3.2 5.60001 3.2H14.5373C14.7494 3.2 14.9529 3.28429 15.103 3.43432L18.9658 7.29705C19.1157 7.44709 19.2 7.65057 19.2 7.86273V20C19.2 20.4418 18.8418 20.8 18.4 20.8H5.60001C5.15819 20.8 4.80001 20.4418 4.80001 20V4ZM5.60001 1.6C4.27452 1.6 3.20001 2.67451 3.20001 4V20C3.20001 21.3254 4.27452 22.4 5.60001 22.4H18.4C19.7255 22.4 20.8 21.3254 20.8 20V7.86273C20.8 7.22622 20.5472 6.61577 20.0971 6.16568L16.2343 2.30294C15.7842 1.85286 15.1738 1.6 14.5373 1.6H5.60001ZM7.20001 6.4C6.75819 6.4 6.40001 6.75817 6.40001 7.2C6.40001 7.64182 6.75819 8 7.20001 8H12C12.4418 8 12.8 7.64182 12.8 7.2C12.8 6.75817 12.4418 6.4 12 6.4H7.20001ZM7.20001 11.2C6.75819 11.2 6.40001 11.5582 6.40001 12C6.40001 12.4418 6.75819 12.8 7.20001 12.8H16.8C17.2418 12.8 17.6 12.4418 17.6 12C17.6 11.5582 17.2418 11.2 16.8 11.2H7.20001ZM7.20001 16C6.75819 16 6.40001 16.3582 6.40001 16.8C6.40001 17.2418 6.75819 17.6 7.20001 17.6H16.8C17.2418 17.6 17.6 17.2418 17.6 16.8C17.6 16.3582 17.2418 16 16.8 16H7.20001Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1175_56528">
          <rect
            width="24"
            height="24"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

DocumentSvg.displayName = 'DocumentSvg'

export default DocumentSvg
