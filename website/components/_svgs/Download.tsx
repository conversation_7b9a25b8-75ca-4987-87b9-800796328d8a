import classNames from 'classnames'

const DownloadSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1175_56527)">
        <path
          d="M11.3333 4V15.0417L7.8125 11.5208L6.85417 12.4792L11.5208 17.1458L12 17.6042L12.4792 17.1458L17.1458 12.4792L16.1875 11.5208L12.6667 15.0417V4H11.3333ZM6 18.6667V20H18V18.6667H6Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1175_56527">
          <rect
            width="24"
            height="24"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

DownloadSvg.displayName = 'DownloadSvg'

export default DownloadSvg
