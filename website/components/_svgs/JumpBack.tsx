import classNames from 'classnames'

const JumpBackSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 36 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1659_37879)">
        <path
          d="M18 7.9375V1.9375L10.5 9.4375L18 16.9375V10.9375C22.965 10.9375 27 14.9725 27 19.9375C27 24.9025 22.965 28.9375 18 28.9375C13.035 28.9375 9 24.9025 9 19.9375H6C6 26.5675 11.37 31.9375 18 31.9375C24.63 31.9375 30 26.5675 30 19.9375C30 13.3075 24.63 7.9375 18 7.9375Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1659_37879">
          <rect
            width="36"
            height="36"
            fill="white"
            transform="translate(0 0.4375)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

JumpBackSvg.displayName = 'JumpBackSvg'

export default JumpBackSvg
