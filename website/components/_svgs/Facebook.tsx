import classNames from 'classnames'

const FacebookSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 30 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_901_23799)">
        <path
          d="M29.719 15.6777C29.719 22.9437 24.387 28.9777 17.414 30.0327V19.8977H20.812L21.457 15.6787H17.414V12.9837C17.414 11.8117 18 10.6987 19.816 10.6987H21.633V7.12273C21.633 7.12273 19.993 6.82973 18.352 6.82973C15.07 6.82973 12.902 8.88073 12.902 12.5137V15.6777H9.21101V19.8967H12.902V30.0327C5.93001 28.9797 0.656006 22.9427 0.656006 15.6777C0.656006 7.65073 7.16001 1.14673 15.187 1.14673C23.215 1.14673 29.719 7.65073 29.719 15.6777Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_901_23799">
          <rect
            width="30"
            height="30"
            fill="white"
            transform="translate(0 0.927734)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

FacebookSvg.displayName = 'FacebookSvg'

export default FacebookSvg
