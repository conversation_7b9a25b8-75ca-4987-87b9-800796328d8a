import classNames from 'classnames'

const PinterestSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 30 31"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_901_23807)">
        <path
          d="M29.719 15.6777C29.719 23.7047 23.215 30.2087 15.187 30.2087C13.664 30.2087 12.199 30.0337 10.852 29.5647C11.437 28.6267 12.316 27.0447 12.668 25.7557C12.844 25.1117 13.547 22.2987 13.547 22.2987C14.016 23.2367 15.422 23.9987 16.887 23.9987C21.281 23.9987 24.445 19.9547 24.445 14.9747C24.445 10.1697 20.52 6.53673 15.48 6.53673C9.21002 6.53673 5.87102 10.7567 5.87102 15.3267C5.87102 17.4937 6.98402 20.1307 8.80102 21.0097C9.09402 21.1267 9.27002 21.0687 9.32802 20.7757C9.32802 20.5997 9.62102 19.6037 9.73802 19.1347C9.73802 19.0177 9.73802 18.8417 9.62102 18.7247C9.03502 18.0217 8.56602 16.6747 8.56602 15.3847C8.56602 12.2207 10.969 9.11473 15.129 9.11473C18.645 9.11473 21.164 11.5177 21.164 15.0327C21.164 18.9587 19.172 21.6547 16.594 21.6547C15.187 21.6547 14.133 20.4827 14.426 19.0757C14.836 17.3187 15.656 15.4437 15.656 14.2127C15.656 13.0997 15.07 12.1627 13.84 12.1627C12.375 12.1627 11.203 13.6857 11.203 15.6777C11.203 16.9677 11.613 17.8457 11.613 17.8457C11.613 17.8457 10.207 23.9397 9.91402 25.0527C9.62102 26.3427 9.73802 28.0997 9.85502 29.2127C7.13428 28.1494 4.79924 26.2873 3.15725 23.8713C1.51525 21.4554 0.643252 18.5988 0.656022 15.6777C0.656022 7.65073 7.16002 1.14673 15.187 1.14673C23.215 1.14673 29.719 7.65073 29.719 15.6777Z"
          fill="currentColor"
          stroke="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_901_23807">
          <rect
            width="30"
            height="30"
            fill="white"
            transform="translate(0 0.927734)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

PinterestSvg.displayName = 'PinterestSvg'

export default PinterestSvg
