import classNames from 'classnames'

const PodcastsSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1175_56396)">
        <path
          d="M6.45001 2.00001C5.86526 1.99869 5.28601 2.1129 4.74551 2.33606C4.20502 2.55923 3.71393 2.88696 3.30045 3.30045C2.88696 3.71393 2.55923 4.20502 2.33606 4.74551C2.1129 5.28601 1.99869 5.86526 2.00001 6.45001V17.55C1.99869 18.1348 2.1129 18.714 2.33606 19.2545C2.55923 19.795 2.88696 20.2861 3.30045 20.6996C3.71393 21.1131 4.20502 21.4408 4.74551 21.664C5.28601 21.8871 5.86526 22.0013 6.45001 22H17.55C18.1348 22.0013 18.714 21.8871 19.2545 21.664C19.795 21.4408 20.2861 21.1131 20.6996 20.6996C21.1131 20.2861 21.4408 19.795 21.664 19.2545C21.8871 18.714 22.0013 18.1348 22 17.55V6.45001C22.0013 5.86526 21.8871 5.28601 21.664 4.74551C21.4408 4.20502 21.1131 3.71393 20.6996 3.30045C20.2861 2.88696 19.795 2.55923 19.2545 2.33606C18.714 2.1129 18.1348 1.99869 17.55 2.00001H6.45001ZM11.8875 4.14001C13.8342 4.14001 15.5942 4.89168 16.9342 6.29584C17.9542 7.35584 18.5275 8.47834 18.8208 9.95584C18.9208 10.4475 18.9208 11.7892 18.8267 12.3425C18.5149 14.1013 17.5506 15.6775 16.1267 16.7558C15.62 17.1392 14.38 17.8067 14.18 17.8067C14.1067 17.8067 14.1 17.7308 14.1333 17.4233C14.1933 16.93 14.2533 16.8275 14.5333 16.71C14.98 16.5233 15.74 15.9817 16.2067 15.5142C17.0203 14.7011 17.5985 13.6828 17.88 12.5675C18.0533 11.8808 18.0333 10.3542 17.84 9.64751C17.2333 7.40084 15.4 5.65418 13.1533 5.18751C12.5 5.05418 11.3133 5.05418 10.6533 5.18751C8.38001 5.65418 6.50001 7.48751 5.92668 9.79418C5.77334 10.4208 5.77334 11.9475 5.92668 12.5742C6.30668 14.1008 7.29334 15.5008 8.58668 16.3342C8.84001 16.5008 9.14668 16.6742 9.27334 16.7275C9.55334 16.8475 9.61334 16.9475 9.66668 17.4408C9.70001 17.7408 9.69168 17.8275 9.62001 17.8275C9.57334 17.8275 9.23334 17.6808 8.87334 17.5075L8.84001 17.4825C6.78001 16.4692 5.46001 14.7542 4.98001 12.4725C4.86001 11.8842 4.84001 10.4792 4.95501 9.93918C5.25501 8.48918 5.82834 7.35584 6.78168 6.35251C8.15501 4.90501 9.92168 4.13918 11.8883 4.13918L11.8875 4.14001ZM11.9992 6.48168C12.34 6.48501 12.6683 6.51501 12.9208 6.57001C15.2408 7.08668 16.8875 9.41001 16.5675 11.715C16.4408 12.6433 16.1208 13.4067 15.5542 14.115C15.2742 14.4733 14.5942 15.0733 14.4742 15.0733C14.455 15.0733 14.4342 14.8467 14.4342 14.5708V14.0667L14.7808 13.6533C16.0875 12.0883 15.9942 9.90168 14.5675 8.46668C14.0142 7.90834 13.3742 7.58001 12.5475 7.42834C12.0142 7.33001 11.9008 7.33001 11.3408 7.42168C10.4908 7.56084 9.83251 7.89001 9.24751 8.46834C7.81418 9.88834 7.72084 12.0867 9.02751 13.6533L9.37168 14.0667V14.5733C9.37168 14.8533 9.34918 15.08 9.32168 15.08C9.29668 15.08 9.10168 14.9467 8.89501 14.78L8.86668 14.7708C8.17334 14.2175 7.56001 13.2358 7.30668 12.2733C7.15334 11.6917 7.15334 10.5867 7.31334 10.0067C7.73334 8.44168 8.88668 7.22751 10.4867 6.65751C10.8283 6.53668 11.4317 6.47418 11.9992 6.48168ZM11.8908 8.97334C12.1492 8.97334 12.4075 9.02334 12.5942 9.12168C13.0008 9.33251 13.3342 9.74251 13.4608 10.1708C13.8475 11.4858 12.4542 12.6375 11.1942 12.0492H11.1817C10.5883 11.7733 10.2683 11.2525 10.2617 10.5742C10.2617 9.96334 10.6017 9.43168 11.1883 9.12001C11.375 9.02251 11.6325 8.97334 11.8908 8.97334ZM11.8817 12.9133C12.705 12.91 13.3033 13.2042 13.5233 13.7217C13.6883 14.1083 13.6267 15.3317 13.3417 17.3067C13.1483 18.6867 13.0417 19.035 12.775 19.27C12.4083 19.595 11.8883 19.685 11.395 19.51H11.3925C10.7958 19.2958 10.6675 19.0058 10.4225 17.3067C10.1383 15.3317 10.0758 14.1083 10.2408 13.7217C10.4592 13.2083 11.0525 12.9167 11.8825 12.9133H11.8817Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1175_56396">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(2 2)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

PodcastsSvg.displayName = 'PodcastsSvg'

export default PodcastsSvg
