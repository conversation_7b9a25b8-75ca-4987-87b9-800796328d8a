import classNames from 'classnames'

const BasketSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.375 11.25V16.5C12.375 16.5995 12.3355 16.6948 12.2652 16.7652C12.1948 16.8355 12.0995 16.875 12 16.875C11.9005 16.875 11.8052 16.8355 11.7348 16.7652C11.6645 16.6948 11.625 16.5995 11.625 16.5V11.25C11.625 11.1505 11.6645 11.0552 11.7348 10.9848C11.8052 10.9145 11.9005 10.875 12 10.875C12.0995 10.875 12.1948 10.9145 12.2652 10.9848C12.3355 11.0552 12.375 11.1505 12.375 11.25ZM16.9875 10.875C16.9385 10.8701 16.889 10.8749 16.8419 10.8891C16.7947 10.9033 16.7508 10.9266 16.7127 10.9578C16.6745 10.9889 16.6429 11.0273 16.6196 11.0707C16.5963 11.1141 16.5818 11.1616 16.5769 11.2106L16.0519 16.4606C16.0464 16.5099 16.0508 16.5598 16.0648 16.6074C16.0787 16.655 16.102 16.6993 16.1332 16.7379C16.1644 16.7764 16.203 16.8084 16.2466 16.8319C16.2903 16.8555 16.3381 16.8701 16.3875 16.875H16.4259C16.5192 16.8755 16.6093 16.8412 16.6787 16.7787C16.748 16.7163 16.7916 16.6303 16.8009 16.5375L17.3259 11.2875C17.3311 11.2381 17.3264 11.1882 17.3122 11.1407C17.2979 11.0932 17.2744 11.0489 17.2429 11.0106C17.2114 10.9722 17.1726 10.9404 17.1288 10.9172C17.0849 10.8939 17.0369 10.8796 16.9875 10.875ZM7.0125 10.875C6.9635 10.8799 6.91595 10.8945 6.87256 10.9178C6.82918 10.9411 6.79081 10.9727 6.75964 11.0108C6.72848 11.0489 6.70513 11.0928 6.69093 11.14C6.67672 11.1871 6.67195 11.2366 6.67688 11.2856L7.20188 16.5356C7.2112 16.6285 7.25479 16.7145 7.32413 16.7769C7.39347 16.8393 7.48359 16.8736 7.57688 16.8731H7.61531C7.66431 16.8682 7.71186 16.8537 7.75525 16.8304C7.79863 16.8071 7.837 16.7755 7.86817 16.7373C7.89934 16.6992 7.92269 16.6553 7.93689 16.6081C7.95109 16.561 7.95586 16.5115 7.95094 16.4625L7.42594 11.2125C7.42126 11.1631 7.40681 11.1151 7.38343 11.0713C7.36005 11.0275 7.32821 10.9888 7.28975 10.9574C7.25129 10.926 7.20698 10.9025 7.15938 10.8884C7.11179 10.8742 7.06186 10.8697 7.0125 10.875ZM22.125 8.29969L20.7084 18.8991C20.6723 19.1691 20.5394 19.4169 20.3344 19.5964C20.1294 19.7759 19.8662 19.8749 19.5938 19.875H4.40625C4.13377 19.8749 3.87058 19.7759 3.66557 19.5964C3.46056 19.4169 3.32767 19.1691 3.29156 18.8991L1.875 8.29969C1.86855 8.24648 1.87358 8.1925 1.88975 8.1414C1.90592 8.0903 1.93286 8.04325 1.96875 8.00344C2.00381 7.96327 2.04702 7.93103 2.09552 7.90889C2.14402 7.88674 2.19668 7.87519 2.25 7.875H6.57938L11.7188 2.00344C11.7539 1.96354 11.7972 1.93158 11.8457 1.90969C11.8942 1.8878 11.9468 1.87648 12 1.87648C12.0532 1.87648 12.1058 1.8878 12.1543 1.90969C12.2028 1.93158 12.2461 1.96354 12.2812 2.00344L17.4206 7.875H21.75C21.8033 7.87519 21.856 7.88674 21.9045 7.90889C21.953 7.93103 21.9962 7.96327 22.0312 8.00344C22.0671 8.04325 22.0941 8.0903 22.1103 8.1414C22.1264 8.1925 22.1315 8.24648 22.125 8.29969ZM7.57688 7.875H16.4231L12 2.81907L7.57688 7.875ZM21.3216 8.625H2.67844L4.03125 18.7997C4.04336 18.8903 4.08816 18.9734 4.15721 19.0333C4.22627 19.0932 4.31483 19.1258 4.40625 19.125H19.5938C19.6852 19.1258 19.7737 19.0932 19.8428 19.0333C19.9118 18.9734 19.9566 18.8903 19.9687 18.7997L21.3216 8.625Z"
        fill="currentColor"
      />
    </svg>
  )
}

BasketSvg.displayName = 'BasketSvg'

export default BasketSvg
