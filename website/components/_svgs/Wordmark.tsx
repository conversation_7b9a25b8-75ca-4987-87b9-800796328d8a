import classNames from 'classnames'

const WordmarkSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 259 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2144_21104)">
        <path
          d="M165.789 23.7528H163.182V22.0212H166.145C169.533 22.0212 171.428 21.891 173.326 20.5965C176.038 18.7848 177.374 16.1257 177.374 12.2388C177.374 5.17556 172.852 2.4397 167.094 2.4397C164.588 2.4397 163.182 2.81671 163.182 2.81671L162.414 1.14183H168.413C173.019 1.14183 176.68 1.85582 179.119 3.53738C181.558 5.09215 183.251 8.00818 183.251 11.5048C183.251 16.8163 179.524 20.5098 174.579 22.5817C174.579 22.5817 171.152 23.7461 165.789 23.7461V23.7528ZM75.5225 42.6903V1.14516H81.1826V20.75C81.1826 20.75 89.0866 6.67695 91.655 1.14516H96.8894L86.0367 20.2996L98.4668 42.6936H92.0354C89.4426 36.9617 81.72 21.7776 81.1826 21.5207V42.6936H75.5225V42.6903ZM27.1283 42.6903L27.3273 12.0286C27.3273 12.0286 16.9526 39.5641 15.9057 42.6903H15.1659C14.4645 39.9945 4.40042 9.3561 4.40042 9.3561C4.40042 9.3561 5.56246 40.0645 5.56246 42.6903H0C0.886365 22.6684 1.30512 1.02172 1.30512 1.02172H6.87805L16.7642 31.7568L28.3811 1.14516H32.785L32.9211 42.6903H27.1283ZM40.6472 42.6903V1.14516H46.3073V42.6903H40.6472ZM59.4632 1.14516V40.5617C62.6527 40.5617 71.2302 38.3129 71.2302 38.3129L70.7347 42.6903H53.803V1.14516H59.4632ZM122.42 3.40392C118.277 3.40392 116.4 6.39336 116.4 8.68548C116.4 13.6 120.608 15.5352 124.604 18.8516C128.495 22.0946 133.293 25.451 133.293 32.0104C133.293 38.5698 127.741 43.4443 120.493 43.4443C116.452 43.4443 112.952 42.36 111.298 41.5959C111.298 39.9945 110.942 34.0189 110.942 34.0189C110.942 34.0189 114.707 40.4849 120.692 40.4849C124.314 40.4849 127.375 37.4921 127.375 33.2182C127.375 28.307 123.868 25.6345 120.186 22.6718C116.239 19.5022 111.591 16.3392 111.591 10.6173C111.591 5.289 116.156 0.557953 122.814 0.557953C126.181 0.557953 130.372 1.11847 130.372 1.11847L129.922 6.95721C129.922 6.95721 126.862 3.40726 122.42 3.40726V3.40392Z"
          fill="currentColor"
        />
        <path
          d="M166.169 1.14569V42.6942H160.509V1.14569H166.169ZM155.864 1.14569H133.96L133.565 5.60315C133.565 5.60315 138.901 3.44449 141.909 3.44449V42.6608H147.569V3.44449C151.356 3.44449 156.258 5.60315 156.258 5.60315L155.864 1.14569ZM258.606 1.14569H236.701L236.307 5.60315C236.307 5.60315 241.643 3.44449 244.651 3.44449V42.6608H250.311V3.44449C254.097 3.44449 259 5.60315 259 5.60315L258.606 1.14569ZM195.115 3.44449C200.723 3.44449 207.57 5.26284 207.57 5.26284L207.982 1.14903H189.455V42.6641H208.439L208.927 38.2134C208.927 38.2134 198.403 40.5655 195.112 40.5655V3.44449H195.115Z"
          fill="currentColor"
        />
        <path
          d="M164.78 23.6899C164.78 23.6899 167.726 23.7033 170.601 24.1804C173.473 29.1416 178.819 42.6575 178.819 42.6575H184.943L175.029 21.955L169.554 22.392H164.99L164.784 23.6866L164.78 23.6899ZM194.501 21.5946C199.673 21.5946 206.139 22.5789 206.139 22.5789L206.851 18.3983L194.501 19.069V21.5913V21.5946ZM189.459 19.3359H194.187V22.8925H189.459V19.3359ZM219.669 3.44451C225.276 3.44451 232.123 5.26286 232.123 5.26286L232.535 1.14905H214.008V42.6641H232.992L233.481 38.2133C233.481 38.2133 222.956 40.5655 219.665 40.5655V3.44451H219.669Z"
          fill="currentColor"
        />
        <path
          d="M219.054 21.6447C223.496 21.6447 230.692 22.6823 230.692 22.6823L231.404 18.2849L219.054 18.9922V21.648V21.6447ZM214.015 19.2692H218.744V23.0126H214.015V19.2692Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2144_21104">
          <rect
            width="259"
            height="42.9415"
            fill="white"
            transform="translate(0 0.529266)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

WordmarkSvg.displayName = 'WordmarkSvg'

export default WordmarkSvg
