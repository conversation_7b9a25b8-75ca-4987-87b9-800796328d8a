import classNames from 'classnames'

const YoutubeSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 284 284"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_263_140)">
        <mask
          id="mask0_263_140"
          x="-2"
          y="0"
          width="294"
          height="284"
        >
          <path
            d="M291.691 0H-1.7793V284H291.691V0Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_263_140)">
          <path
            d="M142 0C113.915 0 86.4609 8.32816 63.1091 23.9313C39.7574 39.5345 21.5569 61.7119 10.8092 87.659C0.0615807 113.606 -2.75057 142.158 2.72854 169.703C8.20764 197.248 21.7319 222.55 41.591 242.409C61.45 262.268 86.752 275.792 114.297 281.272C141.843 286.751 170.394 283.939 196.341 273.191C222.288 262.443 244.466 244.243 260.069 220.891C275.672 197.539 284 170.085 284 142C284 104.339 269.039 68.221 242.409 41.5908C215.779 14.9607 179.661 0 142 0ZM238.5 186.45C237.4 190.724 235.173 194.625 232.053 197.747C228.933 200.869 225.034 203.097 220.76 204.2C204.86 208.27 142 208.27 142 208.27C142 208.27 78.7601 208.27 62.8601 204.2C58.5824 203.103 54.6779 200.877 51.5553 197.755C48.4327 194.632 46.2067 190.728 45.1101 186.45C40.6701 170.54 40.6701 137.64 40.6701 137.64C40.6701 137.64 40.6701 104.35 45.1101 88.83C46.1969 84.5073 48.4132 80.5513 51.5322 77.3673C54.6513 74.1832 58.5608 71.8857 62.8601 70.71C78.7601 66.27 142 66.27 142 66.27C142 66.27 204.86 66.27 220.76 70.71C225.059 71.8876 228.967 74.1858 232.086 77.3695C235.204 80.5532 237.421 84.5082 238.51 88.83C242.95 104.35 242.95 137.64 242.95 137.64C242.95 137.64 242.95 170.55 238.5 186.45Z"
            fill="currentColor"
          />
          <path
            d="M121.289 167.59L173.799 137.64L121.289 107.68V167.59Z"
            fill="currentColor"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_263_140">
          <rect
            width="284"
            height="284"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

YoutubeSvg.displayName = 'YoutubeSvg'

export default YoutubeSvg
