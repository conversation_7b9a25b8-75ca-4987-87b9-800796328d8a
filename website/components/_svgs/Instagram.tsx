import classNames from 'classnames'

const InstagramSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 250 250"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_263_135)">
        <path
          d="M181.629 77.87C179.029 71.68 174.149 66.47 167.969 64.2C158.199 60.28 135.409 61.27 124.969 61.27C114.229 61.27 91.4493 60.27 81.9693 64.19C78.8393 65.4151 75.9873 67.2564 73.5824 69.6046C71.1775 71.9528 69.2687 74.7601 67.9693 77.86C64.0793 87.64 65.0493 110.42 65.0493 120.86C65.0493 131.61 64.0493 154.39 67.9793 163.86C69.2205 167.033 71.1051 169.915 73.5145 172.325C75.9239 174.734 78.806 176.619 81.9793 177.86C91.4193 181.77 114.199 180.79 124.979 180.79C135.399 180.79 158.189 181.79 167.979 177.86C171.108 176.611 173.941 174.72 176.294 172.308C178.646 169.897 180.468 167.019 181.639 163.86C185.559 154.42 184.579 131.63 184.579 120.86C184.569 110.42 185.539 87.63 181.629 77.87ZM124.999 158.27C117.594 158.272 110.354 156.078 104.196 151.965C98.0377 147.852 93.2375 142.006 90.4022 135.165C87.5669 128.324 86.824 120.795 88.2675 113.532C89.711 106.269 93.276 99.5966 98.5117 94.3595C103.747 89.1224 110.419 85.5556 117.681 84.1102C124.944 82.6647 132.473 83.4056 139.315 86.239C146.157 89.0725 152.004 93.8712 156.119 100.028C160.233 106.186 162.429 113.425 162.429 120.83C162.433 125.747 161.468 130.616 159.588 135.16C157.709 139.704 154.952 143.832 151.476 147.309C147.999 150.787 143.872 153.544 139.329 155.425C134.786 157.306 129.916 158.273 124.999 158.27ZM171.859 85.47C171.424 86.5381 170.778 87.5074 169.959 88.32C169.142 89.1396 168.17 89.7888 167.099 90.23C164.943 91.1185 162.522 91.1165 160.367 90.2244C158.211 89.3323 156.497 87.6229 155.599 85.47C154.936 83.8606 154.762 82.0909 155.1 80.3831C155.438 78.6753 156.273 77.1053 157.499 75.87C158.319 75.0547 159.29 74.4089 160.359 73.9696C161.428 73.5304 162.574 73.3062 163.729 73.31C164.886 73.3018 166.032 73.5262 167.099 73.97C168.17 74.4083 169.143 75.0547 169.962 75.8718C170.78 76.6889 171.429 77.6604 171.869 78.73C172.308 79.7994 172.532 80.9444 172.529 82.1C172.533 83.2568 172.306 84.4027 171.859 85.47Z"
          fill="currentColor"
        />
        <path
          d="M142.04 103.79C138.644 100.429 134.334 98.1419 129.648 97.2136C124.961 96.2852 120.105 96.7567 115.684 98.5693C111.264 100.382 107.474 103.455 104.788 107.407C102.102 111.358 100.639 116.013 100.58 120.79C100.576 123.998 101.205 127.175 102.431 130.14C103.657 133.104 105.455 135.798 107.724 138.066C109.992 140.335 112.686 142.133 115.65 143.359C118.615 144.585 121.792 145.214 125 145.21C128.976 145.158 132.88 144.134 136.37 142.228C139.86 140.323 142.832 137.593 145.027 134.277C147.221 130.961 148.572 127.158 148.961 123.201C149.35 119.243 148.766 115.25 147.26 111.57C146.052 108.657 144.278 106.013 142.04 103.79Z"
          fill="currentColor"
        />
        <path
          d="M125 0C100.277 0 76.1099 7.33112 55.5538 21.0663C34.9976 34.8015 18.976 54.3238 9.51507 77.1646C0.0541172 100.005 -2.42124 125.139 2.40191 149.386C7.22506 173.634 19.1301 195.907 36.6116 213.388C54.0932 230.87 76.3661 242.775 100.614 247.598C124.861 252.421 149.995 249.946 172.836 240.485C195.676 231.024 215.199 215.002 228.934 194.446C242.669 173.89 250 149.723 250 125C250 91.8479 236.83 60.0537 213.388 36.6116C189.946 13.1696 158.152 0 125 0ZM197.26 151.11C196.61 162.82 194.01 172.92 185.54 181.71C177.08 190.17 166.67 192.77 154.94 193.42C142.94 194.07 106.77 194.07 94.72 193.42C83 192.77 72.92 190.17 64.12 181.71C55.66 172.92 53.0601 162.82 52.4101 151.11C51.7501 139.11 51.7501 102.93 52.4101 90.88C53.0601 79.17 55.66 68.75 64.12 60.28C72.91 51.82 83 49.22 94.72 48.57C106.77 47.92 142.89 47.92 154.94 48.57C166.67 49.22 177.08 51.82 185.54 60.28C194 68.74 196.61 79.17 197.27 90.88C197.91 102.93 197.91 139.06 197.26 151.11Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_263_135">
          <rect
            width="250"
            height="250"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

InstagramSvg.displayName = 'InstagramSvg'

export default InstagramSvg
