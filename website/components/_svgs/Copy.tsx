import classNames from 'classnames'

const CopySvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.766 5.7234H3.51064C3.37521 5.7234 3.24533 5.7772 3.14956 5.87297C3.0538 5.96873 3 6.09861 3 6.23404V18.4894C3 18.6248 3.0538 18.7547 3.14956 18.8504C3.24533 18.9462 3.37521 19 3.51064 19H15.766C15.9014 19 16.0313 18.9462 16.127 18.8504C16.2228 18.7547 16.2766 18.6248 16.2766 18.4894V6.23404C16.2766 6.09861 16.2228 5.96873 16.127 5.87297C16.0313 5.7772 15.9014 5.7234 15.766 5.7234ZM15.2553 17.9787H4.02128V6.74468H15.2553V17.9787ZM19 3.51064V15.766C19 15.9014 18.9462 16.0313 18.8504 16.127C18.7547 16.2228 18.6248 16.2766 18.4894 16.2766C18.3539 16.2766 18.224 16.2228 18.1283 16.127C18.0325 16.0313 17.9787 15.9014 17.9787 15.766V4.02128H6.23404C6.09861 4.02128 5.96873 3.96748 5.87297 3.87171C5.7772 3.77595 5.7234 3.64607 5.7234 3.51064C5.7234 3.37521 5.7772 3.24533 5.87297 3.14956C5.96873 3.0538 6.09861 3 6.23404 3H18.4894C18.6248 3 18.7547 3.0538 18.8504 3.14956C18.9462 3.24533 19 3.37521 19 3.51064Z"
        fill="currentColor"
      />
    </svg>
  )
}

CopySvg.displayName = 'CopySvg'

export default CopySvg
