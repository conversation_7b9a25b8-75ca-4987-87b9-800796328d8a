import classNames from 'classnames'

const PlayCircleSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.5 14.584V7.41601C8.50015 7.32632 8.52442 7.23833 8.57026 7.16125C8.61611 7.08417 8.68184 7.02084 8.76057 6.97789C8.83931 6.93495 8.92814 6.91397 9.01777 6.91716C9.1074 6.92034 9.19452 6.94758 9.27 6.99601L14.846 10.579C14.9166 10.6242 14.9747 10.6865 15.0149 10.7601C15.0552 10.8336 15.0763 10.9162 15.0763 11C15.0763 11.0839 15.0552 11.1664 15.0149 11.2399C14.9747 11.3135 14.9166 11.3758 14.846 11.421L9.27 15.005C9.19452 15.0534 9.1074 15.0807 9.01777 15.0839C8.92814 15.087 8.83931 15.0661 8.76057 15.0231C8.68184 14.9802 8.61611 14.9168 8.57026 14.8398C8.52442 14.7627 8.50015 14.6747 8.5 14.585V14.584Z"
        fill="currentColor"
      />
      <path
        d="M0 11C0 4.925 4.925 0 11 0C17.075 0 22 4.925 22 11C22 17.075 17.075 22 11 22C4.925 22 0 17.075 0 11ZM11 1.5C8.48044 1.5 6.06408 2.50089 4.28249 4.28249C2.50089 6.06408 1.5 8.48044 1.5 11C1.5 13.5196 2.50089 15.9359 4.28249 17.7175C6.06408 19.4991 8.48044 20.5 11 20.5C13.5196 20.5 15.9359 19.4991 17.7175 17.7175C19.4991 15.9359 20.5 13.5196 20.5 11C20.5 8.48044 19.4991 6.06408 17.7175 4.28249C15.9359 2.50089 13.5196 1.5 11 1.5Z"
        fill="currentColor"
      />
    </svg>
  )
}

PlayCircleSvg.displayName = 'PlayCircleSvg'

export default PlayCircleSvg
