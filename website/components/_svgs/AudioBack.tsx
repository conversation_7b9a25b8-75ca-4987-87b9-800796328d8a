import classNames from 'classnames'

const AudioBackSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 36 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1659_37876)">
        <path
          d="M9 9.4375H12V27.4375H9V9.4375ZM14.25 18.4375L27 27.4375V9.4375L14.25 18.4375Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1659_37876">
          <rect
            width="36"
            height="36"
            fill="white"
            transform="translate(0 0.4375)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

AudioBackSvg.displayName = 'AudioBackSvg'

export default AudioBackSvg
