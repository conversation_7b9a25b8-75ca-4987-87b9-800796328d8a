import classNames from 'classnames'

const ShareSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 12.4375V20.4375C4 20.9679 4.21071 21.4766 4.58579 21.8517C4.96086 22.2268 5.46957 22.4375 6 22.4375H18C18.5304 22.4375 19.0391 22.2268 19.4142 21.8517C19.7893 21.4766 20 20.9679 20 20.4375V12.4375"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16 6.4375L12 2.4375L8 6.4375"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 2.4375V15.4375"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

ShareSvg.displayName = 'ShareSvg'

export default ShareSvg
