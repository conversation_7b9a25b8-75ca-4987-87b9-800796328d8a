import classNames from 'classnames'

const CaretRightSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 16.6879L14.4678 11.8505C14.4908 11.8301 14.5092 11.805 14.5218 11.777C14.5344 11.749 14.5409 11.7186 14.5409 11.6879C14.5409 11.6571 14.5344 11.6268 14.5218 11.5987C14.5092 11.5707 14.4908 11.5457 14.4678 11.5253L9 6.68787"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

CaretRightSvg.displayName = 'CaretRightSvg'

export default CaretRightSvg
