import classNames from 'classnames'

const CaretDownSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.6879 9L11.8505 14.4678C11.8301 14.4908 11.805 14.5092 11.777 14.5218C11.749 14.5344 11.7186 14.5409 11.6879 14.5409C11.6571 14.5409 11.6268 14.5344 11.5987 14.5218C11.5707 14.5092 11.5457 14.4908 11.5253 14.4678L6.68787 9"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

CaretDownSvg.displayName = 'CaretDownSvg'

export default CaretDownSvg
