import classNames from 'classnames'

const AudioNextSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 36 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1659_37888)">
        <path
          d="M27 9.4375H24V27.4375H27V9.4375ZM21.75 18.4375L9 27.4375V9.4375L21.75 18.4375Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1659_37888">
          <rect
            width="36"
            height="36"
            fill="white"
            transform="matrix(-1 0 0 1 36 0.4375)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

AudioNextSvg.displayName = 'AudioNextSvg'

export default AudioNextSvg
