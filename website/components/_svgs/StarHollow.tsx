import classNames from 'classnames'

const StarHollowSvg = ({ className }: SvgProps) => {
  return (
    <svg
      className={classNames(className)}
      viewBox="0 0 40 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M37.9199 14.6836L25.2051 13.3408L20 1.66602L14.7949 13.3408L2.08008 14.6836L11.5771 23.2432L8.92578 35.748L20 29.3613L31.0742 35.748L28.4229 23.2432L37.9199 14.6836Z"
        stroke="currentColor"
        fill="none"
      />
    </svg>
  )
}

StarHollowSvg.displayName = 'StarHollowSvg'

export default StarHollowSvg
