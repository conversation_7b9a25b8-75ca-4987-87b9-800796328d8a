import classnames from 'classnames'
import styles from './Tag.module.scss'
import useBreakpoint from '@/hooks/use-breakpoint'
import { useMemo } from 'react'

const Tag = ({ text, size = 'md', className }: TagProps) => {
  const { isMobile } = useBreakpoint()
  size = useMemo(() => {
    if (isMobile && size === 'lg') {
      return 'md'
    }
    return size
  }, [isMobile, size])

  return (
    <span
      className={classnames(styles.Tag, className)}
      data-size={size}
      role="status"
      aria-label={text}
    >
      {text}
    </span>
  )
}

Tag.displayName = 'Tag'

export default Tag
