import { Meta, StoryObj } from '@storybook/react'
import Tag from './Tag'

const meta: Meta<typeof Tag> = {
  title: 'Components/Tag',
  component: Tag,
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'radio' },
      options: ['sm', 'md', 'lg'],
    },
    text: { control: 'text' },
    className: { control: 'text' },
  },
}

export default meta

type Story = StoryObj<typeof Tag>

export const Small: Story = {
  args: {
    text: 'recipe',
    size: 'sm',
  },
}

export const Medium: Story = {
  args: {
    text: 'recipe',
    size: 'md',
  },
}

export const Large: Story = {
  args: {
    text: 'recipe',
    size: 'lg',
  },
}
