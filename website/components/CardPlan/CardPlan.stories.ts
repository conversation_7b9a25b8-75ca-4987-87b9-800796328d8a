import type { Meta, StoryObj } from '@storybook/react'
import CardPlan from './CardPlan'
import { SANITY_IMAGE_STUB_SOURCE } from '../SanityImage/SanityImage.stub'

const meta = {
  title: 'Cards/CardPlan',
  component: Card<PERSON><PERSON>,
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof CardPlan>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    image: SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE,
    title: 'Plan Title',
    subtitle: 'Plan Subtitle',
    cost: '$100',
    cta: {
      label: 'Join Now',
      linkType: 'internal',
      link: {
        slug: 'test',
        _type: 'page',
      },
    } as SanityLink,
    items: [
      'Everything Included In Print & Digital Access, Plus:',
      'Free Standard Shipping, Milk Street Store, To Contiguous US Addresses',
      'Advance Notification Of Special Store Sales',
      'Receive Access To All Live Stream Cooking Classes (Excluding Workshops & Intensives)',
    ],
  },
}
