'use client'

import classnames from 'classnames'
import styles from './CardPlan.module.scss'
import SanityImage from '../SanityImage/SanityImage'
import Button from '../Button/Button'

const CardPlan = ({ className, image, title, subtitle, cta, cost, items }: CardPlanProps) => {
  if (!image || !title || !cost || !items?.length || !cta) return null

  return (
    <article className={classnames(styles.CardPlan, className)}>
      <SanityImage
        source={image}
        className={styles.image}
        aspectRatio={283 / 140}
        columns={{
          md: 4,
          sm: 12,
        }}
      />
      <h1 className={styles.title}>{title}</h1>
      {subtitle && <p className={styles.subtitle}>{subtitle}</p>}
      <p className={styles.cost}>{cost}</p>
      <Button
        link={cta}
        linkClassName={styles.button}
      />
      <ul className={styles.items}>
        {items.map((item, i) => (
          <li
            className={styles.item}
            key={i}
          >
            {item}
          </li>
        ))}
      </ul>
    </article>
  )
}

CardPlan.displayName = 'CardPlan'

export default CardPlan
