.CardPlan {
  width: 100%;
  text-align: center;
}

.image {
  aspect-ratio: 283/140;
  object-fit: cover;
  margin-bottom: px(24);
}

.title {
  @include font-heading-lg;
  margin-bottom: px(8);
}

.subtitle {
  @include font-text-base;
  margin-bottom: px(8);
}

.cost {
  @include font-heading-xl;
  margin-bottom: px(24);
}

.button {
  width: 100%;
  display: block;
  margin-bottom: px(24);
}

.items {
  @include reset-ul;
  display: flex;
  flex-direction: column;
  gap: px(16);
}

.item {
  @include font-text-base;
  font-weight: 700;
}
