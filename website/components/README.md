## Component guidelines

### .tsx file

1. Ensure that the required props for the component are checked before render
2. Ensure that the main containing element has a PascalCase name as a className, and all children have camelCase names as classNames
3. All `/svgs` are referenced through the `<Icon/>` component and not directly imported
4. `React.FC` should not be used anywhere
5. No hard-coded text set should be all uppercase
6. `SanityImage` should be used for all imagery
7. The custom `Link` component found in `components/Link/Link.tsx` should be used for all links
8. No dashes should be used in class names. Instead, use underscores.
9. All components should have a `displayName` set
10. All components should have a `use client` directive
11. The props type should be stored in a `types/components/[component_name].d.ts` file and not be loose in the component file
12. When adding variance to a component (ie.`size: "sm" | "md" | "lg"`), add this to the component through data attributes. For example `<div data-line-heading-size="{size}">` where `LineHeading` is the component name and `sm` is the variant being passed into the attribute.

### .module.scss file

1. No hard-coded hexes are used and all colors reference the variables set in `global.scss` or, if need be where you need to manipulate the color with transparency, in the `vars.scss` file
2. All class names should be on their own and not nested within one another (unless for a specific reason)
3. With the exception of 1px and 2px units, all px units should be run through the `px()` function (ie. `px(32)`) to generate rem units
4. Mixins should be used in place of property values (ie. instead of `display: flex; align-items: center; justify-content: center;` use `@include flex-center;`)
5. All type should be set through the mixins set in `typography.scss`, and no hard-coded values for `font-size` or `text-transform` should be used. Only `font-style` and `font-weight` should be set directly
6. If styling an icon or svg etc, only a width or height should be set, and not both. Totally fine if not set at all and it's set on a parent or something.
