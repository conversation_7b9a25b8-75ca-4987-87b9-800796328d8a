.Footer {
  color: var(--parchment);
  background-color: var(--navy-dark);
  padding: 0 var(--page-gutter);
}

.inner {
  color: var(--color-parchment);
  padding: px(56) 0;
  margin: 0 auto;
  max-width: var(--section-max-width);
  width: 100%;
}

.logoContainer {
  padding-bottom: px(24);
  border-bottom: 1px solid rgba($parchment, 0.6);
  margin-bottom: px(40);

  @include bp(tablet) {
    padding-bottom: px(12);
  }
}

.logo {
  height: px(40);
  width: auto;
}

.mainContent {
  @include bp(tablet) {
    @include grid-12;
  }
}

.navSections {
  gap: px(24);
  grid-template-columns: repeat(2, minmax(0, 1fr));
  display: grid;
  grid-column: 1 / -1;

  @include bp(laptop) {
    gap: px(28);
    grid-column: 1/9;
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

.section {
  display: flex;
  flex-direction: column;
  gap: px(12);

  @include bp(tablet) {
    gap: px(20);
  }
}

.section__titleLink,
.section__titleText {
  @include font-heading-eyebrow-md;
  font-weight: 700;
}

.section__linkList__itemLink {
  @include font-text-base;
  font-weight: 700;
  transition: opacity $transition-short;

  @include hover {
    opacity: 0.5;
  }
}

.newsletterSection {
  border-top: 1px solid rgba($parchment, 0.5);
  width: 100%;
  margin-top: px(40);
  padding-top: px(24);
  grid-column: 1/-1;

  @include bp(laptop) {
    margin-top: 0;
    padding-top: 0;
    border-top: none;
    grid-column: 9/-1;
  }
}

.newsletterSection__connect {
  @include font-heading-eyebrow-md;
  font-weight: 700;
  display: block;
  margin-bottom: px(8);
}

.newsletterSection__title {
  @include font-heading-md;
  margin-bottom: px(16);
}

.socialLinks {
  gap: px(12);
  display: flex;
  margin-top: px(28);
}

.socialLinks__itemLink {
  transition: opacity $transition-short;
  display: block;
  @include box(px(24));

  @include hover {
    opacity: 0.5;
  }
}

.socialLinks__item__text {
  @include screen-reader-text;
}

.socialLinks__item__icon {
  width: 100%;
  display: block;
}

.legalLinksAndCopyright {
  color: var(--color-parchment);
  opacity: 0.6;
  margin-top: px(48);
}

.legalLinksAndCopyright__inner {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  gap: px(24);
  flex-direction: column;

  @include bp(tablet) {
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: px(16);
  }
}

.legalLinks {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: px(16);
  flex-flow: row wrap;
  order: 1;

  @include bp(tablet) {
    order: 2;
  }
}

.legalLinks__link,
.legalLinks__text {
  @include font-text-sm-display;
}

.copyright {
  @include font-text-sm-display;
  order: 2;

  @include bp(tablet) {
    order: 1;
  }
}
