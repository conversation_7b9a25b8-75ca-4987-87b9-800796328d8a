'use client'

import { SiteSettings, FooterLink } from '@/types/index.d'
import NewsletterForm from '@/components/NewsletterForm/NewsletterForm'
import Link from '../Link/Link'
import { DOC_TYPES, HOME_SLUG } from '@/data'
import styles from './Footer.module.scss'
import Icon from '@/components/Icon/Icon'

interface FooterProps {
  siteSettings: SiteSettings
}

export default function Footer({ siteSettings }: FooterProps) {
  const { footerNav, footerNewsletter, socialLinks, footerLegalLinks, copyrightText, footerLogo } = siteSettings

  return (
    <footer className={styles.Footer}>
      <div className={styles.inner}>
        {/* Footer Logo */}
        {footerLogo && (
          <div className={styles.logoContainer}>
            <Link
              link={{
                linkType: 'internal',
                link: {
                  slug: HOME_SLUG,
                  _type: DOC_TYPES.PAGE,
                },
              }}
            >
              <Icon
                name="wordmark"
                className={styles.logo}
              />
            </Link>
          </div>
        )}
        {/* Main Footer Content */}
        <div className={styles.mainContent}>
          {/* Navigation Sections */}
          <div className={styles.navSections}>
            {footerNav?.map((section, index) => (
              <div
                key={index}
                className={styles.section}
              >
                {section.isLink && section.link ? (
                  <Link
                    link={section.link}
                    className={styles.section__titleLink}
                  />
                ) : (
                  <h3 className={styles.section__titleText}>{section.title}</h3>
                )}
                <ul className={styles.section__linkList}>
                  {section.links?.map((link: FooterLink, linkIndex: number) => (
                    <li
                      key={linkIndex}
                      className={styles.section__linkList__item}
                    >
                      <Link
                        link={link.linkData}
                        className={styles.section__linkList__itemLink}
                      />
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* Newsletter Section */}
          <div className={styles.newsletterSection}>
            <span className={styles.newsletterSection__connect}>Connect</span>
            <h3 className={styles.newsletterSection__title}>{footerNewsletter?.title || 'Get Recipes, Tips & News'}</h3>
            <NewsletterForm newsletter={footerNewsletter || {}} />

            {/* Social Links */}
            <ul className={styles.socialLinks}>
              {socialLinks?.map((social, index) => (
                <li
                  key={index}
                  className={styles.socialLinks__item}
                >
                  <a
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={styles.socialLinks__itemLink}
                  >
                    <span className={styles.socialLinks__item__text}>{social.platform}</span>
                    <Icon
                      name={social.platform as 'facebook'}
                      className={styles.socialLinks__item__icon}
                    />
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>
        {/* Legal Links and Copyright */}
        <div className={styles.legalLinksAndCopyright}>
          <div className={styles.legalLinksAndCopyright__inner}>
            <div className={styles.copyright}>{copyrightText}</div>
            <div className={styles.legalLinks}>
              {footerLegalLinks?.map((link, index) => (
                <span key={index}>
                  {link.isLink && link.linkData ? (
                    <Link
                      link={link.linkData}
                      className={styles.legalLinks__link}
                    />
                  ) : (
                    <span className={styles.legalLinks__text}>{link.text}</span>
                  )}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
