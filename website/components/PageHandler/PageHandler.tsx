import { getPageDataByType } from '@/data/sanity'
import { notFound } from 'next/navigation'
import PageRenderer from '../PageRenderer/PageRenderer'
import { removeLeadingSlash, slugArrayToSlug } from '@/lib/utils'
import { formatPageDataToPageSections } from '@/lib/utils/formatPageDataToPageSections'

const PageHandler = async ({
  slug,
  docType,
  isDraftMode,
}: {
  slug: string[]
  docType: 'page' | 'pageSectionExample' | 'recipe' | 'tvEpisode' | 'radioEpisode'
  isDraftMode: boolean
}) => {
  let slugAsString = slugArrayToSlug(slug)
  slugAsString = decodeURIComponent(slugAsString)
  slugAsString = removeLeadingSlash(slugAsString)

  if (!slugAsString) return notFound()

  if (!getPageDataByType[docType]) {
    console.warn(`No key in getPageDataByType for docType: ${docType}`)
    return notFound()
  }

  let data = (await getPageDataByType[docType](slugAsString, {
    isPreview: isDraftMode,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  })) as any

  if (!data) return notFound()

  data = await formatPageDataToPageSections(data)

  if (!data?.content?.length) return <div />

  return <PageRenderer content={data?.content} />
}

PageHandler.displayName = 'PageHandler'

export default PageHandler
