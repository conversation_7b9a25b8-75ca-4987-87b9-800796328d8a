'use client'

import classnames from 'classnames'
import styles from './TextCarousel.module.scss'
import Swiper from 'swiper'
import { SwiperOptions } from 'swiper/types'
import { useEffect, useRef, useState } from 'react'
import useWindowResize from '@/hooks/use-window-resize'
import { EffectFade } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/effect-fade'
import Button from '@/components/Button/Button'

// eslint-disable-next-line react-hooks/rules-of-hooks
Swiper.use([EffectFade])

const TextCarousel = ({ items, className }: TextCarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const carouselInstance = useRef<Swiper | null>(null)
  const $carousel = useRef<HTMLDivElement | null>(null)
  const resizeKey = useWindowResize()

  useEffect(() => {
    if (!$carousel.current) return

    if (carouselInstance.current) {
      carouselInstance.current.destroy()
    }

    const settings: SwiperOptions = {
      slidesPerView: 1,
      effect: 'fade',
      fadeEffect: { crossFade: true },
      loop: true,
      autoplay: {
        delay: 5000,
        disableOnInteraction: false,
      },
      on: {
        init: swiper => {
          setCurrentIndex(swiper.realIndex)
        },
        slideChange: swiper => {
          setCurrentIndex(swiper.realIndex)
        },
      },
    }

    carouselInstance.current = new Swiper($carousel.current, settings)

    return () => {
      if (carouselInstance.current) {
        carouselInstance.current.destroy()
      }
    }
  }, [resizeKey])

  if (!items?.length) return null

  return (
    <div className={classnames(styles.TextCarousel, className)}>
      <div
        className={styles.carousel}
        ref={$carousel}
      >
        <Button
          icon="caretRight"
          className={styles.nextButton}
          iconOnlySize="lg"
          onClick={() => carouselInstance.current?.slideNext()}
        />
        <Button
          icon="caretLeft"
          className={styles.prevButton}
          iconOnlySize="lg"
          onClick={() => carouselInstance.current?.slidePrev()}
        />
        <ul className={classnames(styles.carouselInner, 'swiper-wrapper')}>
          {items.map((item, i) => (
            <li
              className={classnames(styles.carouselItem, 'swiper-slide')}
              key={i}
            >
              <div className={styles.carouselItemInner}>
                <p className={styles.description}>{item.description}</p>
                {item.subtitle && <p className={styles.subtitle}>{item.subtitle}</p>}
              </div>
            </li>
          ))}
        </ul>
        <div className={styles.pagination}>
          {items.map((_, i) => (
            <button
              key={i}
              className={classnames(styles.paginationDot, {
                [styles.isActive]: currentIndex === i,
              })}
              onClick={() => carouselInstance.current?.slideTo(i)}
              aria-label={`Go to slide ${i + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

TextCarousel.displayName = 'TextCarousel'

export default TextCarousel
