.TextCarousel {
  width: 100%;
  text-align: center;
}

.carousel {
  width: 100%;
  position: relative;
}

.carouselItem {
  width: 100%;
  align-self: center !important;
}

.carouselItemInner {
  padding: 0 px(70);

  @include bp(tablet) {
    padding: 0 px(120);
  }
}

.description {
  @include font-heading-md;
  color: var(--navy-dark);
  margin-bottom: px(8);
}

.subtitle {
  @include font-text-lg;
  color: var(--navy-dark);
}

.nextButton,
.prevButton {
  position: absolute;
  top: calc(50% - #{px(16)});
  transform: translateY(-50%);
  z-index: 2;
}

.nextButton {
  right: 0;
}

.prevButton {
  left: 0;
}

.pagination {
  display: flex;
  justify-content: center;
  gap: px(16);
  margin-top: px(16);
}

.paginationDot {
  @include reset-button;
  width: px(8);
  height: px(8);
  border-radius: 50%;
  background-color: var(--navy-dark);
  opacity: 0.3;
  transition: opacity $transition-short;

  &.isActive {
    opacity: 1;
  }
}
