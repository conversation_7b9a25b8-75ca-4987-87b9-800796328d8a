import type { Meta, StoryObj } from '@storybook/react'
import TextCarousel from './TextCarousel'
import { TEXT_CAROUSEL_STUB_DATA } from './TextCarousel.stub'

const meta = {
  title: 'Carousels/TextCarousel',
  component: TextCarousel,
  parameters: {
    layout: 'fullscreen',
  },
} satisfies Meta<typeof TextCarousel>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    items: TEXT_CAROUSEL_STUB_DATA.items,
  },
}
