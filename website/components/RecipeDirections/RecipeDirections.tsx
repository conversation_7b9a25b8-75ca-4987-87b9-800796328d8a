'use client'

import { PortableTextBlock } from '@portabletext/types'
import classnames from 'classnames'
import styles from './RecipeDirections.module.scss'
import RichTextV2 from '@/components/RichTextV2/RichTextV2'

interface RecipeDirectionsProps {
  instructions: PortableTextBlock[]
  className?: string
}

const RecipeDirections = ({ instructions, className }: RecipeDirectionsProps) => {
  if (!instructions || instructions.length === 0) {
    return null
  }

  return (
    <div className={classnames(styles.recipe__directions, className)}>
      <div className={styles.header}>
        <h2 className={styles.header__title}>DIRECTIONS</h2>
        <hr className={styles.header__divider} />
      </div>
      <div className={styles.steps}>
        {instructions.map((block, blockIndex) => (
          <div
            key={blockIndex}
            className={styles.step}
          >
            <div className={styles.step__number}>
              {
                // TODO: Check if Step number is in instructions
              }
              Step {blockIndex + 1}
            </div>
            <div className={styles.step__content}>
              <RichTextV2 content={[block]} />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default RecipeDirections

RecipeDirections.displayName = 'RecipeDirections'
