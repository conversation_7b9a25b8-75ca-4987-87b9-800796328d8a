'use client'

import { useState } from 'react'
import classnames from 'classnames'
import styles from './RecipeIngredients.module.scss'

interface Ingredient {
  ingredient: string
  quantity: string
  unit?: string
}

interface RecipeIngredientsProps {
  ingredients?: Ingredient[] | null
  className?: string
}

export default function RecipeIngredients({ ingredients, className }: RecipeIngredientsProps) {
  const [copied, setCopied] = useState(false)
  const hasIngredients = Array.isArray(ingredients) && ingredients.length > 0
  if (!hasIngredients) return null

  const copyToClipboard = async () => {
    try {
      const text = ingredients
        .map(({ quantity, unit, ingredient }) => `${quantity} ${unit ? `${unit} ` : ''}${ingredient}`)
        .join('\n')

      await navigator.clipboard.writeText(text)

      // eslint-disable-next-line no-console
      console.log('Ingredients copied to clipboard!')
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  return (
    <section
      className={classnames(styles.RecipeIngredients, className)}
      aria-labelledby="ingredients-title"
    >
      <div className={styles.header}>
        <h2
          id="ingredients-title"
          className={styles.title}
        >
          Ingredients
        </h2>
        <button
          onClick={copyToClipboard}
          className={classnames(styles.copyButton, {
            [styles.copied]: copied,
          })}
          aria-label="Copy ingredients to clipboard"
        >
          <CopyIcon className={styles.copyIcon} />
          {copied ? 'Copied' : 'Copy'}
        </button>
      </div>
      <ul
        className={styles.ingredientsList}
        aria-label="List of ingredients"
      >
        {ingredients.map((item, index) => (
          <li
            key={index}
            className={styles.ingredientItem}
          >
            <span className={styles.quantity}>{item.quantity}</span>
            <span className={styles.ingredient}>
              {item.unit ? `${item.unit} ` : ''}
              {item.ingredient}
            </span>
          </li>
        ))}
      </ul>
    </section>
  )
}

const CopyIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 4V16C8 16.5304 8.21071 17.0391 8.58579 17.4142C8.96086 17.7893 9.46957 18 10 18H18C18.5304 18 19.0391 17.7893 19.4142 17.4142C19.7893 17.0391 20 16.5304 20 16V7.242C20 6.97556 19.9467 6.71181 19.8433 6.46624C19.7399 6.22068 19.5885 5.99824 19.398 5.812L16.188 2.602C16.0018 2.41148 15.7793 2.26012 15.5338 2.15673C15.2882 2.05334 15.0244 2.00001 14.758 2H10C9.46957 2 8.96086 2.21071 8.58579 2.58579C8.21071 2.96086 8 3.46957 8 4Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16 18V20C16 20.5304 15.7893 21.0391 15.4142 21.4142C15.0391 21.7893 14.5304 22 14 22H6C5.46957 22 4.96086 21.7893 4.58579 21.4142C4.21071 21.0391 4 20.5304 4 20V8C4 7.46957 4.21071 6.96086 4.58579 6.58579C4.96086 6.21071 5.46957 6 6 6H8"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

RecipeIngredients.displayName = 'RecipeIngredients'
