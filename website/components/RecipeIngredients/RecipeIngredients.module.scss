.RecipeIngredients {
  margin: px(32) 0;
  max-width: px(400);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: px(24);
  border-bottom: 1px solid var(--slate);
  padding-bottom: px(8);
}

.title {
  @include font-heading-eyebrow-md;
  margin: 0;
  color: var(--navy-dark);
}

.copyButton {
  @include reset-button;
  @include font-text-sm;
  display: flex;
  align-items: center;
  gap: px(4);
  padding: px(4) px(8);
  color: var(--navy-dark);
  transition:
    background-color $transition-short,
    color $transition-short;

  &.copied {
    color: var(--loud-warm);
  }

  &:not(.copied) {
    @include hover {
      background-color: var(--highlight);
    }
  }
}

.copyIcon {
  display: inline-block;
  width: px(14);
  height: px(14);
}

.ingredientsList {
  @include reset-ul;
  display: flex;
  flex-direction: column;
  gap: px(20);
  width: 100%;
}

.ingredientItem {
  display: grid;
  grid-template-columns: px(24) 1fr;
  gap: px(12);
  align-items: baseline;
  margin-bottom: 0;
}

.quantity {
  @include font-heading-md;
  text-align: right;
  justify-self: end;
  white-space: nowrap;
  font-feature-settings: 'tnum';
  color: var(--navy-dark);
}

.ingredient {
  @include font-text-lg;
  color: var(--navy-dark);
}
