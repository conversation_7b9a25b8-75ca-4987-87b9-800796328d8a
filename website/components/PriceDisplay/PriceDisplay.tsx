'use client'

import classnames from 'classnames'
import styles from './PriceDisplay.module.scss'
import PriceDisplayProps from '@/types/components/PriceDisplayProps'
import { getDiscountPercentFromPrice, numberToPriceWithZeros } from '@/lib/utils/price'

const PriceDisplay = ({ className, price, discountPrice, showMemberDiscountText }: PriceDisplayProps) => {
  if (!price) return null

  return (
    <p
      className={classnames(
        styles.PriceDisplay,
        { [styles.showMemberDiscountText]: showMemberDiscountText },
        { [styles.hasDiscountPrice]: discountPrice },
        className,
      )}
    >
      <span
        data-price
        data-price-text
        className={styles.price}
      >
        ${numberToPriceWithZeros(price)}
      </span>
      {discountPrice && (
        <span
          data-sale-price
          data-price-text
          className={styles.salePrice}
        >
          {showMemberDiscountText ? (
            <>
              ${numberToPriceWithZeros(discountPrice)} Store Member (save{' '}
              {getDiscountPercentFromPrice(price, discountPrice)}%)
            </>
          ) : (
            <>${numberToPriceWithZeros(discountPrice)}</>
          )}
        </span>
      )}
    </p>
  )
}

PriceDisplay.displayName = 'PriceDisplay'

export default PriceDisplay
