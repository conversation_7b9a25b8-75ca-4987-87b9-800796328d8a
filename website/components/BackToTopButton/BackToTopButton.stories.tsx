import type { Meta, StoryObj } from '@storybook/react'
import BackToTopButton from './BackToTopButton'
import React from 'react'

type BackToTopButtonProps = React.ComponentProps<typeof BackToTopButton>;

const meta = {
  title: 'Components/BackToTopButton',
  component: BackToTopButton,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    onClick: { action: 'clicked' },
    disableScrollOnClick: { control: 'boolean' },
    className: { control: 'text' },
  },
} satisfies Meta<typeof BackToTopButton>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    disableScrollOnClick: false,
  },
}

export const WithCustomClassName: Story = {
  args: {
    className: 'custom-class',
    disableScrollOnClick: true,
  },
}

const DemoPage: React.FC<BackToTopButtonProps> = (props) => (
  <div style={{ position: 'relative', height: '1200px', padding: '2rem', background: '#fafafa' }}>
    <div style={{ maxWidth: 600, margin: '0 auto' }}>
      <h1>Demo Page {props.disableScrollOnClick ? 'Without' : 'With'} Scroll</h1>
      <p>This story demonstrates the BackToTopButton in a scrollable context. Scroll down and click the button to see the scroll effect.</p>
      {[...Array(30)].map((_, i) => (
        <p key={i}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque imperdiet, nisi nec tincidunt luctus, sem sapien dictum urna, nec dictum lorem lacus nec velit.
        </p>
      ))}
    </div>
    <div style={{ position: 'fixed', right: 40, bottom: 40, zIndex: 10 }}>
      <BackToTopButton {...props} />
    </div>
  </div>
)

export const Page: Story = {
  render: (args) => <DemoPage {...args} />,
  parameters: {
    layout: 'fullscreen',
  },
  args: {
    disableScrollOnClick: false,
  },
}

export const PageDisabledScroll: Story = {
  render: (args) => <DemoPage {...args} />,
  parameters: {
    layout: 'fullscreen',
  },
  args: {
    disableScrollOnClick: true,
  },
}
