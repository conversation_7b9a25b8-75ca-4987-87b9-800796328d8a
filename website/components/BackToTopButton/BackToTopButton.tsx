'use client'

import classnames from 'classnames'
import { MouseEvent } from 'react'
import styles from './BackToTopButton.module.scss'
import Icon from '../Icon/Icon'

const BackToTopButton = ({
  className,
  onClick,
  disableScrollOnClick = false,
}: BackToTopButtonProps) => {
  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    if (onClick) {
      onClick(event)
    }

    if (!disableScrollOnClick) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
    }
  }

  return (
    <button
      className={classnames(styles.BackToTopButton, className)}
      onClick={handleClick}
      aria-label="Scroll back to top"
    >
      <Icon name="caretUp" className={styles.icon} />
      <div className={styles.text}>
        Back up
        <br />
        Top
      </div>
    </button>
  )
}

BackToTopButton.displayName = 'BackToTopButton'

export default BackToTopButton
