.recipeHeader {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;

  @include bp(tablet) {
    display: grid;
    grid-template-columns: minmax(1px, 0.66fr) minmax(1px, 0.34fr);
    gap: px(40);
  }

  @include bp(laptop) {
    gap: px(80);
    align-items: center;
  }
}

.imageContainer {
  width: 100%;
  aspect-ratio: 361/203;
  position: relative;
  margin-bottom: px(16);

  @include bp(tablet) {
    margin-bottom: 0;
    aspect-ratio: 800/623;
  }
}

.image {
  @include position-100(absolute);
  object-fit: cover;
}

.content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.title {
  @include font-heading-xl;
  color: var(--navy-dark);
  margin-bottom: px(16);

  @include bp(tablet) {
    margin-bottom: px(24);
  }
}

.description {
  @include font-heading-sm;
  color: var(--slate);
  font-style: italic;
  margin-bottom: px(16);

  @include bp(tablet) {
    margin-bottom: px(24);
  }
}

.meta {
  @include font-byline-md;
  color: var(--slate);

  time {
    font-style: normal;
  }
}
