'use client'

import { formatDate } from '@/lib/utils/date'
import classnames from 'classnames'
import SanityImage from '@/components/SanityImage/SanityImage'
import styles from './RecipeHeader.module.scss'

interface RecipeHeaderProps {
  title: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  mainImage: any
  description?: string
  author: {
    name: string
  }
  publishedAt: string
  className?: string
}

export default function RecipeHeader({
  title,
  mainImage,
  author,
  publishedAt,
  description,
  className,
}: RecipeHeaderProps) {
  return (
    <div className={classnames(styles.recipeHeader, className)}>
      <div className={styles.imageContainer}>
        <SanityImage
          source={mainImage}
          isCover={true}
          className={styles.image}
          aspectRatio={1.5}
          width={800}
          height={625}
        />
      </div>
      <div className={styles.content}>
        <div className={styles.title}>{title}</div>
        {description && <p className={styles.description}>{description}</p>}
        <div className={styles.meta}>
          {author && (
            <>
              <span className={styles.byline}>by {author.name}</span>{' '}
              <time dateTime={publishedAt}>{formatDate(publishedAt)}</time>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

RecipeHeader.displayName = 'RecipeHeader'
