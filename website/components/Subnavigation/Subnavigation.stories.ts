import type { Meta, StoryObj } from '@storybook/react'
import Subnavigation from './Subnavigation'
import { STUB_LINK } from '@/components/Link/Link.stub'

const meta = {
  title: 'Components/Subnavigation',
  component: Subnavigation,
  parameters: {
    layout: 'fullscreen',
  },
} satisfies Meta<typeof Subnavigation>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    links: [
      STUB_LINK.HOME_WITH_LABEL,
      STUB_LINK.TEST_WITH_LABEL,
      STUB_LINK.TEST_WITH_LABEL,
      STUB_LINK.TEST_WITH_LABEL,
      STUB_LINK.TEST_WITH_LABEL,
      STUB_LINK.TEST_WITH_LABEL,
      STUB_LINK.TEST_WITH_LABEL,
    ] as SanityLink[],
  },
}
