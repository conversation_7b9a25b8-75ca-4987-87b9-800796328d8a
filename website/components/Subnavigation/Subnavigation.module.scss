/* stylelint-disable-next-line block-no-empty */
.Subnavigation {
  @include hide-scrollbar;
  position: relative;
  overflow: auto;
  max-width: 100%;
  opacity: 0;
  transition: opacity $transition-short;

  &.visuallyShow {
    opacity: 1;
  }

  &.isOverflow {
    min-width: 100vw;
    margin-left: calc(var(--page-gutter) * -1);
  }
}

.inner {
  @include flex-center;

  .isOverflow & {
    justify-content: flex-start;
  }
}

.listOuter {
  .isOverflow & {
    padding: 0 var(--page-gutter);
  }
}

.list {
  @include flex-center;
  gap: px(16);
  border-bottom: 1px solid rgba($navy-dark, 0.1);
}

.list__link {
  @include font-text-base;
  font-weight: 700;
  color: var(--slate);
  padding-bottom: px(5);
  display: block;
  border-bottom: 1px solid transparent;
  margin-bottom: -1px;
  transition:
    border-color $transition-short,
    color $transition-short;
  white-space: nowrap;

  &.isActive {
    color: var(--navy-dark);
    border-color: var(--navy-dark);
  }

  @include hover {
    color: var(--navy-dark);
  }
}
