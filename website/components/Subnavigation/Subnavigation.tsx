'use client'

import classnames from 'classnames'
import styles from './Subnavigation.module.scss'
import Link from '@/components/Link/Link'
import useWindowResize from '@/hooks/use-window-resize'
import { useEffect, useRef, useState } from 'react'

const Subnavigation = ({ className, links }: SubnavigationProps) => {
  const resizeKey = useWindowResize()
  const $container = useRef<HTMLDivElement | null>(null)
  const $nav = useRef<HTMLDivElement | null>(null)
  const $listOuter = useRef<HTMLDivElement | null>(null)
  const [isOverflow, setIsOverflow] = useState(false)
  const [visuallyShow, setVisuallyShow] = useState(false)

  useEffect(() => {
    if (!$listOuter.current || !$nav.current) return
    const overflow = $listOuter.current.offsetWidth > $nav.current.offsetWidth
    setIsOverflow(overflow)

    // There might be some jarring movements without this
    setTimeout(() => {
      setVisuallyShow(true)
    }, 10)
  }, [resizeKey])

  if (!links?.length) return null

  return (
    <div
      ref={$container}
      className={classnames(
        styles.Subnavigation,
        className,
        { [styles.isOverflow]: isOverflow },
        { [styles.visuallyShow]: visuallyShow },
      )}
    >
      <nav
        ref={$nav}
        className={styles.inner}
      >
        <div
          className={styles.listOuter}
          ref={$listOuter}
        >
          <ul className={styles.list}>
            {links.map((link, i) => (
              <li
                className={styles.list__item}
                key={i}
              >
                <Link
                  className={styles.list__link}
                  activeClass={styles.isActive}
                  link={link}
                />
              </li>
            ))}
          </ul>
        </div>
      </nav>
    </div>
  )
}

Subnavigation.displayName = 'Subnavigation'

export default Subnavigation
