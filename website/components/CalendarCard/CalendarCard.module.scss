.CalendarCard {
  position: relative;
  width: 100%;
  overflow: hidden;
  display: grid;

  &[data-calendar-card-orientation='portrait'] {
    grid-template: auto 1fr auto / 1fr;
    grid-template-areas:
      'image'
      'content';
  }

  &[data-calendar-card-orientation='landscape'] {
    grid-template: 1fr auto / auto 1fr;
    grid-template-areas: 'image content';
  }
}

.imageWrapper {
  position: relative;
  overflow: hidden;
  @include flex-center;

  [data-calendar-card-orientation='portrait'] & {
    grid-area: image;
    width: 100%;
  }

  [data-calendar-card-orientation='landscape'] & {
    grid-area: image;
    grid-row: 1;
    width: px(156);
    aspect-ratio: var(--aspect-ratio-16-9);
  }
}

.content {
  display: flex;
  flex-direction: column;

  [data-calendar-card-orientation='portrait'] & {
    grid-area: content;
    padding-top: px(8);
  }

  [data-calendar-card-orientation='landscape'] & {
    grid-area: content;
    padding-left: px(16);
  }
}

.dateSquare {
  [data-calendar-card-orientation='landscape'] & {
    @include flex-center;
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--white);
    z-index: 1;
    width: px(38);
    height: px(38);
  }
}

.dateSquare__day {
  @include font-text-sm;
  color: var(--navy-dark);
  font-weight: 700;
}

.eyebrow {
  @include font-heading-eyebrow-sm;
  color: var(--slate);
  margin-bottom: px(4);

  [data-calendar-card-orientation='landscape'] & {
    @include font-heading-eyebrow-md;
  }
}

.title {
  @include font-calendar-card-title;
  color: var(--navy-dark);
  /* stylelint-disable-next-line value-no-vendor-prefix */
  display: -webkit-box;
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -webkit-box-orient: vertical;
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -webkit-line-clamp: 3;
  line-clamp: 3;
  overflow: hidden;

  [data-calendar-card-orientation='landscape'] & {
    @include font-heading-md;
  }
}

.details {
  display: flex;
  margin-top: px(6);

  [data-calendar-card-orientation='portrait'] & {
    justify-content: space-between;
  }

  [data-calendar-card-orientation='landscape'] & {
    grid-column: 2;
    grid-row: 2;
  }
}

.time {
  color: var(--slate);

  [data-calendar-card-orientation='portrait'] & {
    @include font-text-sm;
  }

  [data-calendar-card-orientation='landscape'] & {
    @include font-text-base;
  }
}

.price {
  color: var(--slate);

  [data-calendar-card-orientation='portrait'] & {
    @include font-text-sm;
  }

  [data-calendar-card-orientation='landscape'] & {
    @include font-text-base;
    font-weight: 700;
    margin-left: px(16);
  }
}

.tag {
  @include font-text-sm;
  color: var(--navy-dark);
  margin-top: px(8);
}
