'use client'

import classnames from 'classnames'
import styles from './CalendarCard.module.scss'
import SanityImage from '@/components/SanityImage/SanityImage'
import { numberToPriceWithZeros } from '@/lib/utils/price'
import { getTime } from '@/lib/utils/date'

const CalendarCard = ({
  className,
  image,
  eyebrow,
  title,
  date,
  tagLabel,
  price,
  orientation = 'portrait',
}: CalendarCardProps) => {
  if (!image || !title) {
    return null
  }

  const detailsContent = (
    <div className={styles.details}>
      {date && <div className={styles.time}>{getTime(date)}</div>}
      {price && <div className={styles.price}>${numberToPriceWithZeros(price)}</div>}
    </div>
  )

  return (
    <div
      className={classnames(styles.CalendarCard, className)}
      data-calendar-card-orientation={orientation}
    >
      <div className={styles.imageWrapper}>
        <SanityImage
          source={image}
          isCover={true}
          columns={orientation === 'portrait' ? 3 : 6}
        />
        {date && orientation === 'landscape' && (
          <div className={styles.dateSquare}>
            <span className={styles.dateSquare__day}>{new Date(date).getDate()}</span>
          </div>
        )}
      </div>
      <div className={styles.content}>
        {eyebrow && <div className={styles.eyebrow}>{eyebrow}</div>}
        <h3 className={styles.title}>{title}</h3>
        {tagLabel && <div className={styles.tag}>{tagLabel}</div>}
        {orientation === 'landscape' && detailsContent}
      </div>
      {orientation === 'portrait' && detailsContent}
    </div>
  )
}

CalendarCard.displayName = 'CalendarCard'

export default CalendarCard
