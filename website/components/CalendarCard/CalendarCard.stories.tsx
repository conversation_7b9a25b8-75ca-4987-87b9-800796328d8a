import type { Meta, StoryObj } from '@storybook/react'
import CalendarCard from './CalendarCard'
import { SANITY_IMAGE_STUB_SOURCE } from '@/components/SanityImage/SanityImage.stub'
import React from 'react'

const PortraitContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div style={{ width: '300px', height: '400px' }}>{children}</div>
)

const LandscapeContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div style={{ width: '500px', height: '200px' }}>{children}</div>
)

const meta = {
  title: 'Cards/CalendarCard',
  component: CalendarCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    image: {
      description: 'Sanity image object',
    },
    eyebrow: {
      control: 'text',
      description: 'Eyebrow text (optional)',
    },
    title: {
      control: 'text',
      description: 'Title of the card',
    },
    date: {
      control: 'text',
      description: 'ISO date string (optional)',
    },
    tagLabel: {
      control: 'text',
      description: 'Tag label (optional)',
    },
    price: {
      control: 'number',
      description: 'Price (optional)',
    },
    orientation: {
      control: 'radio',
      options: ['portrait', 'landscape'],
      description: 'Card orientation',
    },
  },
  decorators: [
    Story => (
      <div style={{ padding: '1rem' }}>
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof CalendarCard>

export default meta

type Story = StoryObj<typeof meta>

const mockImage = SANITY_IMAGE_STUB_SOURCE.LARGE_IMAGE

export const Portrait: Story = {
  args: {
    image: mockImage,
    eyebrow: 'Class Type',
    title: 'Sourdough Baking Fundamentals with Peter Reinhart',
    date: '2023-06-29T18:00:00.000Z',
    price: 29.95,
    orientation: 'portrait',
  },
  decorators: [Story => <PortraitContainer>{Story()}</PortraitContainer>],
}

export const Landscape: Story = {
  args: {
    image: mockImage,
    eyebrow: 'Class Type',
    title: 'Sourdough Baking Fundamentals with Peter Reinhart',
    date: '2023-06-29T18:00:00.000Z',
    price: 29.95,
    orientation: 'landscape',
  },
  decorators: [Story => <LandscapeContainer>{Story()}</LandscapeContainer>],
}

export const WithTag: Story = {
  args: {
    ...Portrait.args,
    tagLabel: 'Cooking Class',
  },
  decorators: [Story => <PortraitContainer>{Story()}</PortraitContainer>],
}

export const NoEyebrow: Story = {
  args: {
    ...Portrait.args,
    eyebrow: undefined,
  },
  decorators: [Story => <PortraitContainer>{Story()}</PortraitContainer>],
}

export const NoDate: Story = {
  args: {
    ...Portrait.args,
    date: undefined,
  },
  decorators: [Story => <PortraitContainer>{Story()}</PortraitContainer>],
}

export const NoPrice: Story = {
  args: {
    ...Portrait.args,
    price: undefined,
  },
  decorators: [Story => <PortraitContainer>{Story()}</PortraitContainer>],
}
