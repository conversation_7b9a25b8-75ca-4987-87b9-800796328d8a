'use client'

import classnames from 'classnames'
import styles from './ForumCard.module.scss'
import Tag from '@/components/Tag/Tag'
import Link from '@/components/Link/Link'
import Button from '@/components/Button/Button'
import { ForumCardProps } from '@/types/components/ForumCardProps'

const ForumCard = ({ className, tag, links, cta }: ForumCardProps) => {
  if (!links || links.length === 0) return null

  return (
    <div className={classnames(styles.ForumCard, className)}>
      {tag && (
        <Tag
          text={tag}
          className={styles.tag}
        />
      )}

      <div className={styles.questions}>
        {links.map((link, index) => (
          <Link
            key={index}
            link={link}
            className={styles.questionLink}
          >
            <h3 className={styles.question}>{link.label}</h3>
          </Link>
        ))}
      </div>

      {cta && (
        <div className={styles.ctaWrapper}>
          <Button
            link={cta}
            icon="arrowRight"
            iconPosition="right"
            style="bare"
            bareColor="loud"
            className={styles.cta}
          />
        </div>
      )}
    </div>
  )
}

ForumCard.displayName = 'ForumCard'

export default ForumCard
