import type { Meta, StoryObj } from '@storybook/react'
import ForumCard from './ForumCard'
import { STUB_LINK } from '../Link/Link.stub'

const meta = {
  title: 'Cards/ForumCard',
  component: ForumCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    tag: { control: 'text' },
    links: { control: 'object' },
    cta: { control: 'object' },
  },
} satisfies Meta<typeof ForumCard>

export default meta

type Story = StoryObj<typeof meta>

const links = [
  {
    ...STUB_LINK.TEST_WITH_LABEL,
    label: 'Why is my cabbage burning?',
  },
  {
    ...STUB_LINK.TEST_WITH_LABEL,
    label: 'Baking with alternative flours?',
  },
  {
    ...STUB_LINK.TEST_WITH_LABEL,
    label: 'Maple syrup',
  },
] as SanityLink[]

const cta = {
  ...STUB_LINK.TEST_WITH_LABEL,
  label: 'Ask Milk Street',
} as SanityLink

export const Default: Story = {
  args: {
    tag: 'Forum',
    links,
    cta,
  },
}

export const WithoutCta: Story = {
  args: {
    tag: 'Forum',
    links,
  },
}

export const WithCustomTag: Story = {
  args: {
    tag: 'Questions',
    links,
    cta,
  },
}

export const WithoutTag: Story = {
  args: {
    links,
    cta,
  },
}
