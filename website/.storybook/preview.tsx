import type { Preview } from '@storybook/react'
import { RouterContext } from 'next/dist/shared/lib/router-context.shared-runtime'
import '../styles/global.scss'
import { DM_Sans } from 'next/font/google'
import React, { useEffect } from 'react'
import classnames from 'classnames'
import styles from '../styles/modules/storybook.module.scss'

// Initialize DM Sans font
const dmSans = DM_Sans({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  variable: '--font-dm-sans',
})

const preview: Preview = {
  parameters: {
    backgrounds: {
      values: [
        // 👇 Default values
        { name: 'Dark', value: '#333' },
        { name: 'Light', value: '#F7F9F2' },
      ],
      // 👇 Specify which background is shown by default
      default: 'Light',
    },
    nextRouter: {
      Provider: RouterContext.Provider,
    },
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/',
      },
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  decorators: [
    Story => {
      useEffect(() => {
        document.body.classList.add(dmSans.variable)
      }, [])

      return (
        <div className={classnames(dmSans.className, styles.StorybookContainer)}>
          <div className={styles.inner}>
            <Story />
          </div>
        </div>
      )
    },
  ],
}

export default preview
