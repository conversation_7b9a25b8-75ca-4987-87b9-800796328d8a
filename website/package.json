{"name": "177-milk-street-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "npm run lint && next build", "start": "next start", "stylelint": "stylelint '**/*.scss'", "lint": "npm run stylelint && next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prebuild": "tsx scripts/prebuild.ts"}, "dependencies": {"@portabletext/block-tools": "^1.1.27", "@sanity/asset-utils": "^2.2.1", "@sanity/image-url": "^1.0.2", "@sanity/schema": "^3.91.0", "axios": "^1.9.0", "dotenv": "^16.5.0", "gsap": "^3.12.7", "html-to-text": "^9.0.5", "jsdom": "^26.1.0", "next": "15.2.3", "next-sanity": "^9.1.2", "p-queue": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "sharp": "^0.34.2", "swiper": "^11.2.6", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/nextjs": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/test": "^8.6.12", "@tailwindcss/postcss": "^4", "@types/html-to-text": "^9.0.4", "@types/node": "^20", "@types/react": "^18.2.8", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.2.3", "eslint-plugin-storybook": "^0.12.0", "sass": "^1.86.3", "sass-loader": "^16.0.5", "storybook": "^8.6.12", "stylelint": "^14.11.0", "stylelint-config-prettier": "^9.0.4", "stylelint-config-standard-scss": "^5.0.0", "tailwindcss": "^4.0.14", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.7.1", "typescript": "^5.6.2"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}