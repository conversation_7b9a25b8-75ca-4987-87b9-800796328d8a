import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Match all paths except static files, API routes, and auth routes
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - auth (auth routes)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|auth).*)',
  ],
}

export function middleware(request: NextRequest) {
  // Debug logging
  // console.log('Middleware running for path:', request.nextUrl.pathname)
  // console.log('AUTH_ENABLED:', process.env.AUTH_ENABLED)
  // console.log('AUTH_PASSWORD exists:', !!process.env.AUTH_PASSWORD)

  // Check if authentication is enabled
  if (process.env.AUTH_ENABLED !== 'true') {
    // console.log('Authentication disabled, allowing access')
    return NextResponse.next()
  }

  // Get auth token from cookies
  const authToken = request.cookies.get('auth-token')
  const hasValidToken = authToken?.value === process.env.AUTH_PASSWORD

  // / console.log('Has valid token:', hasValidToken)

  if (!hasValidToken) {
    // console.log('No valid token, redirecting to auth page')
    // Redirect to auth page if no valid token
    const url = request.nextUrl.clone()
    url.pathname = '/auth/index.html'
    const response = NextResponse.rewrite(url)
    response.headers.set('Cache-Control', 'no-store')
    return response
  }

  // console.log('Valid token found, allowing access')
  return NextResponse.next()
}
