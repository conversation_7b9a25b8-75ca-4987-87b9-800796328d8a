import { SanityPage } from '@/types/sanity/SanityPage'
import './loadEnv'
import { DOC_TYPES } from '../data'
import { getPageDataByType } from '@/data/sanity'
import path from 'path'
import fs from 'fs'
const main = async () => {
  try {
    const slugsToPush: string[] = []
    const allPages = (await getPageDataByType[DOC_TYPES.PAGE](undefined, { fetchAll: true })) as SanityPage[]
    allPages.forEach(page => {
      if (page.slug.current) {
        slugsToPush.push(page.slug.current)
      }
    })

    fs.writeFileSync(
      path.join(__dirname, '../data/pageSlugs.ts'),
      `
// File is auto-generated by scripts/prebuild.ts\n\n
export const pageSlugs = ${JSON.stringify(slugsToPush, null)}\n
`,
    )

    // eslint-disable-next-line no-console
    console.log('Successfully wrote pageSlugs.ts')
  } catch (error) {
    console.error('Error in main:', error)
    throw error
  }
}

main()
