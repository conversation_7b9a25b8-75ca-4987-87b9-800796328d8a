<!DOCTYPE html>
<html>
  <head>
    <title>Authenticate</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
        background-color: #f5f5f5;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .container {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 400px;
      }

      .form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .title {
        margin: 0 0 1rem;
        text-align: center;
        color: #333;
        font-size: 1.5rem;
      }

      .input {
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
        width: 100%;
        box-sizing: border-box;
      }

      .input:focus {
        outline: none;
        border-color: #0070f3;
        box-shadow: 0 0 0 2px rgba(0, 112, 243, 0.1);
      }

      .button {
        background: #0070f3;
        color: white;
        border: none;
        padding: 0.75rem;
        border-radius: 4px;
        font-size: 1rem;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .button:hover {
        background: #0051b3;
      }

      .button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }

      .errorMessage {
        color: #dc2626;
        margin: 0;
        text-align: center;
        display: none;
      }

      .success {
        color: #059669;
        margin: 0;
        text-align: center;
        display: none;
      }

      .loading .button {
        opacity: 0.7;
        cursor: not-allowed;
      }

      .error .errorMessage {
        display: block;
      }

      .submit-success .success {
        display: block;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <form id="form" class="form">
        <h1 class="title">Enter password</h1>
        <input
          id="input"
          class="input"
          type="password"
          placeholder="Password"
          autocomplete="off"
          required
        />
        <button type="submit" class="button" id="button">
          Submit
        </button>
        <p id="success" class="success">
          Success! Redirecting...
        </p>
        <p id="errorMessage" class="errorMessage">
          Incorrect password. Please try again.
        </p>
      </form>
    </div>

    <script>
      window.isLoading = false
      const form = document.getElementById('form')

      form.addEventListener('submit', e => {
        e.preventDefault()

        const input = document.getElementById('input')
        const pass = input.value

        if (window.isLoading || !pass) return

        window.isLoading = true
        document.body.classList.add('loading')
        document.body.classList.remove('error')

        fetch('/api/auth', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ pass }),
        })
          .then(response => {
            if (response.ok) {
              document.body.classList.add('submit-success')
              // Redirect after a short delay
              setTimeout(() => {
                window.location.href = '/'
              }, 1000)
            } else {
              document.body.classList.add('error')
            }
          })
          .catch(err => {
            document.body.classList.add('error')
          })
          .finally(() => {
            window.isLoading = false
            document.body.classList.remove('loading')
          })
      })
    </script>
  </body>
</html> 