/* eslint-disable */
import { NextRequest, NextResponse } from 'next/server'
import axios from 'axios'

export const config = {
  api: {
    bodyParser: false, // required to access the raw stream
  },
}

const TIMEOUT = 3000

export async function POST(req: NextRequest) {
  const isDevelopment = process.env.NODE_ENV === 'development'

  const reader = req.body?.getReader()
  if (!reader) {
    console.error('ShopifyHandler: No body found')
    return new Response('ShopifyHandler: No body found', { status: 200 })
  }

  const decoder = new TextDecoder()
  let result = ''
  let done = false

  while (!done) {
    const { value, done: readerDone } = await reader.read()
    done = readerDone
    if (value) {
      result += decoder.decode(value, { stream: !done })
    }
  }

  let data: any = null

  // Try to parse as JSON
  try {
    data = JSON.parse(result)
  } catch (error) {
    console.error('ShopifyHandler: Invalid JSON')
    return NextResponse.json({ message: 'ShopifyHandler: Invalid JSON', error }, { status: 200 })
  }

  if (!data) {
    console.error('ShopifyHandler: No data')
    return NextResponse.json({ message: 'ShopifyHandler: No data' }, { status: 200 })
  }

  // Dont async await this, it will block the request
  axios.post(
    `${process.env.NEXT_PUBLIC_SITE_URL}/api/shopifySync`,
    {
      ...data,
    },
    {
      headers: {
        'Content-Type': 'application/json',
      },
    },
  )

  // pace calls
  if (isDevelopment) {
    console.log(`ShopifyHandler: Waiting for timeout to resolve (${TIMEOUT / 1000} seconds)`)
  }
  await new Promise(resolve => setTimeout(resolve, TIMEOUT))

  return NextResponse.json({ message: 'Successfully posted data' }, { status: 200 })
}
