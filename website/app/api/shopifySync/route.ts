/* eslint-disable */

import { createClient } from '@sanity/client'
import { handleProductUpdate } from './productUpdate'
import { deleteCollectionDocuments, deleteProductDocuments } from './sanityOps'
import { handleCollectionUpdate } from './collectionUpdate'
import { NextRequest, NextResponse } from 'next/server'

export const config = {
  api: {
    bodyParser: false, // required to access the raw stream
  },
}

const SANITY_CONFIG = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION,
  token: process.env.SANITY_MIGRATION_TOKEN,
  useCdn: false,
}

const sanityClient = createClient({
  ...SANITY_CONFIG,
})

export async function POST(req: NextRequest) {
  const reader = req.body?.getReader()
  if (!reader) {
    console.error('ShopifySync: No body found')
    return new Response('ShopifySync: No body found', { status: 400 })
  }

  const decoder = new TextDecoder()
  let result = ''
  let done = false

  while (!done) {
    const { value, done: readerDone } = await reader.read()
    done = readerDone
    if (value) {
      result += decoder.decode(value, { stream: !done })
    }
  }

  let data: any = null

  // Try to parse as JSON
  try {
    data = JSON.parse(result)
  } catch (error) {
    console.error('ShopifySync: Invalid JSON')
    return NextResponse.json({ message: 'ShopifySync: Invalid JSON', error }, { status: 400 })
  }

  if (!data) {
    console.error('ShopifySync: No data')
    return NextResponse.json({ message: 'ShopifySync: No data' }, { status: 400 })
  }

  // console.log('data', data)

  // return NextResponse.json({ message: 'TEST SUCCESS' }, { status: 200 })

  const isDevelopment = process.env.NODE_ENV === 'development'

  try {
    if (['create', 'update', 'sync'].includes(data.action) && !!data?.products?.length) {
      if (isDevelopment) {
        console.log('Updating product: ', data.products.length ? data.products[0].title : 'unknown')
      }
      for (const product of data.products) {
        await handleProductUpdate(sanityClient, product)
      }
    } else if (data.action === 'delete' && !!data?.productIds?.length) {
      if (isDevelopment) {
        console.log('Deleting product: ', data.productIds.length ? data.productIds[0] : 'unknown')
      }
      for (const productId of data.productIds) {
        await deleteProductDocuments(sanityClient, productId)
      }
    } else if (['create', 'update', 'sync'].includes(data.action) && !!data?.collections?.length) {
      if (isDevelopment) {
        console.log('Updating collection: ', data.collections.length ? data.collections[0].title : 'unknown')
      }
      for (const collection of data.collections) {
        await handleCollectionUpdate(sanityClient, collection)
      }
    } else if (data.action === 'delete' && !!data?.collectionIds?.length) {
      if (isDevelopment) {
        console.log('Deleting collection: ', data.collectionIds.length ? data.collectionIds[0] : 'unknown')
      }
      for (const collectionId of data.collectionIds) {
        await deleteCollectionDocuments(sanityClient, collectionId)
      }
    }

    if (isDevelopment) {
      console.log('Complete! Moving on...')
    }

    return NextResponse.json(
      { message: 'ShopifySync: Successfully synced with Shopify' },
      { status: 200, headers: { 'content-type': 'application/json' } },
    )
  } catch (error) {
    console.error('error', error)
    NextResponse.json({ message: 'Error syncing with Shopify', error }, { status: 400 })
  }
}
