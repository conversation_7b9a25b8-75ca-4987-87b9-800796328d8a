import { NextRequest, NextResponse } from 'next/server'
import { revalidatePath } from 'next/cache'
import { DIRECTORY_NAMES, DOC_TYPES, HOME_SLUG } from '@/data'
import { client } from '@/data/sanity'
import { groq } from 'next-sanity'

export const config = {
  api: {
    bodyParser: false, // required to access the raw stream
  },
}

export async function POST(req: NextRequest) {
  try {
    const chunks = []

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const body: any = req.body

    if (!body) {
      return NextResponse.json({ message: 'No body' }, { status: 400 })
    }

    for await (const chunk of body) {
      chunks.push(chunk)
    }

    const buffer = Buffer.concat(chunks)

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const docData: any = JSON.parse(buffer.toString('utf8')) // or use 'base64' for binary

    if (!docData?.slug) {
      return NextResponse.json({ message: 'No slug' }, { status: 400 })
    }

    const path = getPathFromTypeAndSlug(docData.type, docData.slug)

    if (path) {
      revalidatePath(path)
    }

    const referencesToPath = await client.fetch(
      groq`
        *[${Object.values(DOC_TYPES)
          .map(type => `_type == "${type}"`)
          .join(' || ')} && references("${docData.id}")] {
          "type": _type,
          "slug": slug.current,
        }
      `,
    )

    const itemsToRevalidate = []

    for (const reference of referencesToPath) {
      const pathToInvalidate = getPathFromTypeAndSlug(reference.type, reference.slug)
      if (pathToInvalidate) {
        itemsToRevalidate.push(pathToInvalidate)
        revalidatePath(pathToInvalidate)
      }
    }

    return NextResponse.json(
      {
        message: {
          revalidated: itemsToRevalidate,
        },
      },
      { status: 200 },
    )
  } catch (error) {
    console.error('Failed to revaliate:', error)
  }
}

const getPathFromTypeAndSlug = (type: string, slug: string) => {
  if (type === DOC_TYPES.PAGE) {
    if (slug === HOME_SLUG) {
      return '/'
    }
    return `/${slug}`
  } else if (type === DOC_TYPES.RECIPE) {
    return `/${DIRECTORY_NAMES.RECIPES}/${slug}`
  } else if (type === DOC_TYPES.MAGAZINE) {
    return `/${DIRECTORY_NAMES.MAGAZINE}/${slug}`
  } else if (type === DOC_TYPES.ARTICLE) {
    return `/${DIRECTORY_NAMES.ARTICLE}/${slug}`
  } else if (type === DOC_TYPES.RADIO_EPISODE) {
    return `/${DIRECTORY_NAMES.RADIO}/${slug}`
  } else if (type === DOC_TYPES.TV_EPISODE) {
    return `/${DIRECTORY_NAMES.TV}/${slug}`
  }

  return null
}
