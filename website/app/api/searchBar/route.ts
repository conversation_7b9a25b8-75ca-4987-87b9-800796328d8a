import { SEARCH_PER_PAGE } from '@/data'
import { getSearchBarContent } from '@/data/sanity'
import { NextRequest, NextResponse } from 'next/server'

// export const config = {
//   api: {
//     bodyParser: false, // required to access the raw stream
//   },
// }

export async function POST(req: NextRequest) {
  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const body: any = req.body

    if (!body) {
      return NextResponse.json({ message: 'No body' }, { status: 400 })
    }

    const chunks = []

    for await (const chunk of body) {
      chunks.push(chunk)
    }

    const buffer = Buffer.concat(chunks)
    const data = JSON.parse(buffer.toString('utf8'))
    const page = data.page

    if (page === undefined || page === null) {
      return NextResponse.json({ message: 'No page' }, { status: 400 })
    }

    if (!data.typesToQuery) {
      return NextResponse.json({ message: 'No typesToQuery' }, { status: 400 })
    }

    let categoryFilters = null
    if (Object.keys(data?.categoryFilters).length > 0) {
      categoryFilters = data.categoryFilters
    }

    const offset = page * SEARCH_PER_PAGE
    const limit = offset + SEARCH_PER_PAGE

    const options = {
      typesToQuery: data.typesToQuery,
      offset,
      limit,
      categoryFilters,
    }

    const results = await getSearchBarContent(options)

    return NextResponse.json(results, { status: 200 })
  } catch (error) {
    console.error('Failed to revaliate:', error)
  }
}
