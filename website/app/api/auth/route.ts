import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const { pass } = await request.json()

    if (pass !== process.env.AUTH_PASSWORD) {
      return NextResponse.json({ error: 'Invalid password' }, { status: 401 })
    }

    // Calculate expiry time
    const expiryMinutes = parseInt(process.env.AUTH_EXPIRY_MINUTES || '1440')
    const expiryDate = new Date()
    expiryDate.setMinutes(expiryDate.getMinutes() + expiryMinutes)

    // Set the auth cookie
    ;(
      await // Set the auth cookie
      cookies()
    ).set('auth-token', process.env.AUTH_PASSWORD!, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      expires: expiryDate,
      path: '/',
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Authentication error:', error)
    return NextResponse.json({ error: 'Invalid request' }, { status: 400 })
  }
}
