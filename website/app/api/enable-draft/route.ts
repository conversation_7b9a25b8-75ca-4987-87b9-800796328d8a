import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Enable draft mode by setting a cookie
    const response = NextResponse.json({ success: true })

    response.cookies.set('draftMode', 'true', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
    })

    return response
  } catch (error) {
    console.error('Failed to enable draft mode:', error)
    return NextResponse.json({ error: 'Failed to enable draft mode' }, { status: 500 })
  }
}
