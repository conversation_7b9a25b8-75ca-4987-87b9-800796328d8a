/* eslint-disable */
import { createClient, groq } from 'next-sanity'

const SANITY_CONFIG = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION,
  token: process.env.SANITY_MIGRATION_TOKEN,
  useCdn: false,
}

const sanityClient = createClient({
  ...SANITY_CONFIG,
})

const COLLECTION_QUERY = groq`_type == "shopifyCollection" && count(store.rules) > 0`
const MAX_PRODUCTS_PER_COLLECTION = 20
const COLLECTION_FIELDS = groq`
  _id,
  store {
    title,
  },
  "rules": store.rules[]{
    "rule": column + " " + relation + " " + condition,
    column,
    relation,
    condition
  }
`

/*
const getAllCollectionConditions = async () => {
  const client = getSanityClient()

  const collections = await client.fetch(groq`
    *[${COLLECTION_QUERY}] {
      ${COLLECTION_FIELDS}
    }
  `)

  const columnHash: any = {}
  const relationHash: any = {}
  // const conditionHash: any = {}

  collections.forEach((collection: any) => {
    collection.rules.forEach((rule: any) => {
      if (!columnHash[rule?.column]) {
        columnHash[rule.column] = rule.condition
      }
      //  else {
      //   columnHash[rule.column] = [...columnHash[rule.column], collection.store.title]
      // }
      if (!relationHash[rule?.relation]) {
        relationHash[rule.relation] = rule.condition
      }
    })
  })

  console.log(columnHash)
  console.log(relationHash)
  // console.log(conditionHash)
}
*/

/*
  {
    TITLE: true,
    TAG: true,
    VARIANT_INVENTORY: true,
    VENDOR: true,
    VARIANT_COMPARE_AT_PRICE: true,
    TYPE: true,
    VARIANT_PRICE: true,
    IS_PRICE_REDUCED: true,
  }
  {
    CONTAINS: true,
    EQUALS: true,
    GREATER_THAN: true,
    NOT_CONTAINS: true,
    NOT_EQUALS: true,
    LESS_THAN: true,
  }
  */

const valueToExpectedType = (value: any, relation: string) => {
  if (relation === 'GREATER_THAN' || relation === 'LESS_THAN') {
    return parseInt(value)
  }

  return `${value}`
}

const COLUMN_TO_FIELD_MAP = {
  // search for shopifyProduct
  TITLE: 'store.title',
  // search for shopifyProduct
  // "balls" in tags
  TAG: 'store.tags',
  // search for shopifyProductVariant
  VARIANT_INVENTORY: 'store.inventoryQuantity',
  // search for shopifyProduct
  VENDOR: 'store.vendor',
  // search for shopifyProductVariant
  VARIANT_COMPARE_AT_PRICE: 'store.compareAtPrice',
  // search for shopifyProduct
  TYPE: 'store.productType',
  // search for shopifyProductVariant
  VARIANT_PRICE: 'store.price',
  // shopifyProductVariant -> compareAtPrice is not empty
  IS_PRICE_REDUCED: 'store.compareAtPrice',
}

// {
//   CONTAINS: true,
//   EQUALS: true,
//   GREATER_THAN: true,
//   NOT_CONTAINS: true,
//   NOT_EQUALS: true,
//   LESS_THAN: true,
// }

const getQueryFromRule = (column: string, relation: string, condition: any) => {
  /*
  *[
    _type == "shopifyProduct" &&
    references(*[_type == "shopifyProductVariant" && store.inventory.quantity > 0]._id)
  ] {
    "title": store.title
  }
  */

  const fieldToQuery = COLUMN_TO_FIELD_MAP[column as keyof typeof COLUMN_TO_FIELD_MAP]

  // console.log(fieldToQuery)

  if (!fieldToQuery) return ''

  let query = ''

  switch (column) {
    /* ========================================== */
    case 'TITLE':
      switch (relation) {
        case 'CONTAINS':
          query = groq`${fieldToQuery} match "*${condition}*"`
          break
        case 'EQUALS':
          query = groq`${fieldToQuery} == "${condition}"`
          break
        case 'NOT_CONTAINS':
          query = groq`!(${fieldToQuery} match "*${condition}*")`
          break
        case 'NOT_EQUALS':
          query = groq`${fieldToQuery} != "${condition}"`
          break
      }
      break
    /* ========================================== */
    case 'TAG':
      switch (relation) {
        case 'CONTAINS':
          query = groq`"${condition}" in ${fieldToQuery}`
          break
        case 'EQUALS':
          query = groq`"${condition}" in ${fieldToQuery}`
          break
        case 'NOT_CONTAINS':
          query = groq`!("${condition}" in ${fieldToQuery})`
          break
        case 'NOT_EQUALS':
          query = groq`!("${condition}" in ${fieldToQuery})`
          break
      }
      break
    /* ========================================== */
    case 'VARIANT_INVENTORY':
      switch (relation) {
        case 'EQUALS':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} == ${condition}]._id)`
          break
        case 'GREATER_THAN':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} > ${condition}]._id)`
          break
        case 'NOT_EQUALS':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} != ${condition}]._id)`
          break
        case 'LESS_THAN':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} < ${condition}]._id)`
          break
      }
      break
    /* ========================================== */
    case 'VENDOR':
      switch (relation) {
        case 'CONTAINS':
          query = groq`${fieldToQuery} match "*${condition}*"`
          break
        case 'EQUALS':
          query = groq`${fieldToQuery} == "${condition}"`
          break
        case 'NOT_CONTAINS':
          query = groq`!(${fieldToQuery} match "*${condition}*")`
          break
        case 'NOT_EQUALS':
          query = groq`${fieldToQuery} != "${condition}"`
          break
      }
      break
    /* ========================================== */
    case 'VARIANT_COMPARE_AT_PRICE':
      switch (relation) {
        case 'EQUALS':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} == ${condition}]._id)`
          break
        case 'GREATER_THAN':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} > ${condition}]._id)`
          break
        case 'NOT_EQUALS':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} != ${condition}]._id)`
          break
        case 'LESS_THAN':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} < ${condition}]._id)`
          break
      }
      break
    /* ========================================== */
    case 'TYPE':
      switch (relation) {
        case 'CONTAINS':
          query = groq`${fieldToQuery} match "*${condition}*"`
          break
        case 'EQUALS':
          query = groq`${fieldToQuery} == "${condition}"`
          break
        case 'NOT_CONTAINS':
          query = groq`!(${fieldToQuery} match "*${condition}*")`
          break
        case 'NOT_EQUALS':
          query = groq`${fieldToQuery} != "${condition}"`
          break
      }
      break
    /* ========================================== */
    case 'VARIANT_PRICE':
      switch (relation) {
        case 'EQUALS':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} == ${condition}]._id)`
          break
        case 'GREATER_THAN':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} > ${condition}]._id)`
          break
        case 'NOT_EQUALS':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} != ${condition}]._id)`
          break
        case 'LESS_THAN':
          query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery} < ${condition}]._id)`
          break
      }
      break
    /* ========================================== */
    case 'IS_PRICE_REDUCED':
      query = groq`references(*[_type == "shopifyProductVariant" && ${fieldToQuery}]._id)`
      break
    default:
      break
  }

  return query
}

export const updateCollectionsWithProductReferences = async () => {
  const client = sanityClient

  const collections = await client.fetch(groq`
    *[${COLLECTION_QUERY}] {
      ${COLLECTION_FIELDS}
    }
  `)

  // collections = collections.slice(0, 10)

  const collectionQueries: any = []

  /* ========================================== 
  GET QUERIES
  ========================================== */
  for (const collection of collections) {
    const collectionRules = collection.rules

    let queries: any = []

    for (const rule of collectionRules) {
      const { column, relation, condition } = rule
      const value = valueToExpectedType(condition, relation)
      const query = getQueryFromRule(column, relation, value)
      queries.push(query)
    }

    const query = queries
      .filter((query: any) => query)
      .map((query: any) => `(${query})`)
      .join(' && ')

    // const products = await client.fetch(groq`
    //   *[_type == "shopifyProduct" && ${query}] {
    //     _id,
    //     "title": store.title
    //   }
    // `)

    const productsQuery = groq`
      *[_type == "shopifyProduct" && store.status == "active" && store.isDeleted == false && ${query}][0...${MAX_PRODUCTS_PER_COLLECTION}] {
        _id
      }
    `

    if (collection._id && query) {
      collectionQueries.push({
        collectionId: collection._id,
        productsQuery,
      })
    }
  }

  const collectionProductsBatchSize = 50
  const getProductsBatches = []

  for (let i = 0; i < collectionQueries?.length; i += collectionProductsBatchSize) {
    const batch = collectionQueries.slice(i, i + collectionProductsBatchSize)
    getProductsBatches.push(batch)
  }

  const productsByCollectionId: any = {}

  let incyWinky = 0
  for (const batch of getProductsBatches) {
    await Promise.all(
      batch.map(async (transaction: any) => {
        const products = await client.fetch(transaction.productsQuery)
        if (!!products?.length) {
          return (productsByCollectionId[transaction.collectionId] = products)
        }
        return
      }),
    )
    await new Promise(resolve => setTimeout(resolve, 1000))
    process.stdout.write(`\rProcessed productsByCollectionId batch ${incyWinky + 1} of ${getProductsBatches.length}`)
    incyWinky++
  }

  const transactions: any = []
  const collectionIds = Object.keys(productsByCollectionId)

  for (const key of collectionIds) {
    const products = productsByCollectionId[key]
    if (!!products?.length) {
      const productIdsAsReferences = products
        .filter((product: any) => product._id)
        .map((product: any, index: number) => ({
          _type: 'reference',
          _ref: product._id,
          _key: `product-${index}`,
        }))
      transactions.push(client.patch(key).set({ 'store.products': productIdsAsReferences }))
    }
  }

  const batchSize = 50
  const batches = []

  for (let i = 0; i < transactions.length; i += batchSize) {
    const batch = transactions.slice(i, i + batchSize)
    batches.push(batch)
  }

  let i = 0
  for (const batch of batches) {
    await Promise.all(batch.map((transaction: any) => transaction.commit()))
    await new Promise(resolve => setTimeout(resolve, 1000))
    process.stdout.write(`\rProcessed updating collections with product references batch ${i + 1} of ${batches.length}`)
    i++
  }

  process.stdout.write('\n')

  console.log('Added product references to collections.')
}

// updateCollectionsWithProductReferences()
