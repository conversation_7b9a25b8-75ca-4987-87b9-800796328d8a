import { NextResponse } from 'next/server'
import { updateCollectionsWithProductReferences } from './helper'

export async function GET() {
  try {
    await updateCollectionsWithProductReferences()
    return NextResponse.json({ message: 'Successfully updated collections with products' }, { status: 200 })
  } catch (error) {
    console.error(error)
    return NextResponse.json({ message: 'Error updating collections with products', error }, { status: 500 })
  }
}
