import type { Metadata } from 'next'
import { DM_Sans } from 'next/font/google'
import '@/styles/global.scss'

import Header from '@/components/Header/Header'
import Footer from '@/components/Footer/Footer'
import { getSiteSettings, getHeaderNav } from '@/data/sanity'
import { urlFor } from '@/data/sanity'
import { NavItem, SiteSettings } from '@/types'
import { SanityImage } from '@/types/sanity/SanityImage'

export const revalidate = 10

// Initialize DM Sans font
const dmSans = DM_Sans({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  variable: '--font-dm-sans',
})

export const metadata: Metadata = {
  title: 'Milk Street',
  // eslint-disable-next-line
  description: "<PERSON>'s Milk Street: Recipes, Cooking Classes, and More",
}

export interface HeaderData {
  signInLink: SanityLink
  logInLink: SanityLink
  headerNav: NavItem[]
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  // Fetch site settings and header navigation
  const [siteSettings, headerData] = await Promise.all([
    getSiteSettings() as Promise<SiteSettings>,
    getHeaderNav() as Promise<HeaderData>,
  ])

  // Process logo for client component
  const processedSettings: SiteSettings = {
    ...siteSettings,
    logo: siteSettings?.logo
      ? {
          url: urlFor(siteSettings.logo as SanityImage).url(),
          width: (siteSettings.logo as SanityImage)?.asset?.width,
          height: (siteSettings.logo as SanityImage)?.asset?.height,
        }
      : null,
    footerLogo: siteSettings?.footerLogo
      ? {
          url: urlFor(siteSettings.footerLogo as SanityImage).url(),
          width: (siteSettings.footerLogo as SanityImage)?.asset?.width,
          height: (siteSettings.footerLogo as SanityImage)?.asset?.height,
        }
      : null,
  }

  // Ensure headerNav is properly structured
  const headerNav = headerData?.headerNav || []

  // Process navigation items to ensure proper link structure
  const processedNav = headerNav.map((item: NavItem) => ({
    ...item,
    isLink: item.link ? true : item.isLink,
  }))

  return (
    <html
      lang="en"
      className={dmSans.variable}
    >
      <body className="antialiased">
        <Header
          siteSettings={processedSettings}
          headerNav={processedNav}
          headerData={headerData}
        />
        <main>{children}</main>
        <Footer siteSettings={processedSettings} />
      </body>
    </html>
  )
}
