import { draftMode } from 'next/headers'
import { SanityPageType } from '@/types/sanity/SanityPage'
import { Metadata } from 'next'
import { DIRECTORY_NAMES, DOC_TYPES, HOME_SLUG } from '@/data'

import PageHandler from '@/components/PageHandler/PageHandler'
import NextPageParams from '@/types/next/NextPageParams'
import getPageMetadata from '@/lib/utils/getPageMetadata'
// import useStore from '@/store'
import {
  removeLeadingSlash,
  slugArrayToSlug,
  slugPathToSlugArray,
  // slugPathToSlugArray
} from '@/lib/utils'
import { client } from '@/data/sanity'
import { pageSlugs } from '@/data/pageSlugs'

// export const dynamic = 'force-dynamic'
// Ensures all paths that dont exist are 404
export const dynamicParams = false

// this allows for new pages to be added and revalidated
// export const dynamicParams = true
// export const revalidate = 60

const getDocTypeAndSlugFromParams = async (params: { slug: string[] }) => {
  if (!pageSlugs?.length) {
    throw new Error('No page slugs found')
  }

  let docType: string = DOC_TYPES.PAGE

  // No slug, show home
  if (!params?.slug?.length || Object.keys(params).length === 0) {
    return { docType: DOC_TYPES.PAGE, slug: [HOME_SLUG] }
  }

  // let slug = decodeURIComponent(params?.slug[0]).split('/')
  let slug = null

  if (!pageSlugs?.length) {
    throw new Error('No page slugs found')
  }

  const isPageInSanity = pageSlugs?.includes(slugArrayToSlug(params?.slug))

  // If the page exists in Sanity, show it before going to templated pages
  if (isPageInSanity) {
    return { docType: DOC_TYPES.PAGE, slug: params?.slug }
  }

  // Recipe item
  if (params?.slug[0] === DIRECTORY_NAMES.RECIPES && params?.slug?.length === 2) {
    docType = DOC_TYPES.RECIPE
    slug = [params?.slug[1]]
  }

  // TV Episode item
  if (params?.slug[0] === DIRECTORY_NAMES.TV && params?.slug?.length === 2) {
    docType = DOC_TYPES.TV_EPISODE
    slug = [params?.slug[1]]
  }

  // Radio Episode
  if (params?.slug[0] === DIRECTORY_NAMES.RADIO && params?.slug?.length === 2) {
    docType = DOC_TYPES.RADIO_EPISODE
    slug = [params?.slug[1]]
  }

  // Magazine Issue
  if (params?.slug[0] === DIRECTORY_NAMES.MAGAZINE && params?.slug?.length === 2) {
    docType = DOC_TYPES.MAGAZINE
    slug = [params?.slug[1]]
  }

  // Article
  if (params?.slug[0] === DIRECTORY_NAMES.ARTICLE && params?.slug?.length === 2) {
    docType = DOC_TYPES.ARTICLE
    slug = [params?.slug[1]]
  }

  // Page section example
  if (params?.slug[0] === DIRECTORY_NAMES.PAGE_SECTION_EXAMPLE && params?.slug?.length === 2) {
    docType = DOC_TYPES.PAGE_SECTION_EXAMPLE
    slug = [params?.slug[1]]
  }

  return { docType, slug }
}

export async function generateStaticParams() {
  // return [{ slug: ['test', 'test-1'] }, { slug: ['stories'] }]
  /* eslint-disable  */

  const types = [
    DOC_TYPES.PAGE,
    DOC_TYPES.RECIPE,
    DOC_TYPES.ARTICLE,
    DOC_TYPES.TV_EPISODE,
    DOC_TYPES.RADIO_EPISODE,
    DOC_TYPES.MAGAZINE,
    DOC_TYPES.PAGE_SECTION_EXAMPLE,
  ]

  const result = await client.fetch(
    `*[_type in $types] {
      slug,
      _type
    }`,
    { types },
  )

  const pages = result.filter((item: any) => item._type === DOC_TYPES.PAGE)

  const recipes = result.filter((item: any) => item._type === DOC_TYPES.RECIPE)
  const articles = result.filter((item: any) => item._type === DOC_TYPES.ARTICLE)
  const magazines = result.filter((item: any) => item._type === DOC_TYPES.MAGAZINE)
  const pageSectionExamples = result.filter((item: any) => item._type === DOC_TYPES.PAGE_SECTION_EXAMPLE)
  const tvEpisodes = result.filter((item: any) => item._type === DOC_TYPES.TV_EPISODE)
  const radioEpisodes = result.filter((item: any) => item._type === DOC_TYPES.RADIO_EPISODE)
  const items: any[] = []
  const hashMap: any = {}

  // home
  items.push({
    slug: [],
  })

  pages.forEach((page: any) => {
    let slug = page.slug.current
    slug = decodeURIComponent(slug)
    slug = removeLeadingSlash(slug)

    if (!hashMap[slug]) {
      hashMap[slug] = true
      items.push({
        slug: slugPathToSlugArray(page.slug.current),
      })
    }
  })

  recipes.forEach((recipe: any) => {
    if (!recipe.slug.current) return
    const slugToPush = [DIRECTORY_NAMES.RECIPES, recipe.slug.current]
    const slugAsString = slugArrayToSlug(slugToPush)
    if (!hashMap[slugAsString]) {
      hashMap[slugAsString] = true
      items.push({
        slug: slugToPush,
      })
    }
  })

  articles.forEach((article: any) => {
    if (!article?.slug?.current) return
    const slugToPush = [DIRECTORY_NAMES.ARTICLE, article.slug.current]
    const slugAsString = slugArrayToSlug(slugToPush)
    if (!hashMap[slugAsString]) {
      hashMap[slugAsString] = true
      items.push({
        slug: slugToPush,
      })
    }
  })

  magazines.forEach((magazine: any) => {
    if (!magazine?.slug?.current) return
    const slugToPush = [DIRECTORY_NAMES.MAGAZINE, magazine.slug.current]
    const slugAsString = slugArrayToSlug(slugToPush)
    if (!hashMap[slugAsString]) {
      hashMap[slugAsString] = true
      items.push({
        slug: slugToPush,
      })
    }
  })

  pageSectionExamples.forEach((pageSectionExample: any) => {
    if (!pageSectionExample?.slug?.current) return
    const slugToPush = [DIRECTORY_NAMES.PAGE_SECTION_EXAMPLE, pageSectionExample.slug.current]
    const slugAsString = slugArrayToSlug(slugToPush)
    if (!hashMap[slugAsString]) {
      hashMap[slugAsString] = true
      items.push({
        slug: slugToPush,
      })
    }
  })

  tvEpisodes.forEach((tvEpisode: any) => {
    if (!tvEpisode?.slug?.current) return
    const slugToPush = [DIRECTORY_NAMES.TV, tvEpisode.slug.current]
    const slugAsString = slugArrayToSlug(slugToPush)
    if (!hashMap[slugAsString]) {
      hashMap[slugAsString] = true
      items.push({
        slug: slugToPush,
      })
    }
  })

  radioEpisodes.forEach((radioEpisode: any) => {
    if (!radioEpisode?.slug?.current) return
    const slugToPush = [DIRECTORY_NAMES.RADIO, radioEpisode.slug.current]
    const slugAsString = slugArrayToSlug(slugToPush)
    if (!hashMap[slugAsString]) {
      hashMap[slugAsString] = true
      items.push({
        slug: slugToPush,
      })
    }
  })

  if (!pages) {
    return []
  }

  return items

  /* eslint-enable  */
}

export const generateMetadata = async ({ params }: { params: Promise<{ slug: string[] }> }): Promise<Metadata> => {
  const paramsData = await params
  const { docType, slug } = await getDocTypeAndSlugFromParams(paramsData)
  return await getPageMetadata(docType as SanityPageType, slug as string[])
}

export default async function Page({ params }: NextPageParams) {
  const paramsData = await params
  const { isEnabled } = await draftMode()
  const { docType, slug } = await getDocTypeAndSlugFromParams(paramsData)

  return (
    <PageHandler
      slug={slug as string[]}
      docType={docType as 'page'}
      isDraftMode={isEnabled}
    />
  )
}
